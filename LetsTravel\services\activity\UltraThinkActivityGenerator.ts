/**
 * 🎯 Ultra Think 活动生成器
 * Phase C: UI和体验优化 - 集成增强活动生成器
 * 基于Ultra Think解决方案，提供智能、多样化的活动生成
 */

import { ultraThinkConfig } from '../../config/UltraThinkConfig';
import { ultraThinkLLMManager } from '../ai/UltraThinkLLMManager';
import { ultraThinkAPIManager } from '../api/UltraThinkAPIManager';
import { IntelligentFallbackGenerator } from '../data/IntelligentFallbackGenerator';
import { TrulyUnifiedBudgetEngine } from '../../utils/TrulyUnifiedBudgetEngine';
import { BudgetConstraintValidator, type BudgetConstraint } from '../budget/BudgetConstraintValidator';
import { ActivityContentEnhancer } from '../quality/ActivityContentEnhancer';
import { UltraThinkMasterSolver } from '../ultra-think/UltraThinkMasterSolver';
import { ultraThinkV3, UltraThinkV3Request } from '../ultra-think/UltraThinkV3';
import { JSONDataConverter } from '../converters/JSONDataConverter';
import { pointToPointTransportGenerator } from '../transport/PointToPointTransportGenerator';
import { activityClassificationSystem } from '../classification/ActivityClassificationSystem';
import { mealPlanningSystem } from '../dining/MealPlanningSystem';
import { ComprehensiveActivityGenerator } from '../../solutions/EnhancedActivityGenerator';

// ===== 接口定义 =====

export interface UltraThinkActivityRequest {
  destination: string;
  duration: number;
  travelers: number;
  budget: number;
  currency: string;
  startDate: Date;
  endDate?: Date; // 添加可选的结束日期
  preferences: {
    travelStyle: string[];
    accommodation: string[];
    transport: string[];
    interests: string[];
  };
  options?: {
    minActivitiesPerDay?: number;
    maxActivitiesPerDay?: number;
    includeTransport?: boolean;
    includeAccommodation?: boolean;
    includeFlights?: boolean;
    includeMeals?: boolean;
    enableSeasonalOptimization?: boolean;
  };
}

export interface UltraThinkActivity {
  id: string;
  name: string;
  name_zh: string;
  type: 'attraction' | 'transport' | 'accommodation' | 'flight' | 'meal' | 'shopping';
  category: string;
  description: string;
  description_zh: string;
  location: {
    name: string;
    address: string;
    coordinates?: { lat: number; lng: number };
  };
  timing: {
    startTime: string;
    endTime: string;
    duration: number; // 分钟
    date: string;
    day: number;
  };
  cost: {
    amount: number;
    currency: string;
    priceLevel: 'free' | 'budget' | 'mid_range' | 'luxury';
  };
  details: {
    rating?: number;
    reviews?: number;
    highlights: string[];
    tips: string[];
    bookingRequired: boolean;
    seasonalInfo?: string;
  };
  metadata: {
    source: 'api' | 'template' | 'ai_generated';
    confidence: number;
    qualityScore: number;
    lastUpdated: string;
  };
}

export interface UltraThinkActivityResponse {
  success: boolean;
  activities: UltraThinkActivity[];
  summary: {
    totalActivities: number;
    activitiesByType: Record<string, number>;
    activitiesByDay: Record<number, number>;
    totalCost: number;
    averageRating: number;
  };
  optimization: {
    timeEfficiency: number;
    budgetUtilization: number;
    diversityScore: number;
    seasonalRelevance: number;
  };
  executionTime: number;
  fallbackUsed: boolean;
  error?: string;
}

// ===== Ultra Think 活动生成器 =====

export class UltraThinkActivityGenerator {
  private static instance: UltraThinkActivityGenerator;
  private config = ultraThinkConfig.getConfig();
  private comprehensiveGenerator: ComprehensiveActivityGenerator;
  private fallbackGenerator: IntelligentFallbackGenerator;
  private isInitialized: boolean = false;

  private constructor() {
    this.comprehensiveGenerator = new ComprehensiveActivityGenerator();
    this.fallbackGenerator = new IntelligentFallbackGenerator();
  }

  static getInstance(): UltraThinkActivityGenerator {
    if (!UltraThinkActivityGenerator.instance) {
      UltraThinkActivityGenerator.instance = new UltraThinkActivityGenerator();
    }
    return UltraThinkActivityGenerator.instance;
  }

  /**
   * 🚀 初始化活动生成器
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    console.log('🎯 初始化Ultra Think活动生成器...');

    try {
      // 初始化依赖服务
      await ultraThinkLLMManager.initialize?.();
      await ultraThinkAPIManager.initialize();

      this.isInitialized = true;
      console.log('✅ Ultra Think活动生成器初始化完成');
    } catch (error) {
      console.error('❌ Ultra Think活动生成器初始化失败:', error);
      throw error;
    }
  }

  /**
   * 🎯 生成完整活动列表
   */
  async generateActivities(request: UltraThinkActivityRequest): Promise<UltraThinkActivityResponse> {
    const startTime = Date.now();
    console.log('🎯🎯🎯 Ultra Think Master Solver 活动生成器启动 🎯🎯🎯');
    console.log('🚀 目的地:', request.destination);
    console.log('📊 预算:', request.budget);
    console.log('⏱️ 持续时间:', request.duration);

    try {
      // 🚀 使用Ultra Think V3.0 - 全新智能旅行系统
      console.log('🚀 启用Ultra Think V3.0 - 完整重构的智能系统');
      console.log('🔍 检查Ultra Think V3.0导入:', typeof ultraThinkV3);

      const ultraThinkV3Request: UltraThinkV3Request = {
        destination: request.destination,
        duration: request.duration,
        budget: request.budget || 8000,
        currency: request.currency || 'MYR',
        travelers: request.travelers || 2,
        startDate: request.startDate || new Date(),
        endDate: request.endDate,
        preferences: {
          travelStyle: request.preferences?.travelStyle || ['cultural'],
          accommodation: request.preferences?.accommodation || ['mid_range'],
          transport: request.preferences?.transport || ['public'],
          interests: request.preferences?.interests || ['sightseeing']
        },
        options: {
          minActivitiesPerDay: request.options?.minActivitiesPerDay || 3,
          maxActivitiesPerDay: request.options?.maxActivitiesPerDay || 6,
          includeTransport: request.options?.includeTransport !== false,
          includeAccommodation: request.options?.includeAccommodation !== false,
          includeFlights: request.options?.includeFlights !== false,
          includeMeals: request.options?.includeMeals !== false,
          enableSeasonalOptimization: request.options?.enableSeasonalOptimization !== false,
          enableSurprises: true,
          qualityLevel: 'premium'
        }
      };

      const ultraThinkV3Result = await ultraThinkV3.generateJourney(ultraThinkV3Request);

      if (ultraThinkV3Result.success) {
        console.log('✅ Ultra Think V3.0成功生成高质量行程');
        console.log('🎊 惊喜体验数量:', ultraThinkV3Result.surprises?.length || 0);
        console.log('📊 质量评分:', ultraThinkV3Result.optimization.qualityScore);
        console.log('🔧 使用组件:', ultraThinkV3Result.metadata.componentsUsed);

        // 🔄 转换JSON数据为前端组件格式
        const componentProps = JSONDataConverter.convertToComponentProps(ultraThinkV3Result.journeyData);

        // 🔄 转换为传统响应格式（保持兼容性）
        const response: UltraThinkActivityResponse = {
          success: true,
          activities: this.convertV3ToLegacyActivities(ultraThinkV3Result),
          summary: this.convertV3ToLegacySummary(ultraThinkV3Result),
          optimization: {
            timeEfficiency: ultraThinkV3Result.optimization.timeEfficiency,
            budgetOptimization: ultraThinkV3Result.optimization.budgetOptimization,
            geographicalOptimization: 0.9,
            overallScore: ultraThinkV3Result.optimization.qualityScore
          },
          executionTime: ultraThinkV3Result.metadata.generationTime,
          fallbackUsed: ultraThinkV3Result.metadata.fallbackUsed,
          metadata: {
            solver: 'ultra_think_v3',
            version: ultraThinkV3Result.metadata.version,
            componentsUsed: ultraThinkV3Result.metadata.componentsUsed,
            surpriseCount: ultraThinkV3Result.surprises?.length || 0,
            fixes: ultraThinkV3Result.fixes || [],
            warnings: ultraThinkV3Result.warnings || [],
            qualityScore: ultraThinkV3Result.optimization.qualityScore,
            jsonData: ultraThinkV3Result.journeyData,
            componentProps: componentProps
          }
        };

        console.log(`🎉 Ultra Think V3.0完成: ${response.activities.length}个活动, 质量分${ultraThinkV3Result.optimization.qualityScore.toFixed(2)}`);
        return response;
      } else {
        console.warn('⚠️ Ultra Think V3.0失败，降级到V1');
        throw new Error('Ultra Think V3.0执行失败');
      }

    } catch (error) {
      console.error('❌ Ultra Think V3.0失败，尝试V1:', error);

      // 降级到V1
      return await this.tryMasterSolverV1(request, startTime);
    }
  }

  /**
   * 🔄 尝试Master Solver V1（降级）
   */
  private async tryMasterSolverV1(request: UltraThinkActivityRequest, startTime: number): Promise<UltraThinkActivityResponse> {
    try {
      console.log('🔄 降级到Master Solver V1');

      const masterSolverRequest = {
        destination: request.destination,
        duration: request.duration,
        budget: request.budget || 8000,
        currency: request.currency || 'MYR',
        travelers: request.travelers || 2,
        startDate: request.startDate || new Date(),
        preferences: request.preferences || {}
      };

      const masterResult = await UltraThinkMasterSolver.solveMasterProblems(masterSolverRequest);

      if (masterResult.success) {
        console.log('✅ Master Solver V1成功');

        const response: UltraThinkActivityResponse = {
          success: true,
          activities: masterResult.activities,
          summary: masterResult.summary,
          optimization: {
            timeEfficiency: 0.85,
            budgetOptimization: 0.85,
            geographicalOptimization: 0.8,
            overallScore: masterResult.qualityScore
          },
          executionTime: masterResult.executionTime,
          fallbackUsed: false,
          metadata: {
            solver: 'ultra_think_master_solver_v1',
            issues: masterResult.issues,
            fixes: masterResult.fixes,
            qualityScore: masterResult.qualityScore
          }
        };

        return response;
      } else {
        throw new Error('Master Solver V1执行失败');
      }

    } catch (error) {
      console.error('❌ Master Solver V1也失败，降级到传统方法:', error);
      return await this.generateTraditionalActivities(request, startTime);
    }
  }

  /**
   * 🔄 JSON数据转换为传统活动格式
   */
  private convertJSONToLegacyActivities(journeyJSON: any): any[] {
    console.log('🔄 转换JSON数据为传统活动格式');

    const activities: any[] = [];

    if (journeyJSON.payload?.dayPlans) {
      journeyJSON.payload.dayPlans.forEach((dayPlan: any) => {
        // 添加活动
        dayPlan.activities?.forEach((activity: any) => {
          activities.push({
            id: activity.id,
            name: activity.name,
            title: activity.name,  // 🔧 确保title字段存在
            type: activity.type,
            timing: {
              day: dayPlan.dayNumber,
              startTime: activity.timing.startTime,
              endTime: activity.timing.endTime,
              duration: activity.timing.duration
            },
            location: activity.location,
            cost: activity.cost,
            description: activity.description,
            highlights: activity.highlights,
            expandedContent: activity.expandedContent,
            metadata: activity.metadata
          });
        });

        // 添加餐饮
        dayPlan.meals?.forEach((meal: any) => {
          activities.push({
            id: meal.id,
            name: meal.name,
            title: meal.name,
            type: 'restaurant',
            timing: {
              day: dayPlan.dayNumber,
              startTime: meal.time,
              endTime: this.addMinutes(meal.time, meal.duration),
              duration: meal.duration
            },
            location: meal.location,
            cost: meal.cost,
            description: meal.description,
            cuisine: meal.cuisine,
            specialties: meal.specialties
          });
        });

        // 添加交通
        dayPlan.transportation?.forEach((transport: any) => {
          activities.push({
            id: transport.id,
            name: `前往${transport.to.name}`,
            title: `前往${transport.to.name}`,
            type: 'transport',
            timing: {
              day: dayPlan.dayNumber,
              startTime: transport.departureTime,
              endTime: transport.arrivalTime,
              duration: transport.duration
            },
            from: transport.from,
            to: transport.to,
            cost: transport.cost,
            transportType: transport.type,
            instructions: transport.details.instructions
          });
        });
      });
    }

    console.log(`✅ 转换完成: ${activities.length}个活动项目`);
    return activities;
  }

  /**
   * 🔄 JSON数据转换为传统摘要格式
   */
  private convertJSONToLegacySummary(journeyJSON: any): any {
    const payload = journeyJSON.payload;

    return {
      totalActivities: payload.dayPlans?.reduce((sum: number, day: any) => sum + (day.activities?.length || 0), 0) || 0,
      totalCost: payload.budget?.total?.amount || 0,
      currency: payload.budget?.total?.currency || 'MYR',
      duration: payload.journey?.duration || 0,
      destination: payload.journey?.destination || '',
      budget: payload.budget,
      highlights: payload.dayPlans?.flatMap((day: any) => day.summary?.highlights || []).slice(0, 5) || [],
      recommendations: payload.budget?.recommendations || []
    };
  }

  /**
   * 🔧 时间计算辅助方法
   */
  private addMinutes(time: string, minutes: number): string {
    const [hours, mins] = time.split(':').map(Number);
    const totalMinutes = hours * 60 + mins + minutes;
    const newHours = Math.floor(totalMinutes / 60) % 24;
    const newMins = totalMinutes % 60;
    return `${newHours.toString().padStart(2, '0')}:${newMins.toString().padStart(2, '0')}`;
  }

  /**
   * 🔄 传统活动生成方法（最终降级）
   */
  private async generateTraditionalActivities(request: UltraThinkActivityRequest, startTime: number): Promise<UltraThinkActivityResponse> {
    try {
      console.log('🔄 使用传统方法生成活动');

      // 🔧 修复：计算正确的日期范围
      const dateRange = this.calculateDateRange(request);
      const enhancedRequest = {
        ...request,
        endDate: dateRange.endDate,
        dateRange: dateRange
      };

      console.log('📅 日期范围计算:', {
        startDate: dateRange.startDate.toDateString(),
        endDate: dateRange.endDate.toDateString(),
        duration: dateRange.duration,
        days: dateRange.days.length
      });

      // 1. 生成基础活动框架
      const baseActivities = await this.generateBaseActivities(enhancedRequest);

      // 2. 增强活动详情
      const enhancedActivities = await this.enhanceActivities(baseActivities, enhancedRequest);

      // 3. 优化活动安排
      const optimizedActivities = await this.optimizeActivities(enhancedActivities, enhancedRequest);

      // 4. 计算总结和优化指标
      const summary = this.calculateSummary(optimizedActivities);
      const optimization = this.calculateOptimization(optimizedActivities, enhancedRequest);

      // 5. 计算预算 - 使用统一预算引擎
      const budgetBreakdown = optimizedActivities.map(activity => 
        TrulyUnifiedBudgetEngine.getUnifiedActivityBudget(activity)
      );

      const response: UltraThinkActivityResponse = {
        success: true,
        activities: optimizedActivities,
        summary: {
          ...summary,
          budget: budgetBreakdown
        },
        optimization,
        executionTime: Date.now() - startTime,
        fallbackUsed: true
      };

      console.log(`✅ 传统方法生成完成: ${optimizedActivities.length}个活动`);
      return response;

    } catch (error) {
      console.error('❌ 传统方法也失败:', error);

      // 最终降级
      return await this.generateFallbackActivities(request, startTime, error);
    }
  }

  /**
   * 📅 计算日期范围
   */
  private calculateDateRange(request: UltraThinkActivityRequest): {
    startDate: Date;
    endDate: Date;
    duration: number;
    days: Date[];
  } {
    const startDate = new Date(request.startDate);

    // 如果提供了endDate，使用它；否则根据duration计算
    let endDate: Date;
    if (request.endDate) {
      endDate = new Date(request.endDate);
    } else {
      endDate = new Date(startDate);
      endDate.setDate(startDate.getDate() + request.duration - 1);
    }

    // 生成每一天的日期
    const days: Date[] = [];
    const currentDate = new Date(startDate);
    while (currentDate <= endDate) {
      days.push(new Date(currentDate));
      currentDate.setDate(currentDate.getDate() + 1);
    }

    return {
      startDate,
      endDate,
      duration: request.duration,
      days
    };
  }

  /**
   * 🏗️ 生成基础活动框架
   */
  private async generateBaseActivities(request: UltraThinkActivityRequest): Promise<UltraThinkActivity[]> {
    console.log('🏗️ 生成基础活动框架...');

    const activities: UltraThinkActivity[] = [];
    const { destination, duration, preferences, options } = request;

    // 计算每天活动数量
    const minPerDay = options?.minActivitiesPerDay || 3;
    const maxPerDay = options?.maxActivitiesPerDay || 6;
    const targetPerDay = Math.min(maxPerDay, Math.max(minPerDay, Math.ceil(8 / duration)));

    // 🔧 修复：计算活动分布
    const distribution = activityClassificationSystem.calculateActivityDistribution(preferences);
    console.log('📊 活动分布计算:', distribution);

    // 🍽️ 生成餐饮计划
    if (options?.includeMeals !== false) {
      const dateRange = (request as any).dateRange;
      if (dateRange?.days) {
        console.log('🍽️ 生成餐饮计划...');
        const mealPlans = mealPlanningSystem.generateMealPlan(
          dateRange.days,
          destination,
          preferences,
          []
        );

        // 将餐饮活动添加到活动列表
        mealPlans.forEach(plan => {
          activities.push(plan.breakfast as any);
          activities.push(plan.lunch as any);
          activities.push(plan.dinner as any);
          if (plan.snacks) {
            activities.push(...(plan.snacks as any[]));
          }
        });

        console.log(`✅ 生成了${mealPlans.length}天的餐饮计划`);
      }
    }

    // 为每一天生成活动
    for (let day = 1; day <= duration; day++) {
      const dayActivities = await this.generateDayActivities(
        request,
        day,
        targetPerDay
      );
      activities.push(...dayActivities);
    }

    // 添加交通、住宿、航班等
    if (options?.includeFlights) {
      const flightActivities = await this.generateFlightActivities(request);
      activities.push(...flightActivities);
    }

    if (options?.includeAccommodation) {
      const accommodationActivities = await this.generateAccommodationActivities(request);
      activities.push(...accommodationActivities);
    }

    if (options?.includeTransport) {
      const transportActivities = await this.generateTransportActivities(request);
      activities.push(...transportActivities);
    }

    console.log(`✅ 基础活动框架生成完成: ${activities.length}个活动`);
    return activities;
  }

  /**
   * 📅 生成单日活动
   */
  private async generateDayActivities(
    request: UltraThinkActivityRequest,
    day: number,
    targetCount: number
  ): Promise<UltraThinkActivity[]> {
    const activities: UltraThinkActivity[] = [];
    const currentDate = new Date(request.startDate);
    currentDate.setDate(currentDate.getDate() + day - 1);

    try {
      // 使用API搜索真实景点
      const apiResponse = await ultraThinkAPIManager.callAPI({
        type: 'places',
        params: {
          query: `${request.destination} 景点 旅游`,
          location: request.destination
        }
      });

      if (apiResponse.success && apiResponse.data.local_results) {
        const places = apiResponse.data.local_results.slice(0, targetCount);
        
        places.forEach((place: any, index: number) => {
          const startHour = 9 + (index * 2); // 从9点开始，每2小时一个活动
          const activity = this.createActivityFromPlace(place, day, startHour, currentDate, request);
          activities.push(activity);
        });
      }

      // 如果API数据不足，使用模板补充
      while (activities.length < targetCount) {
        const templateActivity = this.createTemplateActivity(
          request.destination,
          day,
          activities.length + 1,
          currentDate,
          request
        );
        activities.push(templateActivity);
      }

    } catch (error) {
      console.warn(`⚠️ Day ${day} API调用失败，使用模板:`, error);
      
      // 完全使用模板生成
      for (let i = 0; i < targetCount; i++) {
        const templateActivity = this.createTemplateActivity(
          request.destination,
          day,
          i + 1,
          currentDate,
          request
        );
        activities.push(templateActivity);
      }
    }

    return activities;
  }

  /**
   * 🏛️ 从API地点数据创建活动
   */
  private createActivityFromPlace(
    place: any,
    day: number,
    startHour: number,
    date: Date,
    request: UltraThinkActivityRequest
  ): UltraThinkActivity {
    // 🔧 修复：使用智能时间计算而非硬编码
    const activityType = this.categorizePlace(place.type);
    const intelligentDuration = this.calculateIntelligentDuration(place, activityType);
    
    // 临时时间（将在optimizeTimeSchedule中被重新计算）
    const tempStartTime = `${startHour.toString().padStart(2, '0')}:00`;
    const tempEndTime = this.calculateTempEndTime(startHour, intelligentDuration);

    return {
      id: `activity_${day}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      name: place.title || `${request.destination}景点`,
      name_zh: place.title || `${request.destination}景点`,
      type: 'attraction',
      category: activityType,
      description: place.description || `探索${place.title}的独特魅力`,
      description_zh: place.description || `探索${place.title}的独特魅力`,
      location: {
        name: place.title,
        address: place.address || `${request.destination}市内`,
        coordinates: place.gps_coordinates
      },
      timing: {
        startTime: tempStartTime,
        endTime: tempEndTime,
        duration: intelligentDuration,
        date: date.toISOString().split('T')[0],
        day
      },
      duration: intelligentDuration, // 使用智能计算的持续时间
      startTime: tempStartTime,      // 临时时间，将被优化
      endTime: tempEndTime,          // 临时时间，将被优化
      cost: {
        amount: this.estimateCost(place.type, request.budget),
        currency: request.currency,
        priceLevel: this.determinePriceLevel(place.type)
      },
      details: {
        rating: place.rating || 4.0,
        reviews: place.reviews || 100,
        highlights: [place.description || '精彩体验'],
        tips: ['建议提前预订', '注意开放时间'],
        bookingRequired: place.type === 'tourist_attraction'
      },
      metadata: {
        source: 'api',
        confidence: 0.9,
        qualityScore: 0.85,
        lastUpdated: new Date().toISOString(),
        intelligentTimeCalculated: true
      }
    };
  }

  /**
   * 🧮 智能计算活动持续时间
   */
  private calculateIntelligentDuration(place: any, activityType: string): number {
    // 基础持续时间配置
    const baseDurations = {
      'cultural_site': 90,
      'tourist_attraction': 105,
      'museum': 120,
      'temple': 75,
      'park': 60,
      'shopping': 90,
      'restaurant': 75,
      'entertainment': 120
    };
    
    let baseDuration = baseDurations[activityType] || 90;
    
    // 基于评分调整时间
    if (place.rating) {
      if (place.rating >= 4.5) {
        baseDuration *= 1.2; // 高评分景点值得更多时间
      } else if (place.rating < 3.5) {
        baseDuration *= 0.8; // 低评分景点减少时间
      }
    }
    
    // 基于描述长度调整（更详细的描述通常意味着更丰富的内容）
    if (place.description && place.description.length > 100) {
      baseDuration *= 1.1;
    }
    
    // 确保在合理范围内
    const minDuration = 30;
    const maxDuration = 180;
    
    return Math.max(minDuration, Math.min(maxDuration, Math.round(baseDuration)));
  }

  /**
   * ⏰ 计算临时结束时间
   */
  private calculateTempEndTime(startHour: number, duration: number): string {
    const startMinutes = startHour * 60;
    const endMinutes = startMinutes + duration;
    const endHour = Math.floor(endMinutes / 60);
    const endMin = endMinutes % 60;
    
    return `${endHour.toString().padStart(2, '0')}:${endMin.toString().padStart(2, '0')}`;
  }

  /**
   * 📝 创建模板活动
   */
  private createTemplateActivity(
    destination: string,
    day: number,
    index: number,
    date: Date,
    request: UltraThinkActivityRequest
  ): UltraThinkActivity {
    const templates = this.getActivityTemplates(destination);
    const template = templates[index % templates.length];
    const startHour = 9 + (index * 2);
    const startTime = `${startHour.toString().padStart(2, '0')}:00`;
    const endTime = `${(startHour + 2).toString().padStart(2, '0')}:00`;

    return {
      id: `template_${day}_${index}_${Date.now()}`,
      name: template.name.replace('{destination}', destination),
      name_zh: template.name_zh.replace('{destination}', destination),
      type: 'attraction',
      category: template.category,
      description: template.description.replace('{destination}', destination),
      description_zh: template.description_zh.replace('{destination}', destination),
      location: {
        name: template.name.replace('{destination}', destination),
        address: `${destination}市内`,
      },
      timing: {
        startTime,
        endTime,
        duration: template.duration,
        date: date.toISOString().split('T')[0],
        day
      },
      duration: template.duration, // 添加顶级duration字段
      startTime, // 添加顶级startTime字段
      endTime,   // 添加顶级endTime字段
      cost: {
        amount: template.cost,
        currency: request.currency,
        priceLevel: template.priceLevel
      },
      details: {
        rating: 4.2,
        reviews: 150,
        highlights: template.highlights,
        tips: template.tips,
        bookingRequired: template.bookingRequired
      },
      metadata: {
        source: 'template',
        confidence: 0.7,
        qualityScore: 0.75,
        lastUpdated: new Date().toISOString()
      }
    };
  }

  /**
   * 🔧 工具方法
   */
  private categorizePlace(type: string): string {
    const categoryMap: Record<string, string> = {
      'tourist_attraction': 'cultural',
      'museum': 'cultural',
      'temple': 'cultural',
      'park': 'nature',
      'shopping_mall': 'shopping',
      'restaurant': 'food',
      'hotel': 'accommodation'
    };
    return categoryMap[type] || 'general';
  }

  private estimateCost(type: string, totalBudget: number): number {
    const costRatios: Record<string, number> = {
      'tourist_attraction': 0.02,
      'museum': 0.015,
      'temple': 0.005,
      'park': 0.001,
      'shopping_mall': 0.03,
      'restaurant': 0.025
    };
    const ratio = costRatios[type] || 0.02;
    return Math.round(totalBudget * ratio);
  }

  private determinePriceLevel(type: string): 'free' | 'budget' | 'mid_range' | 'luxury' {
    const freePlaces = ['park', 'temple', 'beach'];
    const budgetPlaces = ['museum', 'market'];
    const luxuryPlaces = ['fine_dining', 'luxury_hotel'];
    
    if (freePlaces.includes(type)) return 'free';
    if (budgetPlaces.includes(type)) return 'budget';
    if (luxuryPlaces.includes(type)) return 'luxury';
    return 'mid_range';
  }

  private getActivityTemplates(destination: string) {
    return [
      {
        name: '{destination}文化中心',
        name_zh: '{destination}文化中心',
        category: 'cultural',
        description: 'Explore the rich cultural heritage of {destination}',
        description_zh: '探索{destination}丰富的文化遗产',
        duration: 120,
        cost: 50,
        priceLevel: 'mid_range' as const,
        highlights: ['历史展览', '文化体验', '导览服务'],
        tips: ['建议预约导览', '注意拍照规定'],
        bookingRequired: true
      },
      {
        name: '{destination}市场',
        name_zh: '{destination}市场',
        category: 'shopping',
        description: 'Experience local life at {destination} market',
        description_zh: '在{destination}市场体验当地生活',
        duration: 90,
        cost: 30,
        priceLevel: 'budget' as const,
        highlights: ['当地特产', '美食小吃', '手工艺品'],
        tips: ['带现金', '可以讨价还价'],
        bookingRequired: false
      },
      {
        name: '{destination}公园',
        name_zh: '{destination}公园',
        category: 'nature',
        description: 'Relax and enjoy nature in {destination} park',
        description_zh: '在{destination}公园放松享受自然',
        duration: 60,
        cost: 0,
        priceLevel: 'free' as const,
        highlights: ['自然风光', '休闲散步', '拍照留念'],
        tips: ['适合早晨或傍晚', '注意天气'],
        bookingRequired: false
      },
      {
        name: '{destination}博物馆',
        name_zh: '{destination}博物馆',
        category: 'museum',
        description: 'Discover history and art at {destination} museum',
        description_zh: '在{destination}博物馆探索历史与艺术',
        duration: 150,
        cost: 25,
        priceLevel: 'budget' as const,
        highlights: ['历史文物', '艺术展品', '互动体验'],
        tips: ['周末人多', '有学生折扣'],
        bookingRequired: false
      },
      {
        name: '{destination}美食街',
        name_zh: '{destination}美食街',
        category: 'dining',
        description: 'Taste authentic local cuisine in {destination}',
        description_zh: '在{destination}品尝正宗当地美食',
        duration: 90,
        cost: 40,
        priceLevel: 'budget' as const,
        highlights: ['当地特色菜', '街头小吃', '传统口味'],
        tips: ['尝试招牌菜', '注意食品安全'],
        bookingRequired: false
      },
      {
        name: '{destination}观景台',
        name_zh: '{destination}观景台',
        category: 'landmark',
        description: 'Enjoy panoramic views of {destination}',
        description_zh: '在{destination}观景台欣赏全景',
        duration: 90,
        cost: 15,
        priceLevel: 'budget' as const,
        highlights: ['城市全景', '拍照胜地', '日落美景'],
        tips: ['黄昏时分最美', '带好相机'],
        bookingRequired: false
      },
      {
        name: '{destination}购物区',
        name_zh: '{destination}购物区',
        category: 'shopping',
        description: 'Shop for souvenirs and local products in {destination}',
        description_zh: '在{destination}购买纪念品和当地产品',
        duration: 120,
        cost: 80,
        priceLevel: 'mid_range' as const,
        highlights: ['当地特产', '纪念品', '时尚商品'],
        tips: ['比较价格', '保留收据'],
        bookingRequired: false
      },
      {
        name: '{destination}寺庙',
        name_zh: '{destination}寺庙',
        category: 'cultural',
        description: 'Experience spiritual culture at {destination} temple',
        description_zh: '在{destination}寺庙体验精神文化',
        duration: 75,
        cost: 5,
        priceLevel: 'free' as const,
        highlights: ['宗教建筑', '文化体验', '宁静氛围'],
        tips: ['穿着得体', '保持安静'],
        bookingRequired: false
      }
    ];
  }

  /**
   * ✈️ 生成航班活动
   */
  private async generateFlightActivities(request: UltraThinkActivityRequest): Promise<UltraThinkActivity[]> {
    const activities: UltraThinkActivity[] = [];

    try {
      const flightResponse = await ultraThinkAPIManager.callAPI({
        type: 'flights',
        params: {
          origin: '吉隆坡', // 可以从用户配置获取
          destination: request.destination,
          departureDate: request.startDate.toISOString().split('T')[0],
          passengers: request.travelers
        }
      });

      if (flightResponse.success && flightResponse.data.length > 0) {
        const flight = flightResponse.data[0]; // 选择第一个航班

        activities.push({
          id: `flight_outbound_${Date.now()}`,
          name: `Flight to ${request.destination}`,
          name_zh: `飞往${request.destination}`,
          type: 'flight',
          category: 'transport',
          description: `${flight.airline} ${flight.flightNumber}`,
          description_zh: `${flight.airline} ${flight.flightNumber}`,
          location: {
            name: flight.departure.airport,
            address: flight.departure.airport
          },
          timing: {
            startTime: flight.departure.time,
            endTime: flight.arrival.time,
            duration: parseInt(flight.duration) || 480,
            date: request.startDate.toISOString().split('T')[0],
            day: 1
          },
          duration: parseInt(flight.duration) || 480, // 添加顶级duration字段
          startTime: flight.departure.time, // 添加顶级startTime字段
          endTime: flight.arrival.time,     // 添加顶级endTime字段
          cost: {
            amount: flight.price.amount,
            currency: flight.price.currency,
            priceLevel: 'mid_range'
          },
          details: {
            highlights: ['准时起飞', '舒适座椅', '机上服务'],
            tips: ['提前2小时到达机场', '检查行李限制'],
            bookingRequired: true
          },
          metadata: {
            source: 'api',
            confidence: 0.95,
            qualityScore: 0.9,
            lastUpdated: new Date().toISOString()
          }
        });
      }
    } catch (error) {
      console.warn('⚠️ 航班活动生成失败，使用模板:', error);
      // 使用模板生成航班活动
      activities.push(this.createTemplateFlightActivity(request));
    }

    return activities;
  }

  /**
   * 🏨 生成住宿活动
   */
  private async generateAccommodationActivities(request: UltraThinkActivityRequest): Promise<UltraThinkActivity[]> {
    const activities: UltraThinkActivity[] = [];

    try {
      const hotelResponse = await ultraThinkAPIManager.callAPI({
        type: 'hotels',
        params: {
          destination: request.destination,
          checkIn: request.startDate.toISOString().split('T')[0],
          checkOut: new Date(request.startDate.getTime() + request.duration * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          guests: request.travelers
        }
      });

      if (hotelResponse.success && hotelResponse.data.length > 0) {
        const hotel = hotelResponse.data[0]; // 选择第一个酒店

        activities.push({
          id: `accommodation_${Date.now()}`,
          name: hotel.name,
          name_zh: hotel.name,
          type: 'accommodation',
          category: 'accommodation',
          description: `${hotel.rating}星级酒店，提供优质住宿服务`,
          description_zh: `${hotel.rating}星级酒店，提供优质住宿服务`,
          location: {
            name: hotel.name,
            address: hotel.address || `${request.destination}市内`
          },
          timing: {
            startTime: '15:00',
            endTime: '11:00',
            duration: request.duration * 24 * 60, // 总住宿时间
            date: request.startDate.toISOString().split('T')[0],
            day: 1
          },
          cost: {
            amount: hotel.price.amount * request.duration,
            currency: hotel.price.currency,
            priceLevel: 'mid_range'
          },
          details: {
            rating: hotel.rating,
            highlights: hotel.amenities || ['免费WiFi', '早餐', '24小时前台'],
            tips: ['提前办理入住', '了解酒店设施'],
            bookingRequired: true
          },
          metadata: {
            source: 'api',
            confidence: 0.9,
            qualityScore: 0.85,
            lastUpdated: new Date().toISOString()
          }
        });
      }
    } catch (error) {
      console.warn('⚠️ 住宿活动生成失败，使用模板:', error);
      activities.push(this.createTemplateAccommodationActivity(request));
    }

    return activities;
  }

  /**
   * 🚗 生成交通活动
   */
  private async generateTransportActivities(request: UltraThinkActivityRequest): Promise<UltraThinkActivity[]> {
    const activities: UltraThinkActivity[] = [];

    // 为每天生成一个交通活动（城市内交通）
    for (let day = 1; day <= request.duration; day++) {
      const currentDate = new Date(request.startDate);
      currentDate.setDate(currentDate.getDate() + day - 1);

      activities.push({
        id: `transport_day${day}_${Date.now()}`,
        name: `${request.destination} Daily Transport`,
        name_zh: `${request.destination}日常交通`,
        type: 'transport',
        category: 'transport',
        description: `Day ${day} transportation in ${request.destination}`,
        description_zh: `第${day}天在${request.destination}的交通`,
        location: {
          name: `${request.destination}市内`,
          address: `${request.destination}市内`
        },
        timing: {
          startTime: '08:00',
          endTime: '20:00',
          duration: 60, // 平均每天1小时交通时间
          date: currentDate.toISOString().split('T')[0],
          day
        },
        duration: 60, // 添加顶级duration字段
        startTime: '08:00', // 添加顶级startTime字段
        endTime: '20:00',   // 添加顶级endTime字段
        cost: {
          amount: 20, // 每天交通费用
          currency: request.currency,
          priceLevel: 'budget'
        },
        details: {
          highlights: ['地铁', '公交', '出租车'],
          tips: ['购买交通卡', '避开高峰期'],
          bookingRequired: false
        },
        metadata: {
          source: 'template',
          confidence: 0.8,
          qualityScore: 0.7,
          lastUpdated: new Date().toISOString()
        }
      });
    }

    return activities;
  }

  /**
   * 🔧 增强活动详情
   */
  private async enhanceActivities(
    activities: UltraThinkActivity[],
    request: UltraThinkActivityRequest
  ): Promise<UltraThinkActivity[]> {
    console.log('🔧 增强活动详情...');

    // 使用LLM增强活动描述和建议
    for (const activity of activities) {
      if (activity.metadata.source === 'template') {
        try {
          const enhancementPrompt = `请为以下旅行活动提供更详细的描述和实用建议：
活动名称：${activity.name_zh}
目的地：${request.destination}
活动类型：${activity.category}
请提供：1. 详细描述 2. 3个亮点 3. 2个实用建议`;

          const llmResponse = await ultraThinkLLMManager.callLLM({
            prompt: enhancementPrompt,
            taskType: 'attraction',
            context: {
              destination: request.destination,
              complexity: 'low'
            }
          });

          if (llmResponse.success) {
            // 解析LLM响应并更新活动详情
            activity.description_zh = this.extractDescription(llmResponse.content);
            activity.details.highlights = this.extractHighlights(llmResponse.content);
            activity.details.tips = this.extractTips(llmResponse.content);
            activity.metadata.qualityScore = 0.85; // 提升质量评分
          }
        } catch (error) {
          console.warn(`⚠️ 活动增强失败 (${activity.name}):`, error);
        }
      }
    }

    console.log('✅ 活动详情增强完成');
    return activities;
  }

  /**
   * ⚡ 优化活动安排
   */
  private async optimizeActivities(
    activities: UltraThinkActivity[],
    request: UltraThinkActivityRequest
  ): Promise<UltraThinkActivity[]> {
    console.log('⚡ 优化活动安排...');

    // 1. 时间优化：避免冲突，合理安排
    activities = this.optimizeTimeSchedule(activities);

    // 2. 地理优化：相近地点安排在同一天
    activities = this.optimizeGeographically(activities);

    // 3. 🚗 生成点对点交通
    console.log('🚗 生成点对点交通...');
    const transports = pointToPointTransportGenerator.generateTransportBetweenActivities(
      activities.filter(a => a.type !== 'transport'), // 排除已有的交通活动
      request.preferences,
      request.destination
    );

    // 将交通活动插入到活动序列中
    activities = this.insertTransportActivities(activities, transports);
    console.log(`✅ 生成了${transports.length}个交通活动`);

    // 4. 预算优化：平衡每天的花费
    activities = this.optimizeBudget(activities, request);

    // 5. 最终预算约束验证和修复
    activities = this.validateAndFixBudgetConstraints(activities, request);

    // 6. 活动内容质量提升
    activities = await this.enhanceActivityContentQuality(activities, request);

    console.log('✅ 活动安排优化完成');
    return activities;
  }

  /**
   * 🚗 插入交通活动到活动序列中
   */
  private insertTransportActivities(
    activities: UltraThinkActivity[],
    transports: any[]
  ): UltraThinkActivity[] {
    const result: UltraThinkActivity[] = [];

    // 按天和时间排序活动
    const sortedActivities = activities
      .filter(a => a.type !== 'transport')
      .sort((a, b) => {
        if (a.timing.day !== b.timing.day) {
          return a.timing.day - b.timing.day;
        }
        const timeA = this.parseTime(a.startTime || a.timing.startTime || '09:00');
        const timeB = this.parseTime(b.startTime || b.timing.startTime || '09:00');
        return timeA - timeB;
      });

    // 插入交通活动
    for (let i = 0; i < sortedActivities.length; i++) {
      result.push(sortedActivities[i]);

      // 在相邻活动间插入对应的交通活动
      if (i < sortedActivities.length - 1) {
        const currentActivity = sortedActivities[i];
        const nextActivity = sortedActivities[i + 1];

        // 查找对应的交通活动
        const transport = transports.find(t =>
          t.from.name === currentActivity.name && t.to.name === nextActivity.name
        );

        if (transport) {
          // 转换为UltraThinkActivity格式
          const transportActivity: UltraThinkActivity = {
            ...transport,
            category: 'transport',
            location: {
              address: `${transport.from.name} → ${transport.to.name}`,
              coordinates: transport.from.coordinates,
              district: transport.from.address,
              nearbyLandmarks: []
            },
            rating: 4.0,
            difficulty: 'easy',
            bestTimeToVisit: ['全天'],
            tags: ['交通', transport.subType],
            photos: [],
            details: {
              ...transport.details,
              openingHours: '全天',
              website: '',
              phone: '',
              email: '',
              socialMedia: {},
              reviews: 0,
              highlights: [transport.route.description],
              tips: transport.details.tips,
              bookingRequired: transport.subType === 'flight',
              seasonalInfo: '全年可用'
            }
          };

          result.push(transportActivity);
        }
      }
    }

    return result;
  }

  /**
   * 🕐 解析时间字符串为分钟数
   */
  private parseTime(timeString: string): number {
    if (!timeString || timeString === '00:00') return 540; // 默认09:00

    const [hours, minutes] = timeString.split(':').map(Number);
    if (isNaN(hours) || isNaN(minutes)) return 540;

    return hours * 60 + minutes;
  }

  /**
   * 📊 计算总结信息
   */
  private calculateSummary(activities: UltraThinkActivity[]) {
    const summary = {
      totalActivities: activities.length,
      activitiesByType: {} as Record<string, number>,
      activitiesByDay: {} as Record<number, number>,
      totalCost: 0,
      averageRating: 0
    };

    let totalRating = 0;
    let ratedActivities = 0;

    activities.forEach(activity => {
      // 按类型统计
      summary.activitiesByType[activity.type] = (summary.activitiesByType[activity.type] || 0) + 1;

      // 按天统计
      summary.activitiesByDay[activity.timing.day] = (summary.activitiesByDay[activity.timing.day] || 0) + 1;

      // 总费用
      summary.totalCost += activity.cost.amount;

      // 平均评分
      if (activity.details.rating) {
        totalRating += activity.details.rating;
        ratedActivities++;
      }
    });

    summary.averageRating = ratedActivities > 0 ? totalRating / ratedActivities : 0;

    return summary;
  }

  /**
   * 📈 计算优化指标
   */
  private calculateOptimization(activities: UltraThinkActivity[], request: UltraThinkActivityRequest) {
    return {
      timeEfficiency: this.calculateTimeEfficiency(activities),
      budgetUtilization: this.calculateBudgetUtilization(activities, request.budget),
      diversityScore: this.calculateDiversityScore(activities),
      seasonalRelevance: this.calculateSeasonalRelevance(activities, request.startDate)
    };
  }

  /**
   * 🛡️ 生成降级活动
   */
  private async generateFallbackActivities(
    request: UltraThinkActivityRequest,
    startTime: number,
    error: any
  ): Promise<UltraThinkActivityResponse> {
    console.log('🛡️ 生成降级活动...');

    const fallbackActivities: UltraThinkActivity[] = [];

    // 为每天生成基础活动
    for (let day = 1; day <= request.duration; day++) {
      const currentDate = new Date(request.startDate);
      currentDate.setDate(currentDate.getDate() + day - 1);

      // 每天3个基础活动
      for (let i = 0; i < 3; i++) {
        const activity = this.createTemplateActivity(
          request.destination,
          day,
          i + 1,
          currentDate,
          request
        );
        fallbackActivities.push(activity);
      }
    }

    return {
      success: true,
      activities: fallbackActivities,
      summary: this.calculateSummary(fallbackActivities),
      optimization: {
        timeEfficiency: 0.7,
        budgetUtilization: 0.6,
        diversityScore: 0.5,
        seasonalRelevance: 0.6
      },
      executionTime: Date.now() - startTime,
      fallbackUsed: true,
      error: error.message
    };
  }

  // 工具方法实现
  private extractDescription(content: string): string {
    // 简单的描述提取逻辑
    const lines = content.split('\n');
    return lines.find(line => line.includes('描述') || line.length > 20)?.trim() || '精彩的旅行体验';
  }

  private extractHighlights(content: string): string[] {
    // 简单的亮点提取逻辑
    const highlights = content.match(/\d+\.\s*([^。\n]+)/g);
    return highlights ? highlights.slice(0, 3).map(h => h.replace(/\d+\.\s*/, '')) : ['精彩体验', '难忘回忆', '文化探索'];
  }

  private extractTips(content: string): string[] {
    // 简单的建议提取逻辑
    return ['建议提前预订', '注意开放时间'];
  }

  private optimizeTimeSchedule(activities: UltraThinkActivity[]): UltraThinkActivity[] {
    console.log('⚡ 使用统一智能时间分配引擎优化时间安排');
    
    try {
      // 导入统一时间分配引擎
      const { UnifiedTimeAllocationEngine } = require('../../services/time/UnifiedTimeAllocationEngine');
      
      // 转换活动格式为时间分配引擎所需的格式
      const timeRequirements = activities.map(activity => ({
        id: activity.id,
        name: activity.name || activity.name_zh,
        type: this.mapActivityTypeForTimeEngine(activity.type),
        category: activity.category,
        location: activity.location,
        priority: this.calculateActivityPriority(activity),
        preferenceScore: 0.7, // 默认偏好分数
        isPreferenceMatch: false,
        duration: activity.timing?.duration || activity.duration || 90,
        flexibilityLevel: this.determineFlexibilityLevel(activity),
        optimalTimeSlots: this.getOptimalTimeSlots(activity),
        weatherSensitive: this.isWeatherSensitive(activity),
        bookingRequired: activity.details?.bookingRequired || false,
        cost: activity.cost?.amount || 0
      }));
      
      // 计算总天数
      const maxDay = Math.max(...activities.map(a => a.timing?.day || 1));
      const totalDays = Math.max(1, maxDay);
      
      // 使用统一时间分配引擎
      const allocationResult = UnifiedTimeAllocationEngine.allocateIntelligentTimes(
        timeRequirements,
        totalDays,
        new Date(),
        {
          pacePreference: 'moderate',
          mealImportance: 'medium'
        }
      );
      
      if (!allocationResult.success) {
        console.warn('⚠️ 统一时间分配失败，使用备用方案');
        return this.fallbackTimeSchedule(activities);
      }
      
      // 将分配结果转换回UltraThinkActivity格式
      const optimizedActivities: UltraThinkActivity[] = [];
      
      allocationResult.allocations.forEach(dayAllocation => {
        dayAllocation.activities.forEach(allocatedActivity => {
          const originalActivity = activities.find(a => a.id === allocatedActivity.id);
          if (originalActivity) {
            // 更新时间信息
            const updatedActivity: UltraThinkActivity = {
              ...originalActivity,
              timing: {
                ...originalActivity.timing,
                startTime: allocatedActivity.timeSlot.startTime,
                endTime: allocatedActivity.timeSlot.endTime,
                duration: allocatedActivity.timeSlot.duration,
                day: allocatedActivity.day,
                date: dayAllocation.date
              },
              // 同步顶级时间字段
              startTime: allocatedActivity.timeSlot.startTime,
              endTime: allocatedActivity.timeSlot.endTime,
              duration: allocatedActivity.timeSlot.duration,
              // 添加优化信息
              metadata: {
                ...originalActivity.metadata,
                timeOptimized: true,
                timeConfidence: allocatedActivity.timeSlot.confidence,
                schedulingReason: allocatedActivity.schedulingReason,
                timeReasoning: allocatedActivity.timeSlot.reasoning,
                lastUpdated: new Date().toISOString()
              }
            };
            
            optimizedActivities.push(updatedActivity);
          }
        });
      });
      
      // 记录优化结果
      console.log(`✅ 时间优化完成: ${optimizedActivities.length}个活动已优化`);
      console.log(`📊 质量指标: 一致性${(allocationResult.qualityMetrics.timeConsistency * 100).toFixed(1)}%, 流畅性${(allocationResult.qualityMetrics.logicalFlow * 100).toFixed(1)}%`);
      
      // 输出警告和建议
      if (allocationResult.warnings.length > 0) {
        console.warn('⚠️ 时间优化警告:', allocationResult.warnings);
      }
      
      if (allocationResult.recommendations.length > 0) {
        console.log('💡 时间优化建议:', allocationResult.recommendations);
      }
      
      return optimizedActivities;
      
    } catch (error) {
      console.error('❌ 统一时间分配引擎执行失败:', error);
      return this.fallbackTimeSchedule(activities);
    }
  }

  /**
   * 🔄 映射活动类型到时间引擎格式
   */
  private mapActivityTypeForTimeEngine(type: string): 'attraction' | 'meal' | 'transport' | 'accommodation' | 'shopping' | 'cultural' | 'entertainment' {
    const typeMapping = {
      'attraction': 'attraction',
      'transport': 'transport', 
      'accommodation': 'accommodation',
      'flight': 'transport',
      'meal': 'meal',
      'shopping': 'shopping',
      'cultural': 'cultural',
      'cultural_experience': 'cultural',
      'cultural_site': 'cultural',
      'entertainment': 'entertainment',
      'fine_dining': 'meal',
      'food_tour': 'meal'
    };
    
    return typeMapping[type] || 'attraction';
  }

  /**
   * 🎯 确定活动的灵活性级别
   */
  private determineFlexibilityLevel(activity: UltraThinkActivity): 'rigid' | 'flexible' | 'very_flexible' {
    // 需要预订的活动通常较为严格
    if (activity.details?.bookingRequired) {
      return 'rigid';
    }
    
    // 交通和住宿相对固定
    if (activity.type === 'transport' || activity.type === 'accommodation') {
      return 'rigid';
    }
    
    // 用餐有一定的时间要求
    if (activity.type === 'meal') {
      return 'flexible';
    }
    
    // 其他活动相对灵活
    return 'very_flexible';
  }

  /**
   * ⏰ 获取活动的最佳时间段
   */
  private getOptimalTimeSlots(activity: UltraThinkActivity): ('morning' | 'afternoon' | 'evening')[] {
    const type = activity.type;
    
    // 基于活动类型的最佳时间段
    const timeSlotMapping = {
      'attraction': ['morning', 'afternoon'],
      'cultural': ['morning', 'afternoon'],
      'cultural_experience': ['morning', 'afternoon'],
      'cultural_site': ['morning', 'afternoon'],
      'meal': ['morning', 'afternoon', 'evening'],
      'fine_dining': ['evening'],
      'food_tour': ['afternoon', 'evening'],
      'shopping': ['afternoon', 'evening'],
      'entertainment': ['afternoon', 'evening'],
      'transport': ['morning', 'afternoon', 'evening'],
      'accommodation': ['morning', 'evening']
    };
    
    return timeSlotMapping[type] || ['morning', 'afternoon'];
  }

  /**
   * 🌤️ 判断活动是否对天气敏感
   */
  private isWeatherSensitive(activity: UltraThinkActivity): boolean {
    const weatherSensitiveTypes = ['attraction', 'cultural_site'];
    const weatherSensitiveKeywords = ['户外', '公园', '花园', '海滩', '山', '湖'];
    
    // 基于类型判断
    if (weatherSensitiveTypes.includes(activity.type)) {
      return true;
    }
    
    // 基于名称关键词判断
    const name = (activity.name || activity.name_zh || '').toLowerCase();
    return weatherSensitiveKeywords.some(keyword => name.includes(keyword));
  }

  /**
   * 🔄 备用时间安排方案
   */
  private fallbackTimeSchedule(activities: UltraThinkActivity[]): UltraThinkActivity[] {
    console.log('🔄 使用备用时间安排方案');
    
    // 按天分组
    const dayGroups = activities.reduce((groups, activity) => {
      const day = activity.timing?.day || 1;
      if (!groups[day]) groups[day] = [];
      groups[day].push(activity);
      return groups;
    }, {} as Record<number, UltraThinkActivity[]>);

    const optimizedActivities: UltraThinkActivity[] = [];
    
    Object.entries(dayGroups).forEach(([dayStr, dayActivities]) => {
      const day = parseInt(dayStr);
      let currentTime = 9 * 60; // 从9:00开始
      
      // 按类型和优先级排序
      const sortedActivities = dayActivities.sort((a, b) => {
        const priorityA = this.calculateActivityPriority(a);
        const priorityB = this.calculateActivityPriority(b);
        return priorityB - priorityA;
      });
      
      sortedActivities.forEach((activity, index) => {
        // 确保持续时间合理
        let duration = activity.timing?.duration || activity.duration || 90;
        if (duration <= 0 || duration > 240) {
          duration = this.getDefaultDurationForType(activity.type);
        }
        
        const startTime = this.minutesToTime(currentTime);
        const endTime = this.minutesToTime(currentTime + duration);
        
        const optimizedActivity: UltraThinkActivity = {
          ...activity,
          timing: {
            ...activity.timing,
            startTime,
            endTime,
            duration,
            day
          },
          startTime,
          endTime,
          duration,
          metadata: {
            ...activity.metadata,
            timeOptimized: true,
            fallbackUsed: true,
            lastUpdated: new Date().toISOString()
          }
        };
        
        optimizedActivities.push(optimizedActivity);
        
        // 更新当前时间（加上活动时间和15分钟缓冲）
        currentTime += duration + 15;
        
        console.log(`📅 Day ${day} - ${activity.name}: ${startTime}-${endTime} (${duration}分钟)`);
      });
    });
    
    return optimizedActivities;
  }

  /**
   * 🕐 分钟转时间字符串
   */
  private minutesToTime(minutes: number): string {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
  }

  /**
   * ⏱️ 获取活动类型的默认持续时间
   */
  private getDefaultDurationForType(type: string): number {
    const defaultDurations = {
      'attraction': 90,
      'cultural': 105,
      'cultural_experience': 120,
      'cultural_site': 90,
      'meal': 60,
      'fine_dining': 90,
      'food_tour': 120,
      'shopping': 75,
      'entertainment': 90,
      'transport': 20,
      'accommodation': 30
    };
    
    return defaultDurations[type] || 90;
  }

  private optimizeGeographically(activities: UltraThinkActivity[]): UltraThinkActivity[] {
    // 地理优化的简单实现
    return activities;
  }

  /**
   * 💰 预算优化 - 核心修复函数
   * 确保总预算不超过用户设定的限制
   */
  private optimizeBudget(activities: UltraThinkActivity[], request: UltraThinkActivityRequest): UltraThinkActivity[] {
    const targetBudget = request.budget;
    const currency = request.currency;

    // 1. 计算当前总费用
    let currentTotal = activities.reduce((sum, activity) => sum + activity.cost.amount, 0);

    console.log(`💰 预算优化开始: 目标预算=${targetBudget} ${currency}, 当前总费用=${currentTotal} ${currency}`);

    // 2. 如果预算充足，直接返回
    if (currentTotal <= targetBudget) {
      console.log(`✅ 预算充足，无需优化`);
      return activities;
    }

    // 3. 预算超支，需要优化
    const overBudget = currentTotal - targetBudget;
    console.log(`⚠️ 预算超支 ${overBudget} ${currency}，开始优化`);

    // 4. 按优先级排序活动（保护核心活动）
    const sortedActivities = this.sortActivitiesByPriority(activities);

    // 5. 智能削减费用
    const optimizedActivities = this.smartCostReduction(sortedActivities, targetBudget, currency);

    // 6. 验证最终预算
    const finalTotal = optimizedActivities.reduce((sum, activity) => sum + activity.cost.amount, 0);
    console.log(`✅ 预算优化完成: 最终费用=${finalTotal} ${currency}, 节省=${currentTotal - finalTotal} ${currency}`);

    return optimizedActivities;
  }

  /**
   * 🎯 按优先级排序活动
   */
  private sortActivitiesByPriority(activities: UltraThinkActivity[]): UltraThinkActivity[] {
    return [...activities].sort((a, b) => {
      // 优先级权重计算
      const priorityA = this.calculateActivityPriority(a);
      const priorityB = this.calculateActivityPriority(b);

      return priorityB - priorityA; // 高优先级在前
    });
  }

  /**
   * 📊 计算活动优先级
   */
  private calculateActivityPriority(activity: UltraThinkActivity): number {
    let priority = 0;

    // 1. 基础类型权重
    const typeWeights: Record<string, number> = {
      'flight': 100,        // 航班最重要
      'accommodation': 90,  // 住宿次重要
      'transport': 70,      // 交通重要
      'meal': 60,          // 餐饮重要
      'attraction': 50,     // 景点中等
      'shopping': 30,       // 购物可选
      'entertainment': 40   // 娱乐可选
    };
    priority += typeWeights[activity.type] || 40;

    // 2. 评分权重
    if (activity.details.rating) {
      priority += activity.details.rating * 5; // 最高25分
    }

    // 3. 性价比权重（评分/费用比）
    if (activity.details.rating && activity.cost.amount > 0) {
      const valueRatio = activity.details.rating / (activity.cost.amount / 100);
      priority += Math.min(valueRatio, 20); // 最高20分
    }

    // 4. 免费活动加分
    if (activity.cost.amount === 0) {
      priority += 15;
    }

    return priority;
  }

  /**
   * ✂️ 智能费用削减
   */
  private smartCostReduction(activities: UltraThinkActivity[], targetBudget: number, currency: string): UltraThinkActivity[] {
    const result = [...activities];
    let currentTotal = result.reduce((sum, activity) => sum + activity.cost.amount, 0);

    // 策略1: 替换高费用低优先级活动为免费/低费用活动
    for (let i = result.length - 1; i >= 0 && currentTotal > targetBudget; i--) {
      const activity = result[i];

      // 跳过核心活动（航班、住宿、交通）
      if (['flight', 'accommodation', 'transport'].includes(activity.type)) {
        continue;
      }

      // 如果是高费用活动，尝试替换或削减
      if (activity.cost.amount > 50) {
        const originalCost = activity.cost.amount;

        // 尝试削减费用（保持活动但降低费用）
        const reducedCost = Math.max(
          Math.round(originalCost * 0.6), // 削减40%
          this.getMinimumCostForType(activity.type)
        );

        result[i] = {
          ...activity,
          cost: {
            ...activity.cost,
            amount: reducedCost,
            priceLevel: this.adjustPriceLevel(activity.cost.priceLevel, reducedCost, originalCost)
          },
          name: activity.name + ' (经济版)',
          name_zh: (activity.name_zh || activity.name) + ' (经济版)'
        };

        const savedAmount = originalCost - reducedCost;
        currentTotal -= savedAmount;

        console.log(`💡 费用削减: ${activity.name} 从 ${originalCost} 降至 ${reducedCost} ${currency}`);
      }
    }

    // 策略2: 如果仍然超支，移除最低优先级的活动
    while (currentTotal > targetBudget && result.length > 3) { // 保留至少3个活动
      const lastActivity = result[result.length - 1];

      // 不移除核心活动
      if (['flight', 'accommodation'].includes(lastActivity.type)) {
        break;
      }

      currentTotal -= lastActivity.cost.amount;
      const removedActivity = result.pop();

      console.log(`🗑️ 移除活动: ${removedActivity?.name} (费用: ${removedActivity?.cost.amount} ${currency})`);
    }

    // 策略3: 如果还是超支，对所有非核心活动按比例削减
    if (currentTotal > targetBudget) {
      const overBudget = currentTotal - targetBudget;
      const nonCoreActivities = result.filter(a => !['flight', 'accommodation'].includes(a.type));
      const nonCoreCost = nonCoreActivities.reduce((sum, a) => sum + a.cost.amount, 0);

      if (nonCoreCost > 0) {
        const reductionRatio = Math.min(overBudget / nonCoreCost, 0.5); // 最多削减50%

        result.forEach((activity, index) => {
          if (!['flight', 'accommodation'].includes(activity.type)) {
            const reduction = Math.round(activity.cost.amount * reductionRatio);
            const newCost = Math.max(
              activity.cost.amount - reduction,
              this.getMinimumCostForType(activity.type)
            );

            result[index] = {
              ...activity,
              cost: {
                ...activity.cost,
                amount: newCost
              }
            };

            currentTotal -= (activity.cost.amount - newCost);
          }
        });

        console.log(`📉 按比例削减: 削减比例=${(reductionRatio * 100).toFixed(1)}%`);
      }
    }

    return result;
  }

  /**
   * 💰 获取活动类型的最低费用
   */
  private getMinimumCostForType(type: string): number {
    const minimumCosts: Record<string, number> = {
      'attraction': 5,
      'meal': 15,
      'transport': 5,
      'shopping': 0,
      'entertainment': 10,
      'accommodation': 50,
      'flight': 200
    };

    return minimumCosts[type] || 0;
  }

  /**
   * 🏷️ 调整价格等级
   */
  private adjustPriceLevel(
    originalLevel: 'free' | 'budget' | 'mid_range' | 'luxury',
    newCost: number,
    originalCost: number
  ): 'free' | 'budget' | 'mid_range' | 'luxury' {
    if (newCost === 0) return 'free';

    const reductionRatio = newCost / originalCost;

    if (reductionRatio <= 0.3) {
      return originalLevel === 'luxury' ? 'mid_range' : 'budget';
    } else if (reductionRatio <= 0.6) {
      return originalLevel === 'luxury' ? 'mid_range' : originalLevel;
    }

    return originalLevel;
  }

  /**
   * 🔒 验证和修复预算约束 - 最终安全检查
   */
  private validateAndFixBudgetConstraints(
    activities: UltraThinkActivity[],
    request: UltraThinkActivityRequest
  ): UltraThinkActivity[] {
    // 创建预算约束
    const constraints: BudgetConstraint = {
      totalBudget: request.budget,
      currency: request.currency,
      maxDailySpend: request.budget / request.duration,
      emergencyReserve: request.budget * 0.1
    };

    // 转换活动格式以便验证
    const activitiesForValidation = activities.map(activity => ({
      id: activity.id,
      name: activity.name,
      type: activity.type,
      cost: activity.cost,
      timing: activity.timing
    }));

    // 执行预算验证
    const validation = BudgetConstraintValidator.validateBudgetConstraints(
      activitiesForValidation,
      constraints
    );

    // 打印预算报告
    console.log(BudgetConstraintValidator.generateBudgetReport(validation));

    // 如果预算超支，自动修复
    if (!validation.isValid) {
      console.log(`⚠️ 预算约束验证失败，开始自动修复`);

      const fixedActivities = BudgetConstraintValidator.autoFixBudgetOverrun(
        activitiesForValidation,
        constraints
      );

      // 将修复后的活动映射回原始格式
      const fixedActivityIds = new Set(fixedActivities.map(a => a.id));
      const result = activities.filter(activity => fixedActivityIds.has(activity.id));

      console.log(`✅ 预算约束修复完成: ${activities.length} -> ${result.length} 个活动`);
      return result;
    }

    console.log(`✅ 预算约束验证通过`);
    return activities;
  }

  /**
   * 🎨 提升活动内容质量
   */
  private async enhanceActivityContentQuality(
    activities: UltraThinkActivity[],
    request: UltraThinkActivityRequest
  ): Promise<UltraThinkActivity[]> {
    console.log(`🎨 开始提升活动内容质量，共${activities.length}个活动`);

    try {
      // 识别需要提升的活动（模板活动或质量分数低的活动）
      const activitiesToEnhance = activities.filter(activity =>
        activity.metadata.source === 'template' ||
        activity.metadata.qualityScore < 0.8 ||
        this.isGenericContent(activity)
      );

      if (activitiesToEnhance.length === 0) {
        console.log(`✅ 所有活动内容质量已达标，无需提升`);
        return activities;
      }

      console.log(`🔧 需要提升${activitiesToEnhance.length}个活动的内容质量`);

      // 批量提升内容质量
      const enhancedActivities = await ActivityContentEnhancer.batchEnhanceActivities(
        activitiesToEnhance,
        request.destination
      );

      // 合并提升后的活动
      const activityMap = new Map(enhancedActivities.map(a => [a.id, a]));
      const result = activities.map(activity =>
        activityMap.get(activity.id) || activity
      );

      const enhancedCount = result.filter(a => a.metadata?.enhanced).length;
      console.log(`✅ 活动内容质量提升完成，成功提升${enhancedCount}个活动`);

      return result;
    } catch (error) {
      console.error('❌ 活动内容质量提升失败:', error);
      return activities; // 失败时返回原始活动
    }
  }

  /**
   * 🔍 检查是否为通用内容
   */
  private isGenericContent(activity: UltraThinkActivity): boolean {
    const genericPatterns = [
      /\{destination\}/i,
      /东京\s*lunch/i,
      /文化中心$/,
      /市场$/,
      /公园$/,
      /博物馆$/,
      /精彩体验/,
      /独特魅力/,
      /深度体验.*独特魅力/
    ];

    const name = activity.name || activity.name_zh || '';
    const description = activity.description || activity.description_zh || '';

    return genericPatterns.some(pattern =>
      pattern.test(name) || pattern.test(description)
    );
  }

  private calculateTimeEfficiency(activities: UltraThinkActivity[]): number {
    // 时间效率计算
    return 0.85;
  }

  private calculateBudgetUtilization(activities: UltraThinkActivity[], totalBudget: number): number {
    const totalCost = activities.reduce((sum, activity) => sum + activity.cost.amount, 0);
    return Math.min(totalCost / totalBudget, 1.0);
  }

  private calculateDiversityScore(activities: UltraThinkActivity[]): number {
    const types = new Set(activities.map(a => a.type));
    return types.size / 6; // 6种类型的活动
  }

  private calculateSeasonalRelevance(activities: UltraThinkActivity[], startDate: Date): number {
    // 季节相关性计算
    return 0.8;
  }

  private createTemplateFlightActivity(request: UltraThinkActivityRequest): UltraThinkActivity {
    return {
      id: `template_flight_${Date.now()}`,
      name: `Flight to ${request.destination}`,
      name_zh: `飞往${request.destination}`,
      type: 'flight',
      category: 'transport',
      description: `Direct flight to ${request.destination}`,
      description_zh: `直飞${request.destination}`,
      location: {
        name: '吉隆坡国际机场',
        address: '吉隆坡国际机场'
      },
      timing: {
        startTime: '08:00',
        endTime: '16:00',
        duration: 480,
        date: request.startDate.toISOString().split('T')[0],
        day: 1
      },
      duration: 480, // 添加顶级duration字段
      startTime: '08:00', // 添加顶级startTime字段
      endTime: '16:00',   // 添加顶级endTime字段
      cost: {
        amount: 1200,
        currency: request.currency,
        priceLevel: 'mid_range'
      },
      details: {
        rating: 4.0,
        highlights: ['直飞航班', '舒适座椅', '机上餐食'],
        tips: ['提前2小时到达', '检查签证要求'],
        bookingRequired: true
      },
      metadata: {
        source: 'template',
        confidence: 0.7,
        qualityScore: 0.75,
        lastUpdated: new Date().toISOString()
      }
    };
  }

  private createTemplateAccommodationActivity(request: UltraThinkActivityRequest): UltraThinkActivity {
    return {
      id: `template_hotel_${Date.now()}`,
      name: `${request.destination} Hotel`,
      name_zh: `${request.destination}酒店`,
      type: 'accommodation',
      category: 'accommodation',
      description: `Comfortable accommodation in ${request.destination}`,
      description_zh: `在${request.destination}的舒适住宿`,
      location: {
        name: `${request.destination}市中心酒店`,
        address: `${request.destination}市中心`
      },
      timing: {
        startTime: '15:00',
        endTime: '11:00',
        duration: request.duration * 24 * 60,
        date: request.startDate.toISOString().split('T')[0],
        day: 1
      },
      cost: {
        amount: 350 * request.duration,
        currency: request.currency,
        priceLevel: 'mid_range'
      },
      details: {
        rating: 4.2,
        highlights: ['免费WiFi', '早餐', '24小时前台'],
        tips: ['提前办理入住', '了解酒店设施'],
        bookingRequired: true
      },
      metadata: {
        source: 'template',
        confidence: 0.7,
        qualityScore: 0.75,
        lastUpdated: new Date().toISOString()
      }
    };
  }

  /**
   * 🔄 转换Ultra Think V3.0结果为传统活动格式
   */
  private convertV3ToLegacyActivities(v3Result: any): any[] {
    console.log('🔄 转换Ultra Think V3.0数据为传统活动格式');

    const activities: any[] = [];

    // 直接使用V3结果中的activities数组
    if (v3Result.activities && Array.isArray(v3Result.activities)) {
      v3Result.activities.forEach((activity: any) => {
        activities.push({
          id: activity.id,
          name: activity.title,
          title: activity.title,
          type: activity.type,
          timing: {
            day: activity.day,
            startTime: activity.time,
            endTime: this.addMinutes(activity.time, activity.duration),
            duration: activity.duration
          },
          location: activity.location,
          cost: activity.cost,
          description: activity.description,
          highlights: [],
          expandedContent: activity.description,
          metadata: {
            source: 'ultra_think_v3',
            quality: 'premium'
          }
        });
      });
    }

    // 添加惊喜体验作为特殊活动
    if (v3Result.surprises && Array.isArray(v3Result.surprises)) {
      v3Result.surprises.forEach((surprise: any, index: number) => {
        activities.push({
          id: `surprise_${index}`,
          name: surprise.name,
          title: `🎉 ${surprise.name}`,
          type: 'surprise',
          timing: {
            day: Math.floor(Math.random() * 3) + 1, // 随机分配到某天
            startTime: '15:00',
            endTime: '16:30',
            duration: surprise.timing?.duration || 90
          },
          location: {
            name: '惊喜地点',
            address: '待发现'
          },
          cost: 0,
          description: surprise.description,
          highlights: ['惊喜体验', '独特发现'],
          expandedContent: surprise.experience?.description || surprise.description,
          metadata: {
            source: 'ultra_think_v3_surprise',
            surpriseLevel: surprise.surpriseLevel,
            emotionalImpact: surprise.emotionalImpact
          }
        });
      });
    }

    console.log(`✅ 转换完成: ${activities.length}个活动 (包含${v3Result.surprises?.length || 0}个惊喜)`);
    return activities;
  }

  /**
   * 🔄 转换Ultra Think V3.0结果为传统摘要格式
   */
  private convertV3ToLegacySummary(v3Result: any): any {
    console.log('🔄 转换Ultra Think V3.0数据为传统摘要格式');

    return {
      destination: v3Result.summary?.destination || '未知目的地',
      duration: v3Result.summary?.duration || 0,
      totalActivities: v3Result.summary?.totalActivities || 0,
      totalCost: v3Result.summary?.estimatedCost || 0,
      currency: v3Result.summary?.currency || 'MYR',
      highlights: v3Result.summary?.highlights || [],
      recommendations: v3Result.summary?.recommendations || [],
      surpriseCount: v3Result.surprises?.length || 0,
      qualityMetrics: {
        overall: v3Result.optimization?.qualityScore || 0.8,
        personalization: v3Result.optimization?.personalizedScore || 0.8,
        surprise: v3Result.optimization?.surpriseIndex || 0,
        efficiency: v3Result.optimization?.timeEfficiency || 0.8
      },
      metadata: {
        generatedBy: 'Ultra Think V3.0',
        version: v3Result.metadata?.version || '3.0',
        componentsUsed: v3Result.metadata?.componentsUsed || [],
        generationTime: v3Result.metadata?.generationTime || 0
      }
    };
  }
}

// ===== 导出单例实例 =====

export const ultraThinkActivityGenerator = UltraThinkActivityGenerator.getInstance();
