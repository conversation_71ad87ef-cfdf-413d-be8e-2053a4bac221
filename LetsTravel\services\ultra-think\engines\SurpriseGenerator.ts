/**
 * 🎉 Surprise Generator - 意外惊喜生成器
 * Phase 4: 惊喜体验注入器 - 意外惊喜生成
 * 为用户创造难忘的旅行体验，注入意想不到的惊喜元素
 */

import { HiddenGemsDiscovery, HiddenGem, DiscoveryResult } from './HiddenGemsDiscovery';
import { LocalSecretsEngine, LocalSecret, SecretsResult } from './LocalSecretsEngine';
import { UltraThinkLLMManager } from '../../ai/UltraThinkLLMManager';
import { UltraThinkCacheManager } from '../UltraThinkCacheManager';

// ===== 惊喜生成接口定义 =====

export interface SurpriseElement {
  id: string;
  type: 'serendipity' | 'discovery' | 'connection' | 'transformation' | 'revelation' | 'adventure';
  name: string;
  description: string;
  surpriseLevel: number; // 0-1, 惊喜程度
  emotionalImpact: number; // 0-1, 情感影响力
  memorability: number; // 0-1, 记忆深度
  timing: {
    when: 'arrival' | 'morning' | 'afternoon' | 'evening' | 'departure' | 'flexible';
    duration: number; // 分钟
    preparation: number; // 准备时间(分钟)
  };
  trigger: {
    condition: string; // 触发条件
    probability: number; // 0-1, 发生概率
    dependencies: string[]; // 依赖条件
  };
  experience: {
    setup: string; // 设置过程
    climax: string; // 高潮时刻
    resolution: string; // 结果
    afterglow: string; // 余韵
  };
  personalization: {
    userTypes: string[]; // 适合的用户类型
    interests: string[]; // 相关兴趣
    adaptability: number; // 0-1, 适应性
  };
  metadata: {
    creationDate: Date;
    source: string[];
    confidence: number;
    testResults?: SurpriseTestResult[];
  };
}

export interface SurpriseRequest {
  destination: string;
  userProfile: {
    personality: string;
    interests: string[];
    surprisePreference: number; // 0-1, 惊喜偏好
    adventureLevel: 'conservative' | 'moderate' | 'adventurous';
    emotionalOpenness: number; // 0-1, 情感开放度
  };
  journeyContext: {
    duration: number; // 天数
    budget: number;
    companions: string;
    purpose: string; // 旅行目的
    previousExperiences: string[]; // 之前的体验
  };
  constraints: {
    timeFlexibility: number; // 0-1, 时间灵活性
    budgetFlexibility: number; // 0-1, 预算灵活性
    comfortZone: number; // 0-1, 舒适区范围
    riskTolerance: number; // 0-1, 风险承受度
  };
}

export interface SurprisePackage {
  id: string;
  destination: string;
  theme: string;
  surprises: SurpriseElement[];
  overallImpact: number; // 0-1, 整体影响力
  coherence: number; // 0-1, 连贯性
  progression: SurpriseProgression;
  contingencies: SurpriseContingency[];
  metadata: {
    generationDate: Date;
    algorithm: string;
    version: string;
    personalizedFor: string;
  };
}

export interface SurpriseProgression {
  arc: 'gentle' | 'building' | 'climactic' | 'wave-like';
  phases: SurprisePhase[];
  emotionalJourney: string[];
  peakMoments: number[]; // 高峰时刻的索引
}

export interface SurprisePhase {
  name: string;
  timeframe: string;
  surprises: string[]; // surprise IDs
  emotionalTone: string;
  intensity: number; // 0-1
}

export interface SurpriseContingency {
  scenario: string;
  alternatives: SurpriseElement[];
  fallbackPlan: string;
  recoveryStrategy: string;
}

export interface SurpriseTestResult {
  testType: 'simulation' | 'user-feedback' | 'expert-review';
  score: number; // 0-1
  feedback: string;
  improvements: string[];
}

// ===== Surprise Generator 核心类 =====

export class SurpriseGenerator {
  private static instance: SurpriseGenerator;
  private hiddenGemsDiscovery = HiddenGemsDiscovery.getInstance();
  private localSecretsEngine = LocalSecretsEngine.getInstance();
  private llmManager = UltraThinkLLMManager.getInstance();
  private cacheManager = UltraThinkCacheManager.getInstance();
  private surpriseTemplates = new Map<string, any>();
  private emotionalPatterns = new Map<string, any>();
  private surpriseHistory: SurprisePackage[] = [];

  private constructor() {
    this.initializeSurpriseTemplates();
    this.initializeEmotionalPatterns();
    console.log('🎉 Surprise Generator 初始化完成');
  }

  static getInstance(): SurpriseGenerator {
    if (!SurpriseGenerator.instance) {
      SurpriseGenerator.instance = new SurpriseGenerator();
    }
    return SurpriseGenerator.instance;
  }

  /**
   * 🎯 生成惊喜包 - 主要入口方法
   */
  async generateSurprisePackage(request: SurpriseRequest): Promise<SurprisePackage> {
    const startTime = Date.now();
    console.log(`🎉 开始生成惊喜包: ${request.destination}`);

    try {
      // 1. 检查缓存
      const cacheKey = this.generateCacheKey(request);
      const cached = await this.cacheManager.get<SurprisePackage>(cacheKey, 'surprise-packages');
      
      if (cached && this.isCacheValid(cached)) {
        console.log('✅ 惊喜包缓存命中');
        return cached;
      }

      // 2. 收集惊喜素材
      const surpriseMaterials = await this.collectSurpriseMaterials(request);
      console.log(`📦 收集到${surpriseMaterials.hiddenGems.length}个隐藏宝石, ${surpriseMaterials.secrets.length}个当地秘密`);

      // 3. 生成惊喜元素
      const surpriseElements = await this.generateSurpriseElements(surpriseMaterials, request);
      console.log(`✨ 生成${surpriseElements.length}个惊喜元素`);

      // 4. 个性化匹配和筛选
      const personalizedSurprises = this.personalizeSuprises(surpriseElements, request);

      // 5. 构建惊喜进程
      const progression = this.buildSurpriseProgression(personalizedSurprises, request);

      // 6. 生成应急预案
      const contingencies = this.generateContingencies(personalizedSurprises, request);

      // 7. 构建惊喜包
      const surprisePackage: SurprisePackage = {
        id: this.generateSurprisePackageId(request),
        destination: request.destination,
        theme: this.determineTheme(personalizedSurprises, request),
        surprises: personalizedSurprises,
        overallImpact: this.calculateOverallImpact(personalizedSurprises),
        coherence: this.calculateCoherence(personalizedSurprises, progression),
        progression,
        contingencies,
        metadata: {
          generationDate: new Date(),
          algorithm: 'surprise-generator-v3',
          version: '3.0',
          personalizedFor: request.userProfile.personality
        }
      };

      // 8. 质量验证和优化
      const optimizedPackage = await this.optimizeSurprisePackage(surprisePackage, request);

      // 9. 缓存结果
      await this.cacheSurprisePackage(optimizedPackage);

      // 10. 记录历史
      this.recordSurpriseHistory(optimizedPackage);

      console.log(`✅ 惊喜包生成完成: ${optimizedPackage.surprises.length}个惊喜, 影响力${optimizedPackage.overallImpact.toFixed(2)}`);
      return optimizedPackage;

    } catch (error) {
      console.error('❌ 惊喜包生成失败:', error);
      return this.generateFallbackPackage(request, startTime);
    }
  }

  /**
   * 📦 收集惊喜素材
   */
  private async collectSurpriseMaterials(request: SurpriseRequest): Promise<any> {
    const materials = {
      hiddenGems: [] as HiddenGem[],
      secrets: [] as LocalSecret[]
    };

    try {
      // 收集隐藏宝石
      const gemsResult = await this.hiddenGemsDiscovery.discoverHiddenGems({
        destination: request.destination,
        userProfile: {
          interests: request.userProfile.interests,
          adventureLevel: request.userProfile.adventureLevel,
          culturalOpenness: request.userProfile.emotionalOpenness,
          budgetRange: this.mapBudgetRange(request.journeyContext.budget),
          groupType: this.mapGroupType(request.journeyContext.companions)
        },
        constraints: {
          maxDistance: 50,
          timeAvailable: request.journeyContext.duration * 8,
          accessibilityNeeds: [],
          avoidCrowds: request.userProfile.surprisePreference > 0.7
        },
        preferences: {
          hiddenLevel: request.userProfile.surprisePreference,
          localExperience: 0.8,
          surpriseLevel: request.userProfile.surprisePreference
        }
      });

      materials.hiddenGems = gemsResult.hiddenGems;

      // 收集当地秘密
      const secretsResult = await this.localSecretsEngine.discoverLocalSecrets({
        destination: request.destination,
        userProfile: {
          culturalSensitivity: request.userProfile.emotionalOpenness,
          languageSkills: [],
          socialComfort: request.constraints.comfortZone,
          respectLevel: 0.9,
          curiosity: request.userProfile.surprisePreference
        },
        interests: {
          categories: request.userProfile.interests,
          depth: request.userProfile.adventureLevel === 'adventurous' ? 'deep' : 'moderate',
          interaction: request.constraints.comfortZone > 0.7 ? 'immerse' : 'participate'
        },
        constraints: {
          timeCommitment: request.journeyContext.duration * 2,
          socialEnergy: request.userProfile.emotionalOpenness,
          comfortZone: request.constraints.comfortZone,
          languageBarrier: true
        }
      });

      materials.secrets = secretsResult.secrets;

    } catch (error) {
      console.warn('⚠️ 素材收集部分失败:', error);
    }

    return materials;
  }

  /**
   * ✨ 生成惊喜元素
   */
  private async generateSurpriseElements(materials: any, request: SurpriseRequest): Promise<SurpriseElement[]> {
    const elements: SurpriseElement[] = [];

    // 1. 从隐藏宝石生成惊喜
    materials.hiddenGems.forEach((gem: HiddenGem, index: number) => {
      elements.push(this.createSurpriseFromGem(gem, request, index));
    });

    // 2. 从当地秘密生成惊喜
    materials.secrets.forEach((secret: LocalSecret, index: number) => {
      elements.push(this.createSurpriseFromSecret(secret, request, index));
    });

    // 3. 使用LLM生成创意惊喜
    const creativeElements = await this.generateCreativeSurprises(request);
    elements.push(...creativeElements);

    // 4. 基于模板生成惊喜
    const templateElements = this.generateTemplateBasedSurprises(request);
    elements.push(...templateElements);

    return elements;
  }

  /**
   * 🎭 个性化惊喜
   */
  private personalizeSuprises(elements: SurpriseElement[], request: SurpriseRequest): SurpriseElement[] {
    return elements
      .filter(element => this.matchesUserProfile(element, request))
      .map(element => this.adaptToUser(element, request))
      .sort((a, b) => b.surpriseLevel - a.surpriseLevel)
      .slice(0, 8); // 最多8个惊喜
  }

  /**
   * 🔧 初始化惊喜模板
   */
  private initializeSurpriseTemplates(): void {
    const templates = {
      serendipity: {
        name: '偶遇惊喜',
        patterns: ['意外发现', '偶然相遇', '巧合体验'],
        emotionalImpact: 0.8,
        timing: 'flexible'
      },
      discovery: {
        name: '发现惊喜',
        patterns: ['隐藏地点', '秘密通道', '未知体验'],
        emotionalImpact: 0.9,
        timing: 'afternoon'
      },
      connection: {
        name: '连接惊喜',
        patterns: ['人际相遇', '文化交流', '情感共鸣'],
        emotionalImpact: 0.85,
        timing: 'evening'
      },
      transformation: {
        name: '转变惊喜',
        patterns: ['技能学习', '视角改变', '成长体验'],
        emotionalImpact: 0.95,
        timing: 'morning'
      },
      revelation: {
        name: '启示惊喜',
        patterns: ['深度理解', '文化洞察', '历史揭秘'],
        emotionalImpact: 0.9,
        timing: 'afternoon'
      },
      adventure: {
        name: '冒险惊喜',
        patterns: ['挑战体验', '勇气考验', '极限突破'],
        emotionalImpact: 0.85,
        timing: 'morning'
      }
    };

    Object.entries(templates).forEach(([type, template]) => {
      this.surpriseTemplates.set(type, template);
    });
  }

  private initializeEmotionalPatterns(): void {
    const patterns = {
      gentle: {
        progression: [0.3, 0.5, 0.6, 0.7, 0.6, 0.5],
        description: '温和渐进的情感体验'
      },
      building: {
        progression: [0.2, 0.4, 0.6, 0.8, 0.9, 0.7],
        description: '逐步升级的惊喜体验'
      },
      climactic: {
        progression: [0.3, 0.4, 0.5, 0.9, 0.6, 0.4],
        description: '高潮式的惊喜体验'
      },
      'wave-like': {
        progression: [0.4, 0.7, 0.5, 0.8, 0.6, 0.9],
        description: '波浪式的情感起伏'
      }
    };

    Object.entries(patterns).forEach(([type, pattern]) => {
      this.emotionalPatterns.set(type, pattern);
    });
  }

  /**
   * 🔧 辅助方法
   */
  private createSurpriseFromGem(gem: HiddenGem, request: SurpriseRequest, index: number): SurpriseElement {
    return {
      id: `gem_surprise_${index}`,
      type: 'discovery',
      name: `发现${gem.name}`,
      description: `意外发现${gem.description}`,
      surpriseLevel: gem.hiddenLevel,
      emotionalImpact: gem.uniqueness,
      memorability: (gem.hiddenLevel + gem.uniqueness) / 2,
      timing: {
        when: 'flexible',
        duration: gem.bestTime.duration,
        preparation: 30
      },
      trigger: {
        condition: '探索当地区域时',
        probability: 0.8,
        dependencies: ['时间充足', '好奇心']
      },
      experience: {
        setup: `在探索${gem.location.district}时`,
        climax: `突然发现了${gem.name}`,
        resolution: gem.discoveryStory,
        afterglow: '留下深刻的发现记忆'
      },
      personalization: {
        userTypes: [request.userProfile.personality],
        interests: request.userProfile.interests,
        adaptability: 0.8
      },
      metadata: {
        creationDate: new Date(),
        source: ['hidden-gems'],
        confidence: gem.metadata.confidence
      }
    };
  }

  private createSurpriseFromSecret(secret: LocalSecret, request: SurpriseRequest, index: number): SurpriseElement {
    return {
      id: `secret_surprise_${index}`,
      type: 'connection',
      name: secret.title,
      description: secret.experience.description,
      surpriseLevel: secret.secretLevel,
      emotionalImpact: secret.authenticity.localApproval,
      memorability: secret.secretLevel,
      timing: {
        when: 'afternoon',
        duration: 120,
        preparation: 60
      },
      trigger: {
        condition: secret.access.howToFind,
        probability: 0.7,
        dependencies: secret.access.prerequisites
      },
      experience: {
        setup: secret.access.howToFind,
        climax: secret.experience.whatMakesItSpecial,
        resolution: secret.experience.emotionalImpact,
        afterglow: '深度的文化连接体验'
      },
      personalization: {
        userTypes: [request.userProfile.personality],
        interests: [secret.category],
        adaptability: 0.7
      },
      metadata: {
        creationDate: new Date(),
        source: ['local-secrets'],
        confidence: secret.metadata.verificationLevel
      }
    };
  }

  private async generateCreativeSurprises(request: SurpriseRequest): Promise<SurpriseElement[]> {
    const prompt = `为${request.destination}的${request.userProfile.personality}类型旅行者设计3个创意惊喜体验。

用户特征：
- 惊喜偏好：${request.userProfile.surprisePreference}
- 冒险程度：${request.userProfile.adventureLevel}
- 兴趣：${request.userProfile.interests.join(', ')}

请设计独特、难忘且符合用户特征的惊喜体验。`;

    try {
      const llmResponse = await this.llmManager.callLLM({
        prompt,
        taskType: 'creative-content',
        context: {
          destination: request.destination,
          complexity: 'high'
        }
      });

      if (llmResponse.success) {
        return this.parseLLMSurprises(llmResponse.content, request);
      }
    } catch (error) {
      console.warn('⚠️ 创意惊喜生成失败:', error);
    }

    return [];
  }

  private generateTemplateBasedSurprises(request: SurpriseRequest): SurpriseElement[] {
    const surprises: SurpriseElement[] = [];
    
    // 基于用户偏好选择模板
    const preferredTypes = this.selectPreferredSurpriseTypes(request);
    
    preferredTypes.forEach((type, index) => {
      const template = this.surpriseTemplates.get(type);
      if (template) {
        surprises.push({
          id: `template_${type}_${index}`,
          type: type as any,
          name: template.name,
          description: `基于${template.name}模板的个性化惊喜`,
          surpriseLevel: 0.7,
          emotionalImpact: template.emotionalImpact,
          memorability: 0.8,
          timing: {
            when: template.timing,
            duration: 90,
            preparation: 30
          },
          trigger: {
            condition: '合适的时机出现',
            probability: 0.6,
            dependencies: ['用户开放度', '环境条件']
          },
          experience: {
            setup: `准备${template.name}体验`,
            climax: `${template.patterns[0]}的高潮时刻`,
            resolution: '完成惊喜体验',
            afterglow: '留下美好回忆'
          },
          personalization: {
            userTypes: [request.userProfile.personality],
            interests: request.userProfile.interests,
            adaptability: 0.9
          },
          metadata: {
            creationDate: new Date(),
            source: ['template'],
            confidence: 0.8
          }
        });
      }
    });

    return surprises;
  }

  private matchesUserProfile(element: SurpriseElement, request: SurpriseRequest): boolean {
    // 惊喜程度匹配
    if (element.surpriseLevel > request.userProfile.surprisePreference + 0.2) return false;
    
    // 冒险程度匹配
    const adventureLevels = { conservative: 0.4, moderate: 0.7, adventurous: 1.0 };
    const userAdventureScore = adventureLevels[request.userProfile.adventureLevel];
    if (element.surpriseLevel > userAdventureScore) return false;
    
    // 兴趣匹配
    const hasInterestMatch = element.personalization.interests.some(interest =>
      request.userProfile.interests.includes(interest)
    );
    
    return hasInterestMatch || element.personalization.adaptability > 0.8;
  }

  private adaptToUser(element: SurpriseElement, request: SurpriseRequest): SurpriseElement {
    const adapted = { ...element };
    
    // 根据用户偏好调整惊喜程度
    adapted.surpriseLevel = Math.min(adapted.surpriseLevel, request.userProfile.surprisePreference);
    
    // 根据情感开放度调整情感影响
    adapted.emotionalImpact *= request.userProfile.emotionalOpenness;
    
    return adapted;
  }

  private buildSurpriseProgression(surprises: SurpriseElement[], request: SurpriseRequest): SurpriseProgression {
    const arcType = this.selectProgressionArc(request);
    const pattern = this.emotionalPatterns.get(arcType);
    
    return {
      arc: arcType as any,
      phases: this.createSurprisePhases(surprises, request),
      emotionalJourney: pattern?.description ? [pattern.description] : [],
      peakMoments: this.identifyPeakMoments(surprises)
    };
  }

  private generateContingencies(surprises: SurpriseElement[], request: SurpriseRequest): SurpriseContingency[] {
    return [
      {
        scenario: '天气不佳',
        alternatives: surprises.filter(s => s.timing.when !== 'flexible'),
        fallbackPlan: '转为室内惊喜体验',
        recoveryStrategy: '调整时间安排，保持惊喜效果'
      },
      {
        scenario: '时间不足',
        alternatives: surprises.filter(s => s.timing.duration <= 60),
        fallbackPlan: '选择快速惊喜体验',
        recoveryStrategy: '集中精力于高影响力惊喜'
      }
    ];
  }

  private mapBudgetRange(budget: number): 'budget' | 'mid-range' | 'luxury' {
    if (budget < 3000) return 'budget';
    if (budget < 8000) return 'mid-range';
    return 'luxury';
  }

  private mapGroupType(companions: string): 'solo' | 'couple' | 'family' | 'group' {
    if (companions.includes('独自')) return 'solo';
    if (companions.includes('情侣') || companions.includes('夫妻')) return 'couple';
    if (companions.includes('家庭') || companions.includes('孩子')) return 'family';
    return 'group';
  }

  private parseLLMSurprises(content: string, request: SurpriseRequest): SurpriseElement[] {
    // 简化的LLM响应解析
    return [{
      id: 'llm_creative_0',
      type: 'serendipity',
      name: 'AI创意惊喜',
      description: content.substring(0, 100),
      surpriseLevel: 0.8,
      emotionalImpact: 0.7,
      memorability: 0.8,
      timing: { when: 'flexible', duration: 90, preparation: 30 },
      trigger: { condition: '合适时机', probability: 0.7, dependencies: [] },
      experience: {
        setup: 'AI设计的惊喜体验',
        climax: '意想不到的精彩时刻',
        resolution: '完美的惊喜结局',
        afterglow: '难忘的回忆'
      },
      personalization: {
        userTypes: [request.userProfile.personality],
        interests: request.userProfile.interests,
        adaptability: 0.9
      },
      metadata: {
        creationDate: new Date(),
        source: ['llm-creative'],
        confidence: 0.7
      }
    }];
  }

  private selectPreferredSurpriseTypes(request: SurpriseRequest): string[] {
    const types = ['serendipity', 'discovery', 'connection'];
    
    if (request.userProfile.adventureLevel === 'adventurous') {
      types.push('adventure', 'transformation');
    }
    
    if (request.userProfile.emotionalOpenness > 0.7) {
      types.push('revelation', 'connection');
    }
    
    return types.slice(0, 3);
  }

  private selectProgressionArc(request: SurpriseRequest): string {
    if (request.userProfile.surprisePreference > 0.8) return 'climactic';
    if (request.userProfile.adventureLevel === 'adventurous') return 'building';
    if (request.constraints.comfortZone < 0.5) return 'gentle';
    return 'wave-like';
  }

  private createSurprisePhases(surprises: SurpriseElement[], request: SurpriseRequest): SurprisePhase[] {
    const phases: SurprisePhase[] = [];
    const duration = request.journeyContext.duration;
    
    if (duration >= 3) {
      phases.push({
        name: '初期探索',
        timeframe: '第1-2天',
        surprises: surprises.slice(0, 2).map(s => s.id),
        emotionalTone: '好奇与期待',
        intensity: 0.6
      });
    }
    
    if (duration >= 5) {
      phases.push({
        name: '深度体验',
        timeframe: '第3-4天',
        surprises: surprises.slice(2, 5).map(s => s.id),
        emotionalTone: '沉浸与发现',
        intensity: 0.8
      });
    }
    
    phases.push({
      name: '难忘收官',
      timeframe: '最后1-2天',
      surprises: surprises.slice(-2).map(s => s.id),
      emotionalTone: '感动与不舍',
      intensity: 0.9
    });
    
    return phases;
  }

  private identifyPeakMoments(surprises: SurpriseElement[]): number[] {
    return surprises
      .map((surprise, index) => ({ index, impact: surprise.emotionalImpact }))
      .sort((a, b) => b.impact - a.impact)
      .slice(0, 2)
      .map(item => item.index);
  }

  private determineTheme(surprises: SurpriseElement[], request: SurpriseRequest): string {
    const typeCount = new Map<string, number>();
    
    surprises.forEach(surprise => {
      typeCount.set(surprise.type, (typeCount.get(surprise.type) || 0) + 1);
    });
    
    const dominantType = Array.from(typeCount.entries())
      .sort(([,a], [,b]) => b - a)[0][0];
    
    const themeMap = {
      serendipity: '偶遇奇缘',
      discovery: '探索发现',
      connection: '深度连接',
      transformation: '成长蜕变',
      revelation: '文化启示',
      adventure: '冒险挑战'
    };
    
    return themeMap[dominantType as keyof typeof themeMap] || '综合体验';
  }

  private calculateOverallImpact(surprises: SurpriseElement[]): number {
    if (surprises.length === 0) return 0;
    return surprises.reduce((sum, s) => sum + s.emotionalImpact, 0) / surprises.length;
  }

  private calculateCoherence(surprises: SurpriseElement[], progression: SurpriseProgression): number {
    // 简化的连贯性计算
    return 0.8;
  }

  private async optimizeSurprisePackage(pkg: SurprisePackage, request: SurpriseRequest): Promise<SurprisePackage> {
    // 简化的优化处理
    return pkg;
  }

  private generateSurprisePackageId(request: SurpriseRequest): string {
    return `surprise_${request.destination}_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`;
  }

  private generateCacheKey(request: SurpriseRequest): string {
    return `surprise_${request.destination}_${request.userProfile.personality}_${request.userProfile.surprisePreference}`;
  }

  private isCacheValid(pkg: SurprisePackage): boolean {
    const hoursSinceGeneration = (Date.now() - pkg.metadata.generationDate.getTime()) / (1000 * 60 * 60);
    return hoursSinceGeneration < 12; // 12小时内有效
  }

  private async cacheSurprisePackage(pkg: SurprisePackage): Promise<void> {
    const cacheKey = `surprise_pkg_${pkg.id}`;
    await this.cacheManager.set(cacheKey, pkg, 'surprise-packages', 12 * 60 * 60 * 1000); // 12小时
  }

  private recordSurpriseHistory(pkg: SurprisePackage): void {
    this.surpriseHistory.push(pkg);
    
    if (this.surpriseHistory.length > 50) {
      this.surpriseHistory = this.surpriseHistory.slice(-25);
    }
  }

  private generateFallbackPackage(request: SurpriseRequest, startTime: number): SurprisePackage {
    return {
      id: `fallback_${Date.now()}`,
      destination: request.destination,
      theme: '基础体验',
      surprises: [],
      overallImpact: 0.3,
      coherence: 0.5,
      progression: {
        arc: 'gentle',
        phases: [],
        emotionalJourney: ['平稳体验'],
        peakMoments: []
      },
      contingencies: [],
      metadata: {
        generationDate: new Date(),
        algorithm: 'fallback',
        version: '3.0',
        personalizedFor: request.userProfile.personality
      }
    };
  }

  /**
   * 📊 获取惊喜统计
   */
  getSurpriseStats() {
    const recentPackages = this.surpriseHistory.slice(-10);
    
    return {
      totalPackages: this.surpriseHistory.length,
      recentPackages: recentPackages.length,
      averageImpact: recentPackages.length > 0 
        ? recentPackages.reduce((sum, pkg) => sum + pkg.overallImpact, 0) / recentPackages.length 
        : 0,
      averageCoherence: recentPackages.length > 0 
        ? recentPackages.reduce((sum, pkg) => sum + pkg.coherence, 0) / recentPackages.length 
        : 0,
      surpriseTemplates: this.surpriseTemplates.size,
      emotionalPatterns: this.emotionalPatterns.size
    };
  }
}
