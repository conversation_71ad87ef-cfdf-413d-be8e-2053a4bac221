/**
 * ✅ Quality Validator - 质量验证器
 * Phase 3: 实时内容生成引擎 - 质量验证和自动优化
 * 实现多维度质量评估和自动优化系统
 */

import { CreativeContent } from './CreativeAI';
import { FusedData } from './RealTimeDataFusion';
import { UltraThinkCacheManager } from '../UltraThinkCacheManager';

// ===== 质量验证接口定义 =====

export interface QualityMetrics {
  accuracy: number; // 0-1, 准确性
  relevance: number; // 0-1, 相关性
  completeness: number; // 0-1, 完整性
  freshness: number; // 0-1, 新鲜度
  uniqueness: number; // 0-1, 独特性
  readability: number; // 0-1, 可读性
  culturalSensitivity: number; // 0-1, 文化敏感性
  practicalValue: number; // 0-1, 实用价值
}

export interface ValidationRequest {
  content: any;
  type: 'creative-content' | 'fused-data' | 'journey-plan' | 'recommendation';
  context: {
    destination: string;
    userProfile?: any;
    requirements?: any;
  };
  qualityThreshold: number; // 0-1, 最低质量要求
  strictMode: boolean; // 是否启用严格模式
}

export interface ValidationResult {
  passed: boolean;
  overallScore: number; // 0-1, 总体质量评分
  metrics: QualityMetrics;
  issues: QualityIssue[];
  suggestions: QualitySuggestion[];
  optimizedContent?: any; // 优化后的内容
  metadata: {
    validationDate: Date;
    validator: string;
    processingTime: number;
    rulesApplied: string[];
  };
}

export interface QualityIssue {
  severity: 'low' | 'medium' | 'high' | 'critical';
  category: 'accuracy' | 'relevance' | 'completeness' | 'readability' | 'cultural' | 'practical';
  description: string;
  location?: string; // 问题位置
  suggestion: string;
  autoFixable: boolean;
}

export interface QualitySuggestion {
  type: 'improvement' | 'enhancement' | 'optimization';
  priority: 'low' | 'medium' | 'high';
  description: string;
  expectedImpact: number; // 0-1, 预期改进效果
  implementationCost: 'low' | 'medium' | 'high';
}

export interface QualityRule {
  id: string;
  name: string;
  category: string;
  weight: number; // 0-1, 规则权重
  enabled: boolean;
  validator: (content: any, context: any) => QualityCheckResult;
}

export interface QualityCheckResult {
  score: number; // 0-1
  passed: boolean;
  issues: QualityIssue[];
  suggestions: QualitySuggestion[];
}

// ===== Quality Validator 核心类 =====

export class QualityValidator {
  private static instance: QualityValidator;
  private cacheManager = UltraThinkCacheManager.getInstance();
  private qualityRules = new Map<string, QualityRule>();
  private validationHistory: ValidationResult[] = [];
  private qualityStandards = new Map<string, any>();

  private constructor() {
    this.initializeQualityRules();
    this.initializeQualityStandards();
    console.log('✅ Quality Validator 初始化完成');
  }

  static getInstance(): QualityValidator {
    if (!QualityValidator.instance) {
      QualityValidator.instance = new QualityValidator();
    }
    return QualityValidator.instance;
  }

  /**
   * 🔍 质量验证 - 主要入口方法
   */
  async validateQuality(request: ValidationRequest): Promise<ValidationResult> {
    const startTime = Date.now();
    console.log(`✅ 开始质量验证: ${request.type} for ${request.context.destination}`);

    try {
      // 1. 预处理和标准化
      const normalizedContent = this.normalizeContent(request.content, request.type);

      // 2. 执行质量检查规则
      const ruleResults = await this.executeQualityRules(normalizedContent, request);
      console.log(`📊 执行${ruleResults.length}个质量规则`);

      // 3. 计算质量指标
      const metrics = this.calculateQualityMetrics(ruleResults, request);

      // 4. 收集问题和建议
      const issues = this.collectIssues(ruleResults);
      const suggestions = this.generateSuggestions(ruleResults, metrics);

      // 5. 计算总体评分
      const overallScore = this.calculateOverallScore(metrics);

      // 6. 判断是否通过验证
      const passed = this.determineValidationResult(overallScore, issues, request);

      // 7. 自动优化（如果需要）
      let optimizedContent = undefined;
      if (!passed && this.canAutoOptimize(issues)) {
        optimizedContent = await this.autoOptimizeContent(normalizedContent, issues, request);
        console.log('🔧 执行自动优化');
      }

      // 8. 构建验证结果
      const result: ValidationResult = {
        passed,
        overallScore,
        metrics,
        issues,
        suggestions,
        optimizedContent,
        metadata: {
          validationDate: new Date(),
          validator: 'ultra-think-quality-validator-v3',
          processingTime: Date.now() - startTime,
          rulesApplied: ruleResults.map(r => r.ruleId)
        }
      };

      // 9. 记录验证历史
      this.recordValidationHistory(result);

      console.log(`✅ 质量验证完成: 评分${overallScore.toFixed(2)}, ${passed ? '通过' : '未通过'}`);
      return result;

    } catch (error) {
      console.error('❌ 质量验证失败:', error);
      return this.generateFallbackResult(request, startTime);
    }
  }

  /**
   * 📏 执行质量规则
   */
  private async executeQualityRules(content: any, request: ValidationRequest): Promise<any[]> {
    const results: any[] = [];
    const applicableRules = this.getApplicableRules(request.type);

    for (const rule of applicableRules) {
      try {
        const ruleResult = rule.validator(content, request.context);
        results.push({
          ruleId: rule.id,
          ruleName: rule.name,
          category: rule.category,
          weight: rule.weight,
          ...ruleResult
        });
      } catch (error) {
        console.warn(`⚠️ 规则${rule.name}执行失败:`, error);
        results.push({
          ruleId: rule.id,
          ruleName: rule.name,
          category: rule.category,
          weight: rule.weight,
          score: 0.5,
          passed: false,
          issues: [{
            severity: 'medium' as const,
            category: 'accuracy' as const,
            description: `规则执行失败: ${rule.name}`,
            suggestion: '请检查内容格式',
            autoFixable: false
          }],
          suggestions: []
        });
      }
    }

    return results;
  }

  /**
   * 📊 计算质量指标
   */
  private calculateQualityMetrics(ruleResults: any[], request: ValidationRequest): QualityMetrics {
    const categoryScores = this.calculateCategoryScores(ruleResults);

    return {
      accuracy: categoryScores.accuracy || 0.7,
      relevance: categoryScores.relevance || 0.7,
      completeness: categoryScores.completeness || 0.7,
      freshness: categoryScores.freshness || 0.8,
      uniqueness: categoryScores.uniqueness || 0.6,
      readability: categoryScores.readability || 0.8,
      culturalSensitivity: categoryScores.cultural || 0.9,
      practicalValue: categoryScores.practical || 0.7
    };
  }

  /**
   * 🔧 初始化质量规则
   */
  private initializeQualityRules(): void {
    const rules: QualityRule[] = [
      // 准确性规则
      {
        id: 'accuracy_basic_info',
        name: '基础信息准确性',
        category: 'accuracy',
        weight: 0.9,
        enabled: true,
        validator: this.validateBasicAccuracy.bind(this)
      },
      {
        id: 'accuracy_location_info',
        name: '位置信息准确性',
        category: 'accuracy',
        weight: 0.8,
        enabled: true,
        validator: this.validateLocationAccuracy.bind(this)
      },

      // 相关性规则
      {
        id: 'relevance_destination',
        name: '目的地相关性',
        category: 'relevance',
        weight: 0.9,
        enabled: true,
        validator: this.validateDestinationRelevance.bind(this)
      },
      {
        id: 'relevance_user_profile',
        name: '用户档案相关性',
        category: 'relevance',
        weight: 0.7,
        enabled: true,
        validator: this.validateUserRelevance.bind(this)
      },

      // 完整性规则
      {
        id: 'completeness_required_fields',
        name: '必需字段完整性',
        category: 'completeness',
        weight: 0.9,
        enabled: true,
        validator: this.validateRequiredFields.bind(this)
      },
      {
        id: 'completeness_detail_level',
        name: '详细程度完整性',
        category: 'completeness',
        weight: 0.6,
        enabled: true,
        validator: this.validateDetailLevel.bind(this)
      },

      // 可读性规则
      {
        id: 'readability_language',
        name: '语言可读性',
        category: 'readability',
        weight: 0.8,
        enabled: true,
        validator: this.validateLanguageReadability.bind(this)
      },
      {
        id: 'readability_structure',
        name: '结构可读性',
        category: 'readability',
        weight: 0.7,
        enabled: true,
        validator: this.validateStructureReadability.bind(this)
      },

      // 文化敏感性规则
      {
        id: 'cultural_sensitivity',
        name: '文化敏感性',
        category: 'cultural',
        weight: 0.9,
        enabled: true,
        validator: this.validateCulturalSensitivity.bind(this)
      },

      // 实用价值规则
      {
        id: 'practical_actionable',
        name: '可操作性',
        category: 'practical',
        weight: 0.8,
        enabled: true,
        validator: this.validateActionability.bind(this)
      }
    ];

    rules.forEach(rule => {
      this.qualityRules.set(rule.id, rule);
    });

    console.log(`🔧 初始化${rules.length}个质量规则`);
  }

  /**
   * 🔧 质量规则验证方法
   */
  private validateBasicAccuracy(content: any, context: any): QualityCheckResult {
    let score = 0.8;
    const issues: QualityIssue[] = [];
    const suggestions: QualitySuggestion[] = [];

    // 检查基本信息是否存在
    if (!content || typeof content !== 'object') {
      score = 0.2;
      issues.push({
        severity: 'critical',
        category: 'accuracy',
        description: '内容格式不正确或为空',
        suggestion: '请提供有效的内容对象',
        autoFixable: false
      });
    }

    // 检查必要字段
    if (content.title && content.title.length < 5) {
      score -= 0.2;
      issues.push({
        severity: 'medium',
        category: 'accuracy',
        description: '标题过短',
        suggestion: '建议标题长度至少5个字符',
        autoFixable: true
      });
    }

    return { score: Math.max(0, score), passed: score >= 0.6, issues, suggestions };
  }

  private validateLocationAccuracy(content: any, context: any): QualityCheckResult {
    let score = 0.7;
    const issues: QualityIssue[] = [];
    const suggestions: QualitySuggestion[] = [];

    const destination = context.destination?.toLowerCase() || '';
    const contentStr = JSON.stringify(content).toLowerCase();

    // 检查是否包含目的地信息
    if (destination && !contentStr.includes(destination)) {
      score -= 0.3;
      issues.push({
        severity: 'medium',
        category: 'accuracy',
        description: '内容中未包含目的地信息',
        suggestion: `建议在内容中明确提及${context.destination}`,
        autoFixable: true
      });
    }

    return { score: Math.max(0, score), passed: score >= 0.5, issues, suggestions };
  }

  private validateDestinationRelevance(content: any, context: any): QualityCheckResult {
    let score = 0.8;
    const issues: QualityIssue[] = [];
    const suggestions: QualitySuggestion[] = [];

    // 简化的相关性检查
    if (context.destination) {
      const contentStr = JSON.stringify(content).toLowerCase();
      const destination = context.destination.toLowerCase();
      
      if (contentStr.includes(destination)) {
        score += 0.1;
      } else {
        score -= 0.2;
        suggestions.push({
          type: 'improvement',
          priority: 'medium',
          description: '增加更多目的地相关的具体信息',
          expectedImpact: 0.2,
          implementationCost: 'low'
        });
      }
    }

    return { score: Math.max(0, score), passed: score >= 0.6, issues, suggestions };
  }

  private validateUserRelevance(content: any, context: any): QualityCheckResult {
    let score = 0.7;
    const issues: QualityIssue[] = [];
    const suggestions: QualitySuggestion[] = [];

    // 如果有用户档案，检查相关性
    if (context.userProfile) {
      // 简化的用户相关性检查
      score += 0.1;
    }

    return { score, passed: score >= 0.5, issues, suggestions };
  }

  private validateRequiredFields(content: any, context: any): QualityCheckResult {
    let score = 0.8;
    const issues: QualityIssue[] = [];
    const suggestions: QualitySuggestion[] = [];

    // 检查必需字段
    const requiredFields = ['title', 'description'];
    const missingFields = requiredFields.filter(field => !content[field]);

    if (missingFields.length > 0) {
      score -= missingFields.length * 0.3;
      issues.push({
        severity: 'high',
        category: 'completeness',
        description: `缺少必需字段: ${missingFields.join(', ')}`,
        suggestion: '请补充所有必需字段',
        autoFixable: true
      });
    }

    return { score: Math.max(0, score), passed: score >= 0.6, issues, suggestions };
  }

  private validateDetailLevel(content: any, context: any): QualityCheckResult {
    let score = 0.7;
    const issues: QualityIssue[] = [];
    const suggestions: QualitySuggestion[] = [];

    // 检查内容详细程度
    const contentStr = JSON.stringify(content);
    if (contentStr.length < 100) {
      score -= 0.2;
      suggestions.push({
        type: 'enhancement',
        priority: 'medium',
        description: '增加更多详细信息和描述',
        expectedImpact: 0.2,
        implementationCost: 'medium'
      });
    }

    return { score, passed: score >= 0.5, issues, suggestions };
  }

  private validateLanguageReadability(content: any, context: any): QualityCheckResult {
    let score = 0.8;
    const issues: QualityIssue[] = [];
    const suggestions: QualitySuggestion[] = [];

    // 简化的语言可读性检查
    if (content.description) {
      const desc = content.description;
      
      // 检查句子长度
      const sentences = desc.split(/[。！？]/).filter((s: string) => s.trim().length > 0);
      const avgLength = sentences.reduce((sum: number, s: string) => sum + s.length, 0) / sentences.length;
      
      if (avgLength > 50) {
        score -= 0.1;
        suggestions.push({
          type: 'improvement',
          priority: 'low',
          description: '考虑使用更短的句子提高可读性',
          expectedImpact: 0.1,
          implementationCost: 'low'
        });
      }
    }

    return { score, passed: score >= 0.6, issues, suggestions };
  }

  private validateStructureReadability(content: any, context: any): QualityCheckResult {
    let score = 0.8;
    const issues: QualityIssue[] = [];
    const suggestions: QualitySuggestion[] = [];

    // 检查内容结构
    if (content.highlights && Array.isArray(content.highlights) && content.highlights.length > 0) {
      score += 0.1;
    } else {
      suggestions.push({
        type: 'enhancement',
        priority: 'low',
        description: '添加要点或亮点信息提高结构性',
        expectedImpact: 0.1,
        implementationCost: 'low'
      });
    }

    return { score, passed: score >= 0.6, issues, suggestions };
  }

  private validateCulturalSensitivity(content: any, context: any): QualityCheckResult {
    let score = 0.9;
    const issues: QualityIssue[] = [];
    const suggestions: QualitySuggestion[] = [];

    // 简化的文化敏感性检查
    const contentStr = JSON.stringify(content).toLowerCase();
    const sensitiveTerms = ['落后', '原始', '奇怪', '野蛮'];
    
    const foundSensitiveTerms = sensitiveTerms.filter(term => contentStr.includes(term));
    if (foundSensitiveTerms.length > 0) {
      score -= 0.3;
      issues.push({
        severity: 'high',
        category: 'cultural',
        description: `包含文化敏感词汇: ${foundSensitiveTerms.join(', ')}`,
        suggestion: '请使用更中性和尊重的表达方式',
        autoFixable: true
      });
    }

    return { score: Math.max(0, score), passed: score >= 0.7, issues, suggestions };
  }

  private validateActionability(content: any, context: any): QualityCheckResult {
    let score = 0.7;
    const issues: QualityIssue[] = [];
    const suggestions: QualitySuggestion[] = [];

    // 检查是否包含可操作的信息
    const contentStr = JSON.stringify(content).toLowerCase();
    const actionableKeywords = ['时间', '地址', '价格', '预订', '开放', '交通'];
    
    const foundKeywords = actionableKeywords.filter(keyword => contentStr.includes(keyword));
    if (foundKeywords.length >= 2) {
      score += 0.2;
    } else {
      suggestions.push({
        type: 'enhancement',
        priority: 'medium',
        description: '添加更多实用信息如时间、地址、价格等',
        expectedImpact: 0.2,
        implementationCost: 'medium'
      });
    }

    return { score, passed: score >= 0.5, issues, suggestions };
  }

  /**
   * 🔧 辅助方法
   */
  private initializeQualityStandards(): void {
    const standards = {
      'creative-content': {
        minScore: 0.7,
        requiredFields: ['content', 'type'],
        maxLength: 1000,
        minLength: 20
      },
      'fused-data': {
        minScore: 0.8,
        requiredFields: ['destination', 'dataPoints'],
        minDataPoints: 1
      },
      'journey-plan': {
        minScore: 0.8,
        requiredFields: ['destination', 'duration', 'activities'],
        minActivities: 3
      }
    };

    Object.entries(standards).forEach(([type, standard]) => {
      this.qualityStandards.set(type, standard);
    });
  }

  private normalizeContent(content: any, type: string): any {
    // 内容标准化处理
    if (typeof content === 'string') {
      return { content, type };
    }
    return content;
  }

  private getApplicableRules(contentType: string): QualityRule[] {
    return Array.from(this.qualityRules.values()).filter(rule => rule.enabled);
  }

  private calculateCategoryScores(ruleResults: any[]): Record<string, number> {
    const categoryScores: Record<string, number> = {};
    const categoryWeights: Record<string, number> = {};

    ruleResults.forEach(result => {
      const category = result.category;
      if (!categoryScores[category]) {
        categoryScores[category] = 0;
        categoryWeights[category] = 0;
      }
      categoryScores[category] += result.score * result.weight;
      categoryWeights[category] += result.weight;
    });

    // 计算加权平均
    Object.keys(categoryScores).forEach(category => {
      if (categoryWeights[category] > 0) {
        categoryScores[category] /= categoryWeights[category];
      }
    });

    return categoryScores;
  }

  private collectIssues(ruleResults: any[]): QualityIssue[] {
    const allIssues: QualityIssue[] = [];
    ruleResults.forEach(result => {
      if (result.issues) {
        allIssues.push(...result.issues);
      }
    });
    return allIssues;
  }

  private generateSuggestions(ruleResults: any[], metrics: QualityMetrics): QualitySuggestion[] {
    const allSuggestions: QualitySuggestion[] = [];
    
    ruleResults.forEach(result => {
      if (result.suggestions) {
        allSuggestions.push(...result.suggestions);
      }
    });

    // 基于指标生成额外建议
    if (metrics.accuracy < 0.7) {
      allSuggestions.push({
        type: 'improvement',
        priority: 'high',
        description: '提高内容准确性，验证所有事实信息',
        expectedImpact: 0.3,
        implementationCost: 'medium'
      });
    }

    return allSuggestions;
  }

  private calculateOverallScore(metrics: QualityMetrics): number {
    const weights = {
      accuracy: 0.25,
      relevance: 0.20,
      completeness: 0.15,
      readability: 0.15,
      culturalSensitivity: 0.10,
      practicalValue: 0.10,
      freshness: 0.03,
      uniqueness: 0.02
    };

    return Object.entries(weights).reduce((score, [metric, weight]) => {
      return score + (metrics[metric as keyof QualityMetrics] * weight);
    }, 0);
  }

  private determineValidationResult(score: number, issues: QualityIssue[], request: ValidationRequest): boolean {
    // 检查是否达到最低分数要求
    if (score < request.qualityThreshold) return false;

    // 检查是否有严重问题
    const criticalIssues = issues.filter(issue => issue.severity === 'critical');
    if (criticalIssues.length > 0) return false;

    // 严格模式下检查高严重性问题
    if (request.strictMode) {
      const highSeverityIssues = issues.filter(issue => issue.severity === 'high');
      if (highSeverityIssues.length > 0) return false;
    }

    return true;
  }

  private canAutoOptimize(issues: QualityIssue[]): boolean {
    return issues.some(issue => issue.autoFixable);
  }

  private async autoOptimizeContent(content: any, issues: QualityIssue[], request: ValidationRequest): Promise<any> {
    let optimized = { ...content };

    // 自动修复可修复的问题
    const fixableIssues = issues.filter(issue => issue.autoFixable);
    
    for (const issue of fixableIssues) {
      optimized = this.applyAutoFix(optimized, issue);
    }

    return optimized;
  }

  private applyAutoFix(content: any, issue: QualityIssue): any {
    const fixed = { ...content };

    switch (issue.category) {
      case 'completeness':
        if (issue.description.includes('缺少必需字段')) {
          if (!fixed.title) fixed.title = '旅行体验';
          if (!fixed.description) fixed.description = '精彩的旅行体验等待您的发现';
        }
        break;
      
      case 'readability':
        if (issue.description.includes('标题过短')) {
          if (fixed.title && fixed.title.length < 5) {
            fixed.title = `精彩${fixed.title}体验`;
          }
        }
        break;
    }

    return fixed;
  }

  private recordValidationHistory(result: ValidationResult): void {
    this.validationHistory.push(result);
    
    // 保持历史记录在合理范围内
    if (this.validationHistory.length > 100) {
      this.validationHistory = this.validationHistory.slice(-50);
    }
  }

  private generateFallbackResult(request: ValidationRequest, startTime: number): ValidationResult {
    return {
      passed: false,
      overallScore: 0.3,
      metrics: {
        accuracy: 0.3,
        relevance: 0.3,
        completeness: 0.3,
        freshness: 0.5,
        uniqueness: 0.3,
        readability: 0.5,
        culturalSensitivity: 0.8,
        practicalValue: 0.3
      },
      issues: [{
        severity: 'critical',
        category: 'accuracy',
        description: '质量验证系统故障',
        suggestion: '请稍后重试',
        autoFixable: false
      }],
      suggestions: [{
        type: 'improvement',
        priority: 'high',
        description: '系统恢复后重新验证',
        expectedImpact: 0.5,
        implementationCost: 'low'
      }],
      metadata: {
        validationDate: new Date(),
        validator: 'fallback',
        processingTime: Date.now() - startTime,
        rulesApplied: []
      }
    };
  }

  /**
   * 📊 获取质量统计
   */
  getQualityStats() {
    const recentValidations = this.validationHistory.slice(-20);
    
    return {
      totalValidations: this.validationHistory.length,
      recentValidations: recentValidations.length,
      averageScore: recentValidations.length > 0 
        ? recentValidations.reduce((sum, v) => sum + v.overallScore, 0) / recentValidations.length 
        : 0,
      passRate: recentValidations.length > 0 
        ? recentValidations.filter(v => v.passed).length / recentValidations.length 
        : 0,
      commonIssues: this.getCommonIssues(recentValidations),
      qualityTrends: recentValidations.map(v => v.overallScore)
    };
  }

  private getCommonIssues(validations: ValidationResult[]): any[] {
    const issueCount: Record<string, number> = {};
    
    validations.forEach(validation => {
      validation.issues.forEach(issue => {
        const key = `${issue.category}:${issue.description}`;
        issueCount[key] = (issueCount[key] || 0) + 1;
      });
    });

    return Object.entries(issueCount)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([issue, count]) => ({ issue, count }));
  }
}
