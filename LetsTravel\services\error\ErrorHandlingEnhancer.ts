/**
 * Trekmate 4.0 - 错误处理增强服务
 * 提供智能错误捕获、分类、恢复和用户友好的错误反馈
 */

import { Alert, ToastAndroid, Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

// ============================================================================
// 错误处理类型定义
// ============================================================================

interface ErrorConfig {
  maxRetries: number;
  retryDelay: number;
  enableLogging: boolean;
  enableCrashReporting: boolean;
  enableUserFeedback: boolean;
  autoRecovery: boolean;
}

interface ErrorContext {
  userId?: string;
  sessionId: string;
  appVersion: string;
  platform: string;
  timestamp: string;
  screenName?: string;
  userAction?: string;
  networkStatus?: string;
  deviceInfo?: any;
}

interface ErrorMetadata {
  id: string;
  type: ErrorType;
  severity: ErrorSeverity;
  category: ErrorCategory;
  message: string;
  stack?: string;
  context: ErrorContext;
  attempts: number;
  resolved: boolean;
  userNotified: boolean;
  timestamp: Date;
}

enum ErrorType {
  NETWORK = 'network',
  VALIDATION = 'validation',
  AUTHENTICATION = 'authentication',
  PERMISSION = 'permission',
  RUNTIME = 'runtime',
  TIMEOUT = 'timeout',
  UNKNOWN = 'unknown'
}

enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

enum ErrorCategory {
  SEARCH = 'search',
  LOCATION = 'location',
  NETWORK = 'network',
  STORAGE = 'storage',
  UI = 'ui',
  SYSTEM = 'system'
}

interface RecoveryStrategy {
  type: 'retry' | 'fallback' | 'redirect' | 'refresh' | 'ignore';
  action?: () => Promise<void>;
  maxAttempts?: number;
  delay?: number;
  fallbackValue?: any;
}

interface ErrorPattern {
  pattern: RegExp;
  type: ErrorType;
  severity: ErrorSeverity;
  category: ErrorCategory;
  recovery: RecoveryStrategy;
  userMessage: string;
}

// ============================================================================
// 错误处理增强服务
// ============================================================================

export class ErrorHandlingEnhancer {
  private config: ErrorConfig;
  private errorHistory: ErrorMetadata[] = [];
  private retryCounters: Map<string, number> = new Map();
  private sessionId: string;
  private errorPatterns: ErrorPattern[] = [];
  private eventListeners: {
    onError?: (error: ErrorMetadata) => void;
    onRecovery?: (error: ErrorMetadata) => void;
    onCriticalError?: (error: ErrorMetadata) => void;
  } = {};

  constructor(config: Partial<ErrorConfig> = {}) {
    this.config = {
      maxRetries: 3,
      retryDelay: 1000,
      enableLogging: true,
      enableCrashReporting: true,
      enableUserFeedback: true,
      autoRecovery: true,
      ...config,
    };
    
    this.sessionId = this.generateSessionId();
    this.initializeErrorPatterns();
    this.setupGlobalErrorHandlers();
  }

  /**
   * 初始化错误模式
   */
  private initializeErrorPatterns(): void {
    this.errorPatterns = [
      // 网络错误
      {
        pattern: /network|fetch|timeout|connection/i,
        type: ErrorType.NETWORK,
        severity: ErrorSeverity.MEDIUM,
        category: ErrorCategory.NETWORK,
        recovery: {
          type: 'retry',
          maxAttempts: 3,
          delay: 2000,
        },
        userMessage: '网络连接不稳定，正在重试...',
      },
      // 权限错误
      {
        pattern: /permission|unauthorized|403|401/i,
        type: ErrorType.PERMISSION,
        severity: ErrorSeverity.HIGH,
        category: ErrorCategory.SYSTEM,
        recovery: {
          type: 'redirect',
          action: () => this.handlePermissionError(),
        },
        userMessage: '需要相关权限才能继续，请检查设置',
      },
      // 位置服务错误
      {
        pattern: /location|gps|geolocation/i,
        type: ErrorType.PERMISSION,
        severity: ErrorSeverity.HIGH,
        category: ErrorCategory.LOCATION,
        recovery: {
          type: 'fallback',
          fallbackValue: null,
        },
        userMessage: '位置服务不可用，部分功能可能受限',
      },
      // 搜索错误
      {
        pattern: /search|query|suggestion/i,
        type: ErrorType.RUNTIME,
        severity: ErrorSeverity.MEDIUM,
        category: ErrorCategory.SEARCH,
        recovery: {
          type: 'fallback',
          fallbackValue: [],
        },
        userMessage: '搜索服务暂时不可用，请稍后重试',
      },
      // 存储错误
      {
        pattern: /storage|asyncstorage|sqlite/i,
        type: ErrorType.RUNTIME,
        severity: ErrorSeverity.HIGH,
        category: ErrorCategory.STORAGE,
        recovery: {
          type: 'retry',
          maxAttempts: 2,
          delay: 1000,
        },
        userMessage: '数据保存失败，正在重试...',
      },
      // 验证错误
      {
        pattern: /validation|invalid|required/i,
        type: ErrorType.VALIDATION,
        severity: ErrorSeverity.LOW,
        category: ErrorCategory.UI,
        recovery: {
          type: 'ignore',
        },
        userMessage: '请检查输入信息是否正确',
      },
    ];
  }

  /**
   * 设置全局错误处理器
   */
  private setupGlobalErrorHandlers(): void {
    // 处理未捕获的Promise rejection
    if (typeof window !== 'undefined' && window.addEventListener) {
      window.addEventListener('unhandledrejection', (event) => {
        this.handleError(event.reason, {
          screenName: 'Unknown',
          userAction: 'Promise Rejection',
        });
      });
    } else {
      // React Native环境下的Promise rejection处理
      const originalPromiseReject = Promise.prototype.catch;
      Promise.prototype.catch = function(onRejected) {
        return originalPromiseReject.call(this, (reason) => {
          if (!onRejected) {
            console.warn('🚨 [RN] 未处理的Promise rejection:', reason);
          }
          return onRejected ? onRejected(reason) : Promise.reject(reason);
        });
      };
    }

    // 处理未捕获的错误
    const originalConsoleError = console.error;
    console.error = (...args) => {
      const error = args[0];
      if (error instanceof Error) {
        this.handleError(error, {
          screenName: 'Console',
          userAction: 'Console Error',
        });
      }
      originalConsoleError.apply(console, args);
    };
  }

  /**
   * 处理错误的主要方法
   */
  async handleError(
    error: Error | string,
    context: Partial<ErrorContext> = {}
  ): Promise<any> {
    try {
      // 创建错误元数据
      const errorMetadata = await this.createErrorMetadata(error, context);
      
      // 记录错误
      if (this.config.enableLogging) {
        await this.logError(errorMetadata);
      }
      
      // 查找匹配的错误模式
      const pattern = this.findMatchingPattern(errorMetadata.message);
      
      // 尝试自动恢复
      if (this.config.autoRecovery && pattern) {
        const recoveryResult = await this.attemptRecovery(errorMetadata, pattern);
        if (recoveryResult.success) {
          return recoveryResult.data;
        }
      }
      
      // 通知用户
      if (this.config.enableUserFeedback) {
        await this.notifyUser(errorMetadata, pattern);
      }
      
      // 触发事件监听器
      this.eventListeners.onError?.(errorMetadata);
      
      if (errorMetadata.severity === ErrorSeverity.CRITICAL) {
        this.eventListeners.onCriticalError?.(errorMetadata);
      }
      
      return null;
      
    } catch (handlingError) {
      console.error('Error handling failed:', handlingError);
      return null;
    }
  }

  /**
   * 创建错误元数据
   */
  private async createErrorMetadata(
    error: Error | string,
    context: Partial<ErrorContext>
  ): Promise<ErrorMetadata> {
    const errorMessage = typeof error === 'string' ? error : error.message;
    const errorStack = typeof error === 'string' ? undefined : error.stack;
    
    const pattern = this.findMatchingPattern(errorMessage);
    
    return {
      id: this.generateErrorId(),
      type: pattern?.type || ErrorType.UNKNOWN,
      severity: pattern?.severity || ErrorSeverity.MEDIUM,
      category: pattern?.category || ErrorCategory.SYSTEM,
      message: errorMessage,
      stack: errorStack,
      context: {
        sessionId: this.sessionId,
        appVersion: '4.0.0',
        platform: Platform.OS,
        timestamp: new Date().toISOString(),
        ...context,
      },
      attempts: 0,
      resolved: false,
      userNotified: false,
      timestamp: new Date(),
    };
  }

  /**
   * 记录错误
   */
  private async logError(errorMetadata: ErrorMetadata): Promise<void> {
    try {
      // 添加到历史记录
      this.errorHistory.push(errorMetadata);
      
      // 限制历史记录数量
      if (this.errorHistory.length > 100) {
        this.errorHistory = this.errorHistory.slice(-50);
      }
      
      // 持久化错误日志
      await this.persistErrorLog(errorMetadata);
      
      // 发送到崩溃报告服务
      if (this.config.enableCrashReporting) {
        await this.reportToCrashService(errorMetadata);
      }
      
    } catch (loggingError) {
      console.error('Error logging failed:', loggingError);
    }
  }

  /**
   * 持久化错误日志
   */
  private async persistErrorLog(errorMetadata: ErrorMetadata): Promise<void> {
    try {
      const storageKey = `error_log_${errorMetadata.id}`;
      const logData = {
        ...errorMetadata,
        timestamp: errorMetadata.timestamp.toISOString(),
      };
      
      await AsyncStorage.setItem(storageKey, JSON.stringify(logData));
      
    } catch (storageError) {
      console.error('Error log persistence failed:', storageError);
    }
  }

  /**
   * 发送到崩溃报告服务
   */
  private async reportToCrashService(errorMetadata: ErrorMetadata): Promise<void> {
    try {
      // 这里可以集成Sentry、Firebase Crashlytics等服务
      console.log('📊 Error reported to crash service:', errorMetadata.id);
      
    } catch (reportingError) {
      console.error('Crash reporting failed:', reportingError);
    }
  }

  /**
   * 查找匹配的错误模式
   */
  private findMatchingPattern(errorMessage: string): ErrorPattern | null {
    return this.errorPatterns.find(pattern => 
      pattern.pattern.test(errorMessage)
    ) || null;
  }

  /**
   * 尝试自动恢复
   */
  private async attemptRecovery(
    errorMetadata: ErrorMetadata,
    pattern: ErrorPattern
  ): Promise<{ success: boolean; data?: any }> {
    try {
      const recovery = pattern.recovery;
      const retryKey = `${errorMetadata.type}_${errorMetadata.category}`;
      
      // 检查重试次数
      const currentRetries = this.retryCounters.get(retryKey) || 0;
      if (recovery.maxAttempts && currentRetries >= recovery.maxAttempts) {
        return { success: false };
      }
      
      // 更新尝试次数
      errorMetadata.attempts = currentRetries + 1;
      this.retryCounters.set(retryKey, errorMetadata.attempts);
      
      switch (recovery.type) {
        case 'retry':
          if (recovery.delay) {
            await new Promise(resolve => setTimeout(resolve, recovery.delay));
          }
          if (recovery.action) {
            await recovery.action();
          }
          return { success: true };
          
        case 'fallback':
          return { success: true, data: recovery.fallbackValue };
          
        case 'redirect':
          if (recovery.action) {
            await recovery.action();
          }
          return { success: true };
          
        case 'refresh':
          // 可以触发页面刷新或重新加载
          return { success: true };
          
        case 'ignore':
          return { success: true };
          
        default:
          return { success: false };
      }
      
    } catch (recoveryError) {
      console.error('Recovery attempt failed:', recoveryError);
      return { success: false };
    }
  }

  /**
   * 通知用户
   */
  private async notifyUser(
    errorMetadata: ErrorMetadata,
    pattern: ErrorPattern | null
  ): Promise<void> {
    try {
      if (errorMetadata.userNotified) return;
      
      const userMessage = pattern?.userMessage || '发生了一个错误，请稍后重试';
      
      // 根据错误严重程度选择通知方式
      if (errorMetadata.severity === ErrorSeverity.CRITICAL) {
        Alert.alert(
          '严重错误',
          userMessage,
          [
            { text: '重试', onPress: () => this.handleRetry(errorMetadata) },
            { text: '取消', style: 'cancel' },
          ]
        );
      } else if (errorMetadata.severity === ErrorSeverity.HIGH) {
        Alert.alert('错误', userMessage, [{ text: '确定' }]);
      } else {
        // 低优先级错误使用Toast
        if (Platform.OS === 'android') {
          ToastAndroid.show(userMessage, ToastAndroid.SHORT);
        } else {
          Alert.alert('提示', userMessage, [{ text: '确定' }]);
        }
      }
      
      errorMetadata.userNotified = true;
      
    } catch (notificationError) {
      console.error('User notification failed:', notificationError);
    }
  }

  /**
   * 处理重试
   */
  private async handleRetry(errorMetadata: ErrorMetadata): Promise<void> {
    const pattern = this.findMatchingPattern(errorMetadata.message);
    if (pattern) {
      const result = await this.attemptRecovery(errorMetadata, pattern);
      if (result.success) {
        errorMetadata.resolved = true;
        this.eventListeners.onRecovery?.(errorMetadata);
      }
    }
  }

  /**
   * 处理权限错误
   */
  private async handlePermissionError(): Promise<void> {
    Alert.alert(
      '权限不足',
      '应用需要相关权限才能正常工作，请前往设置开启权限',
      [
        { text: '稍后', style: 'cancel' },
        { text: '去设置', onPress: () => console.log('打开设置') },
      ]
    );
  }

  /**
   * 生成会话ID
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 生成错误ID
   */
  private generateErrorId(): string {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 添加自定义错误模式
   */
  addErrorPattern(pattern: ErrorPattern): void {
    this.errorPatterns.push(pattern);
  }

  /**
   * 设置事件监听器
   */
  setEventListeners(listeners: Partial<typeof this.eventListeners>): void {
    this.eventListeners = { ...this.eventListeners, ...listeners };
  }

  /**
   * 获取错误历史
   */
  getErrorHistory(): ErrorMetadata[] {
    return [...this.errorHistory];
  }

  /**
   * 获取错误统计
   */
  getErrorStatistics() {
    const stats = {
      totalErrors: this.errorHistory.length,
      errorsByType: {} as Record<string, number>,
      errorsBySeverity: {} as Record<string, number>,
      errorsByCategory: {} as Record<string, number>,
      resolvedErrors: 0,
      unresolvedErrors: 0,
    };

    this.errorHistory.forEach(error => {
      stats.errorsByType[error.type] = (stats.errorsByType[error.type] || 0) + 1;
      stats.errorsBySeverity[error.severity] = (stats.errorsBySeverity[error.severity] || 0) + 1;
      stats.errorsByCategory[error.category] = (stats.errorsByCategory[error.category] || 0) + 1;
      
      if (error.resolved) {
        stats.resolvedErrors++;
      } else {
        stats.unresolvedErrors++;
      }
    });

    return stats;
  }

  /**
   * 清理错误历史
   */
  async clearErrorHistory(): Promise<void> {
    try {
      this.errorHistory = [];
      this.retryCounters.clear();
      
      // 清理持久化的错误日志
      const keys = await AsyncStorage.getAllKeys();
      const errorLogKeys = keys.filter(key => key.startsWith('error_log_'));
      await AsyncStorage.multiRemove(errorLogKeys);
      
    } catch (clearError) {
      console.error('Error history clearing failed:', clearError);
    }
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    this.errorHistory = [];
    this.retryCounters.clear();
    this.eventListeners = {};
    console.log('🧹 错误处理增强服务已清理');
  }
}

// 创建单例实例
export const errorHandlingEnhancer = new ErrorHandlingEnhancer();
export default errorHandlingEnhancer; 