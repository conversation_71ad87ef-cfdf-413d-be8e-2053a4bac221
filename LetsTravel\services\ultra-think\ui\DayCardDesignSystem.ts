/**
 * 📅 Day Card Design System - Day Card设计系统
 * Phase 5: UI简化和体验优化 - Day Card设计系统
 * 实现简化的Day Card设计，包括未展开和展开两种格式
 */

import { UltraThinkUIEngine, RenderRequest, RenderResult } from './UltraThinkUIEngine';
import { UltraThinkCacheManager } from '../UltraThinkCacheManager';

// ===== Day Card设计系统接口定义 =====

export interface DayCardData {
  day: number;
  date: string;
  title: string;
  summary: string;
  activities: ActivityData[];
  weather?: WeatherData;
  budget?: BudgetData;
  highlights: string[];
  transportation: TransportationData[];
  metadata: {
    totalDuration: number;
    difficulty: 'easy' | 'moderate' | 'challenging';
    theme: string;
    season: string;
  };
}

export interface ActivityData {
  id: string;
  title: string;
  description: string;
  type: 'transport' | 'food' | 'culture' | 'nature' | 'shopping' | 'entertainment';
  startTime: string;
  endTime: string;
  duration: number;
  location: {
    name: string;
    address: string;
    coordinates?: { lat: number; lng: number };
  };
  cost?: number;
  priority: 'high' | 'medium' | 'low';
  bookingRequired: boolean;
  tips?: string[];
}

export interface WeatherData {
  temperature: { min: number; max: number };
  condition: string;
  icon: string;
  humidity: number;
  precipitation: number;
}

export interface BudgetData {
  total: number;
  breakdown: {
    transport: number;
    food: number;
    activities: number;
    shopping: number;
  };
  currency: string;
}

export interface TransportationData {
  from: string;
  to: string;
  method: string;
  duration: number;
  cost?: number;
  notes?: string;
}

export interface DayCardConfig {
  variant: 'compact' | 'detailed' | 'timeline';
  expandable: boolean;
  showWeather: boolean;
  showBudget: boolean;
  showTransport: boolean;
  colorScheme: 'auto' | 'light' | 'dark';
  animations: boolean;
  accessibility: boolean;
}

export interface DayCardState {
  expanded: boolean;
  selectedActivity?: string;
  viewMode: 'overview' | 'timeline' | 'map';
  filters: {
    activityTypes: string[];
    timeRange?: { start: string; end: string };
  };
}

// ===== Day Card Design System 核心类 =====

export class DayCardDesignSystem {
  private static instance: DayCardDesignSystem;
  private uiEngine = UltraThinkUIEngine.getInstance();
  private cacheManager = UltraThinkCacheManager.getInstance();
  private cardTemplates = new Map<string, any>();
  private animationPresets = new Map<string, any>();
  private layoutPatterns = new Map<string, any>();

  private constructor() {
    this.initializeCardTemplates();
    this.initializeAnimationPresets();
    this.initializeLayoutPatterns();
    console.log('📅 Day Card Design System 初始化完成');
  }

  static getInstance(): DayCardDesignSystem {
    if (!DayCardDesignSystem.instance) {
      DayCardDesignSystem.instance = new DayCardDesignSystem();
    }
    return DayCardDesignSystem.instance;
  }

  /**
   * 🎨 渲染Day Card - 主要入口方法
   */
  async renderDayCard(data: DayCardData, config: DayCardConfig, state?: DayCardState): Promise<RenderResult> {
    console.log(`📅 开始渲染Day Card: 第${data.day}天`);

    try {
      // 1. 预处理数据
      const processedData = this.preprocessData(data, config);

      // 2. 选择模板
      const template = this.selectTemplate(config.variant, state?.expanded || false);

      // 3. 生成HTML结构
      const html = this.generateHTML(processedData, template, config, state);

      // 4. 生成CSS样式
      const css = this.generateCSS(template, config);

      // 5. 生成JavaScript交互
      const javascript = this.generateJavaScript(config, state);

      // 6. 构建渲染结果
      const result: RenderResult = {
        html,
        css,
        javascript,
        metadata: {
          component: 'day-card',
          theme: config.colorScheme,
          renderTime: Date.now(),
          cacheHit: false
        }
      };

      console.log(`✅ Day Card渲染完成: 第${data.day}天`);
      return result;

    } catch (error) {
      console.error('❌ Day Card渲染失败:', error);
      return this.generateFallbackCard(data, config);
    }
  }

  /**
   * 📱 渲染紧凑版Day Card
   */
  async renderCompactCard(data: DayCardData, config: Partial<DayCardConfig> = {}): Promise<RenderResult> {
    const compactConfig: DayCardConfig = {
      variant: 'compact',
      expandable: true,
      showWeather: true,
      showBudget: false,
      showTransport: false,
      colorScheme: 'auto',
      animations: true,
      accessibility: true,
      ...config
    };

    return this.renderDayCard(data, compactConfig);
  }

  /**
   * 📋 渲染详细版Day Card
   */
  async renderDetailedCard(data: DayCardData, config: Partial<DayCardConfig> = {}): Promise<RenderResult> {
    const detailedConfig: DayCardConfig = {
      variant: 'detailed',
      expandable: false,
      showWeather: true,
      showBudget: true,
      showTransport: true,
      colorScheme: 'auto',
      animations: true,
      accessibility: true,
      ...config
    };

    return this.renderDayCard(data, detailedConfig);
  }

  /**
   * ⏰ 渲染时间线版Day Card
   */
  async renderTimelineCard(data: DayCardData, config: Partial<DayCardConfig> = {}): Promise<RenderResult> {
    const timelineConfig: DayCardConfig = {
      variant: 'timeline',
      expandable: true,
      showWeather: false,
      showBudget: false,
      showTransport: true,
      colorScheme: 'auto',
      animations: true,
      accessibility: true,
      ...config
    };

    return this.renderDayCard(data, timelineConfig);
  }

  /**
   * 🔧 初始化卡片模板
   */
  private initializeCardTemplates(): void {
    const compactTemplate = {
      name: 'compact',
      structure: {
        header: ['day', 'title', 'weather', 'expand-button'],
        body: ['summary', 'highlights'],
        expandedBody: ['activities', 'transportation', 'budget'],
        footer: ['duration', 'difficulty']
      },
      layout: 'vertical',
      maxHeight: '120px',
      expandedMaxHeight: 'auto'
    };

    const detailedTemplate = {
      name: 'detailed',
      structure: {
        header: ['day', 'title', 'date', 'weather'],
        body: ['summary', 'activities', 'transportation'],
        sidebar: ['budget', 'highlights', 'tips'],
        footer: ['metadata']
      },
      layout: 'horizontal',
      maxHeight: 'auto',
      expandedMaxHeight: 'auto'
    };

    const timelineTemplate = {
      name: 'timeline',
      structure: {
        header: ['day', 'title', 'date'],
        body: ['timeline-activities'],
        sidebar: ['transportation-flow'],
        footer: ['summary']
      },
      layout: 'timeline',
      maxHeight: 'auto',
      expandedMaxHeight: 'auto'
    };

    this.cardTemplates.set('compact', compactTemplate);
    this.cardTemplates.set('detailed', detailedTemplate);
    this.cardTemplates.set('timeline', timelineTemplate);
  }

  private initializeAnimationPresets(): void {
    const animations = {
      expand: {
        duration: '0.3s',
        easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
        properties: ['height', 'opacity', 'transform']
      },
      collapse: {
        duration: '0.2s',
        easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
        properties: ['height', 'opacity', 'transform']
      },
      fadeIn: {
        duration: '0.2s',
        easing: 'ease-out',
        properties: ['opacity', 'transform']
      },
      slideIn: {
        duration: '0.3s',
        easing: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
        properties: ['transform', 'opacity']
      }
    };

    Object.entries(animations).forEach(([name, preset]) => {
      this.animationPresets.set(name, preset);
    });
  }

  private initializeLayoutPatterns(): void {
    const patterns = {
      vertical: {
        direction: 'column',
        spacing: 'var(--spacing-md)',
        alignment: 'stretch'
      },
      horizontal: {
        direction: 'row',
        spacing: 'var(--spacing-lg)',
        alignment: 'flex-start'
      },
      timeline: {
        direction: 'column',
        spacing: 'var(--spacing-sm)',
        alignment: 'stretch',
        special: 'timeline-layout'
      }
    };

    Object.entries(patterns).forEach(([name, pattern]) => {
      this.layoutPatterns.set(name, pattern);
    });
  }

  /**
   * 🔧 辅助方法
   */
  private preprocessData(data: DayCardData, config: DayCardConfig): DayCardData {
    const processed = { ...data };

    // 排序活动
    processed.activities = processed.activities.sort((a, b) => 
      a.startTime.localeCompare(b.startTime)
    );

    // 过滤活动（如果有过滤器）
    if (config.variant === 'compact') {
      processed.activities = processed.activities.filter(a => a.priority === 'high').slice(0, 3);
    }

    // 生成摘要
    if (!processed.summary) {
      processed.summary = this.generateSummary(processed);
    }

    return processed;
  }

  private selectTemplate(variant: string, expanded: boolean): any {
    const template = this.cardTemplates.get(variant) || this.cardTemplates.get('compact');
    
    if (expanded && template.expandedStructure) {
      return { ...template, structure: template.expandedStructure };
    }
    
    return template;
  }

  private generateHTML(data: DayCardData, template: any, config: DayCardConfig, state?: DayCardState): string {
    const cardClass = `day-card day-card--${template.name}${state?.expanded ? ' day-card--expanded' : ''}`;
    const accessibilityAttrs = config.accessibility ? this.generateAccessibilityAttributes(data) : '';

    return `
      <div class="${cardClass}" data-day="${data.day}" ${accessibilityAttrs}>
        ${this.renderHeader(data, template, config)}
        ${this.renderBody(data, template, config, state)}
        ${template.structure.sidebar ? this.renderSidebar(data, template, config) : ''}
        ${template.structure.footer ? this.renderFooter(data, template, config) : ''}
        ${config.expandable ? this.renderExpandButton(state?.expanded || false) : ''}
      </div>
    `;
  }

  private renderHeader(data: DayCardData, template: any, config: DayCardConfig): string {
    const headerElements = template.structure.header || [];
    
    return `
      <div class="day-card__header">
        ${headerElements.includes('day') ? `<div class="day-card__day">第${data.day}天</div>` : ''}
        ${headerElements.includes('title') ? `<h3 class="day-card__title">${data.title}</h3>` : ''}
        ${headerElements.includes('date') ? `<div class="day-card__date">${data.date}</div>` : ''}
        ${headerElements.includes('weather') && config.showWeather && data.weather ? 
          `<div class="day-card__weather">
            <span class="weather-icon">${data.weather.icon}</span>
            <span class="weather-temp">${data.weather.temperature.max}°</span>
          </div>` : ''}
      </div>
    `;
  }

  private renderBody(data: DayCardData, template: any, config: DayCardConfig, state?: DayCardState): string {
    const bodyElements = template.structure.body || [];
    
    let bodyHTML = '<div class="day-card__body">';
    
    if (bodyElements.includes('summary')) {
      bodyHTML += `<p class="day-card__summary">${data.summary}</p>`;
    }
    
    if (bodyElements.includes('highlights')) {
      bodyHTML += `
        <div class="day-card__highlights">
          ${data.highlights.map(highlight => `<span class="highlight-tag">${highlight}</span>`).join('')}
        </div>
      `;
    }
    
    if (bodyElements.includes('activities')) {
      bodyHTML += this.renderActivities(data.activities, template, config);
    }
    
    if (bodyElements.includes('timeline-activities')) {
      bodyHTML += this.renderTimelineActivities(data.activities, data.transportation, config);
    }
    
    if (bodyElements.includes('transportation') && config.showTransport) {
      bodyHTML += this.renderTransportation(data.transportation);
    }
    
    bodyHTML += '</div>';
    
    return bodyHTML;
  }

  private renderActivities(activities: ActivityData[], template: any, config: DayCardConfig): string {
    return `
      <div class="day-card__activities">
        ${activities.map(activity => `
          <div class="activity-item" data-type="${activity.type}">
            <div class="activity-time">${activity.startTime}</div>
            <div class="activity-content">
              <h4 class="activity-title">${activity.title}</h4>
              <p class="activity-description">${activity.description}</p>
              ${activity.location ? `<span class="activity-location">${activity.location.name}</span>` : ''}
            </div>
            <div class="activity-duration">${activity.duration}分钟</div>
          </div>
        `).join('')}
      </div>
    `;
  }

  private renderTimelineActivities(activities: ActivityData[], transportation: TransportationData[], config: DayCardConfig): string {
    const timelineItems: any[] = [];
    
    // 合并活动和交通
    activities.forEach(activity => {
      timelineItems.push({ type: 'activity', data: activity, time: activity.startTime });
    });
    
    transportation.forEach(transport => {
      timelineItems.push({ type: 'transport', data: transport, time: '00:00' }); // 简化处理
    });
    
    timelineItems.sort((a, b) => a.time.localeCompare(b.time));
    
    return `
      <div class="day-card__timeline">
        ${timelineItems.map((item, index) => `
          <div class="timeline-item timeline-item--${item.type}">
            <div class="timeline-marker"></div>
            <div class="timeline-content">
              ${item.type === 'activity' ? 
                `<h4>${item.data.title}</h4><p>${item.data.description}</p>` :
                `<div class="transport-info">${item.data.method}: ${item.data.from} → ${item.data.to}</div>`
              }
            </div>
            <div class="timeline-time">${item.time}</div>
          </div>
        `).join('')}
      </div>
    `;
  }

  private renderSidebar(data: DayCardData, template: any, config: DayCardConfig): string {
    const sidebarElements = template.structure.sidebar || [];
    
    let sidebarHTML = '<div class="day-card__sidebar">';
    
    if (sidebarElements.includes('budget') && config.showBudget && data.budget) {
      sidebarHTML += `
        <div class="budget-info">
          <h4>预算</h4>
          <div class="budget-total">${data.budget.total} ${data.budget.currency}</div>
          <div class="budget-breakdown">
            <div>交通: ${data.budget.breakdown.transport}</div>
            <div>餐饮: ${data.budget.breakdown.food}</div>
            <div>活动: ${data.budget.breakdown.activities}</div>
          </div>
        </div>
      `;
    }
    
    if (sidebarElements.includes('highlights')) {
      sidebarHTML += `
        <div class="highlights-info">
          <h4>亮点</h4>
          ${data.highlights.map(highlight => `<div class="highlight-item">${highlight}</div>`).join('')}
        </div>
      `;
    }
    
    sidebarHTML += '</div>';
    
    return sidebarHTML;
  }

  private renderFooter(data: DayCardData, template: any, config: DayCardConfig): string {
    return `
      <div class="day-card__footer">
        <div class="metadata">
          <span class="duration">${data.metadata.totalDuration}小时</span>
          <span class="difficulty difficulty--${data.metadata.difficulty}">${data.metadata.difficulty}</span>
          <span class="theme">${data.metadata.theme}</span>
        </div>
      </div>
    `;
  }

  private renderTransportation(transportation: TransportationData[]): string {
    return `
      <div class="day-card__transportation">
        <h4>交通</h4>
        ${transportation.map(transport => `
          <div class="transport-item">
            <span class="transport-method">${transport.method}</span>
            <span class="transport-route">${transport.from} → ${transport.to}</span>
            <span class="transport-duration">${transport.duration}分钟</span>
          </div>
        `).join('')}
      </div>
    `;
  }

  private renderExpandButton(expanded: boolean): string {
    return `
      <button class="day-card__expand-btn" aria-label="${expanded ? '收起' : '展开'}详情">
        <span class="expand-icon ${expanded ? 'expanded' : ''}">${expanded ? '▲' : '▼'}</span>
      </button>
    `;
  }

  private generateCSS(template: any, config: DayCardConfig): string {
    const layout = this.layoutPatterns.get(template.layout) || this.layoutPatterns.get('vertical');
    
    return `
      .day-card {
        background: var(--surface);
        border-radius: var(--border-radius-lg);
        box-shadow: var(--shadow-md);
        padding: var(--spacing-lg);
        margin-bottom: var(--spacing-md);
        transition: all 0.3s ease;
        max-height: ${template.maxHeight};
        overflow: hidden;
      }
      
      .day-card--expanded {
        max-height: ${template.expandedMaxHeight};
      }
      
      .day-card__header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--spacing-md);
      }
      
      .day-card__title {
        font-size: var(--font-size-xl);
        font-weight: var(--font-weight-semibold);
        color: var(--text-primary);
        margin: 0;
      }
      
      .day-card__day {
        background: var(--color-primary);
        color: white;
        padding: var(--spacing-xs) var(--spacing-sm);
        border-radius: var(--border-radius-full);
        font-size: var(--font-size-sm);
        font-weight: var(--font-weight-medium);
      }
      
      .day-card__body {
        display: flex;
        flex-direction: ${layout.direction};
        gap: ${layout.spacing};
        align-items: ${layout.alignment};
      }
      
      .activity-item {
        display: flex;
        align-items: center;
        padding: var(--spacing-sm);
        background: var(--background);
        border-radius: var(--border-radius-md);
        margin-bottom: var(--spacing-sm);
      }
      
      .timeline-item {
        position: relative;
        padding-left: var(--spacing-lg);
        padding-bottom: var(--spacing-md);
        border-left: 2px solid var(--color-secondary);
      }
      
      .timeline-marker {
        position: absolute;
        left: -6px;
        top: 4px;
        width: 10px;
        height: 10px;
        background: var(--color-primary);
        border-radius: 50%;
      }
      
      .day-card__expand-btn {
        position: absolute;
        top: var(--spacing-md);
        right: var(--spacing-md);
        background: none;
        border: none;
        cursor: pointer;
        padding: var(--spacing-xs);
        border-radius: var(--border-radius-sm);
        transition: background-color 0.2s ease;
      }
      
      .day-card__expand-btn:hover {
        background: var(--color-secondary);
      }
      
      .expand-icon {
        transition: transform 0.3s ease;
      }
      
      .expand-icon.expanded {
        transform: rotate(180deg);
      }
      
      ${config.animations ? this.generateAnimationCSS() : ''}
      ${this.generateResponsiveCSS()}
    `;
  }

  private generateAnimationCSS(): string {
    return `
      @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
      }
      
      @keyframes slideUp {
        from { transform: translateY(20px); opacity: 0; }
        to { transform: translateY(0); opacity: 1; }
      }
      
      .day-card {
        animation: fadeIn 0.3s ease-out;
      }
      
      .activity-item {
        animation: slideUp 0.2s ease-out;
        animation-fill-mode: both;
      }
      
      .activity-item:nth-child(1) { animation-delay: 0.1s; }
      .activity-item:nth-child(2) { animation-delay: 0.2s; }
      .activity-item:nth-child(3) { animation-delay: 0.3s; }
    `;
  }

  private generateResponsiveCSS(): string {
    return `
      @media (max-width: 768px) {
        .day-card {
          padding: var(--spacing-md);
          margin-bottom: var(--spacing-sm);
        }
        
        .day-card__header {
          flex-direction: column;
          align-items: flex-start;
          gap: var(--spacing-sm);
        }
        
        .day-card__body {
          flex-direction: column;
        }
        
        .activity-item {
          flex-direction: column;
          align-items: flex-start;
        }
      }
      
      @media (min-width: 1025px) {
        .day-card--detailed .day-card__body {
          display: grid;
          grid-template-columns: 2fr 1fr;
          gap: var(--spacing-xl);
        }
      }
    `;
  }

  private generateJavaScript(config: DayCardConfig, state?: DayCardState): string {
    if (!config.expandable) return '';
    
    return `
      document.querySelectorAll('.day-card__expand-btn').forEach(btn => {
        btn.addEventListener('click', function() {
          const card = this.closest('.day-card');
          const isExpanded = card.classList.contains('day-card--expanded');
          
          card.classList.toggle('day-card--expanded');
          
          const icon = this.querySelector('.expand-icon');
          icon.classList.toggle('expanded');
          
          this.setAttribute('aria-label', isExpanded ? '展开详情' : '收起详情');
          
          // 触发自定义事件
          card.dispatchEvent(new CustomEvent('daycard:toggle', {
            detail: { expanded: !isExpanded, day: card.dataset.day }
          }));
        });
      });
      
      // 活动项点击事件
      document.querySelectorAll('.activity-item').forEach(item => {
        item.addEventListener('click', function() {
          this.classList.toggle('activity-item--selected');
          
          // 触发自定义事件
          this.dispatchEvent(new CustomEvent('activity:select', {
            detail: { activityId: this.dataset.id }
          }));
        });
      });
    `;
  }

  private generateSummary(data: DayCardData): string {
    const activityCount = data.activities.length;
    const mainTypes = [...new Set(data.activities.map(a => a.type))];
    
    return `今天安排了${activityCount}个活动，主要包括${mainTypes.join('、')}等体验。`;
  }

  private generateAccessibilityAttributes(data: DayCardData): string {
    return `
      role="article"
      aria-label="第${data.day}天行程：${data.title}"
      tabindex="0"
    `.trim();
  }

  private generateFallbackCard(data: DayCardData, config: DayCardConfig): RenderResult {
    return {
      html: `
        <div class="day-card day-card--fallback">
          <div class="day-card__header">
            <h3>第${data.day}天 - ${data.title}</h3>
          </div>
          <div class="day-card__body">
            <p>卡片渲染失败，请稍后重试</p>
          </div>
        </div>
      `,
      css: `
        .day-card--fallback {
          background: #fee;
          border: 1px solid #fcc;
          padding: 1rem;
          border-radius: 0.5rem;
        }
      `,
      javascript: '',
      metadata: {
        component: 'day-card',
        theme: 'fallback',
        renderTime: Date.now(),
        cacheHit: false
      }
    };
  }

  /**
   * 📊 获取设计系统统计
   */
  getDesignSystemStats() {
    return {
      cardTemplates: this.cardTemplates.size,
      animationPresets: this.animationPresets.size,
      layoutPatterns: this.layoutPatterns.size,
      supportedVariants: Array.from(this.cardTemplates.keys()),
      supportedLayouts: Array.from(this.layoutPatterns.keys())
    };
  }
}
