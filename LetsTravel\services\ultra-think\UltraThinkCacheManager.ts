/**
 * 💾 Ultra Think Cache Manager - 数据持久化和缓存系统
 * Phase 1: 核心架构重构 - 智能缓存策略
 * 实现用户偏好缓存、API响应缓存和实时数据缓存
 */

import { ultraThinkConfig } from '../../config/UltraThinkConfig';

// ===== 缓存系统接口定义 =====

export interface CacheItem<T = any> {
  key: string;
  data: T;
  timestamp: number;
  ttl: number;
  accessCount: number;
  lastAccessed: number;
  metadata: {
    source: string;
    version: string;
    compressed: boolean;
    encrypted: boolean;
    size: number;
  };
}

export interface CacheStrategy {
  name: string;
  storage: 'memory' | 'localStorage' | 'indexedDB' | 'hybrid';
  ttl: number;
  maxSize: number;
  compression: boolean;
  encryption: boolean;
  evictionPolicy: 'lru' | 'lfu' | 'ttl' | 'fifo';
}

export interface CacheStats {
  totalItems: number;
  totalSize: number;
  hitRate: number;
  missRate: number;
  evictionCount: number;
  storageUsage: {
    memory: number;
    localStorage: number;
    indexedDB: number;
  };
}

export interface CacheOperation {
  operation: 'get' | 'set' | 'delete' | 'clear';
  key: string;
  success: boolean;
  executionTime: number;
  cacheHit?: boolean;
  dataSize?: number;
}

// ===== Ultra Think Cache Manager 核心类 =====

export class UltraThinkCacheManager {
  private static instance: UltraThinkCacheManager;
  private config = ultraThinkConfig.getConfig();
  private memoryCache = new Map<string, CacheItem>();
  private cacheStrategies = new Map<string, CacheStrategy>();
  private operationHistory: CacheOperation[] = [];
  private stats: CacheStats;

  private constructor() {
    this.initializeCacheStrategies();
    this.initializeStats();
    this.startCacheCleanup();
    console.log('💾 Ultra Think Cache Manager 初始化完成');
  }

  static getInstance(): UltraThinkCacheManager {
    if (!UltraThinkCacheManager.instance) {
      UltraThinkCacheManager.instance = new UltraThinkCacheManager();
    }
    return UltraThinkCacheManager.instance;
  }

  /**
   * 📥 获取缓存数据
   */
  async get<T>(key: string, category: string = 'default'): Promise<T | null> {
    const startTime = Date.now();
    console.log(`💾 获取缓存: ${key} (类别: ${category})`);

    try {
      const strategy = this.getCacheStrategy(category);
      let cacheItem: CacheItem<T> | null = null;

      // 1. 检查内存缓存
      if (strategy.storage === 'memory' || strategy.storage === 'hybrid') {
        cacheItem = this.getFromMemory<T>(key);
      }

      // 2. 检查localStorage缓存
      if (!cacheItem && (strategy.storage === 'localStorage' || strategy.storage === 'hybrid')) {
        cacheItem = await this.getFromLocalStorage<T>(key);
        
        // 如果找到，加载到内存缓存
        if (cacheItem && strategy.storage === 'hybrid') {
          this.memoryCache.set(key, cacheItem);
        }
      }

      // 3. 检查IndexedDB缓存
      if (!cacheItem && (strategy.storage === 'indexedDB' || strategy.storage === 'hybrid')) {
        cacheItem = await this.getFromIndexedDB<T>(key);
        
        // 如果找到，加载到内存缓存
        if (cacheItem && strategy.storage === 'hybrid') {
          this.memoryCache.set(key, cacheItem);
        }
      }

      // 4. 验证缓存有效性
      if (cacheItem && this.isCacheValid(cacheItem)) {
        // 更新访问统计
        cacheItem.accessCount++;
        cacheItem.lastAccessed = Date.now();
        
        this.recordOperation({
          operation: 'get',
          key,
          success: true,
          executionTime: Date.now() - startTime,
          cacheHit: true,
          dataSize: cacheItem.metadata.size
        });

        console.log(`✅ 缓存命中: ${key}`);
        return cacheItem.data;
      }

      // 5. 缓存未命中或已过期
      if (cacheItem && !this.isCacheValid(cacheItem)) {
        console.log(`⏰ 缓存过期: ${key}`);
        await this.delete(key, category);
      }

      this.recordOperation({
        operation: 'get',
        key,
        success: false,
        executionTime: Date.now() - startTime,
        cacheHit: false
      });

      console.log(`❌ 缓存未命中: ${key}`);
      return null;

    } catch (error) {
      console.error('❌ 缓存获取失败:', error);
      this.recordOperation({
        operation: 'get',
        key,
        success: false,
        executionTime: Date.now() - startTime,
        cacheHit: false
      });
      return null;
    }
  }

  /**
   * 📤 设置缓存数据
   */
  async set<T>(key: string, data: T, category: string = 'default', customTTL?: number): Promise<boolean> {
    const startTime = Date.now();
    console.log(`💾 设置缓存: ${key} (类别: ${category})`);

    try {
      const strategy = this.getCacheStrategy(category);
      const serializedData = JSON.stringify(data);
      const dataSize = new Blob([serializedData]).size;

      // 检查缓存大小限制
      if (dataSize > strategy.maxSize) {
        console.warn(`⚠️ 数据过大，跳过缓存: ${key} (${dataSize} bytes > ${strategy.maxSize} bytes)`);
        return false;
      }

      const cacheItem: CacheItem<T> = {
        key,
        data,
        timestamp: Date.now(),
        ttl: customTTL || strategy.ttl,
        accessCount: 0,
        lastAccessed: Date.now(),
        metadata: {
          source: 'ultra-think',
          version: '1.0',
          compressed: strategy.compression,
          encrypted: strategy.encryption,
          size: dataSize
        }
      };

      // 数据压缩
      if (strategy.compression) {
        cacheItem.data = this.compressData(cacheItem.data) as T;
      }

      // 数据加密
      if (strategy.encryption) {
        cacheItem.data = this.encryptData(cacheItem.data) as T;
      }

      let success = false;

      // 1. 存储到内存缓存
      if (strategy.storage === 'memory' || strategy.storage === 'hybrid') {
        this.memoryCache.set(key, cacheItem);
        success = true;
      }

      // 2. 存储到localStorage
      if (strategy.storage === 'localStorage' || strategy.storage === 'hybrid') {
        success = await this.setToLocalStorage(key, cacheItem) || success;
      }

      // 3. 存储到IndexedDB
      if (strategy.storage === 'indexedDB' || strategy.storage === 'hybrid') {
        success = await this.setToIndexedDB(key, cacheItem) || success;
      }

      // 4. 检查缓存容量并执行清理
      await this.enforceCapacityLimits(strategy);

      this.recordOperation({
        operation: 'set',
        key,
        success,
        executionTime: Date.now() - startTime,
        dataSize
      });

      if (success) {
        console.log(`✅ 缓存设置成功: ${key}`);
      } else {
        console.warn(`⚠️ 缓存设置失败: ${key}`);
      }

      return success;

    } catch (error) {
      console.error('❌ 缓存设置失败:', error);
      this.recordOperation({
        operation: 'set',
        key,
        success: false,
        executionTime: Date.now() - startTime
      });
      return false;
    }
  }

  /**
   * 🗑️ 删除缓存数据
   */
  async delete(key: string, category: string = 'default'): Promise<boolean> {
    const startTime = Date.now();
    console.log(`💾 删除缓存: ${key}`);

    try {
      const strategy = this.getCacheStrategy(category);
      let success = false;

      // 1. 从内存缓存删除
      if (strategy.storage === 'memory' || strategy.storage === 'hybrid') {
        success = this.memoryCache.delete(key) || success;
      }

      // 2. 从localStorage删除
      if (strategy.storage === 'localStorage' || strategy.storage === 'hybrid') {
        success = this.deleteFromLocalStorage(key) || success;
      }

      // 3. 从IndexedDB删除
      if (strategy.storage === 'indexedDB' || strategy.storage === 'hybrid') {
        success = await this.deleteFromIndexedDB(key) || success;
      }

      this.recordOperation({
        operation: 'delete',
        key,
        success,
        executionTime: Date.now() - startTime
      });

      return success;

    } catch (error) {
      console.error('❌ 缓存删除失败:', error);
      return false;
    }
  }

  /**
   * 🧹 清理过期缓存
   */
  async cleanup(): Promise<void> {
    console.log('🧹 开始缓存清理');
    const startTime = Date.now();
    let cleanedCount = 0;

    // 清理内存缓存
    for (const [key, item] of this.memoryCache.entries()) {
      if (!this.isCacheValid(item)) {
        this.memoryCache.delete(key);
        cleanedCount++;
      }
    }

    // 清理localStorage缓存
    cleanedCount += await this.cleanupLocalStorage();

    // 清理IndexedDB缓存
    cleanedCount += await this.cleanupIndexedDB();

    const executionTime = Date.now() - startTime;
    console.log(`✅ 缓存清理完成: 清理${cleanedCount}项，耗时${executionTime}ms`);

    // 更新统计信息
    this.updateStats();
  }

  /**
   * 🔧 初始化缓存策略
   */
  private initializeCacheStrategies(): void {
    const strategies: Array<[string, CacheStrategy]> = [
      ['user-preferences', {
        name: '用户偏好缓存',
        storage: 'localStorage',
        ttl: 30 * 24 * 60 * 60 * 1000, // 30天
        maxSize: 100 * 1024, // 100KB
        compression: true,
        encryption: true,
        evictionPolicy: 'lru'
      }],
      ['api-responses', {
        name: 'API响应缓存',
        storage: 'hybrid',
        ttl: 24 * 60 * 60 * 1000, // 24小时
        maxSize: 500 * 1024, // 500KB
        compression: true,
        encryption: false,
        evictionPolicy: 'ttl'
      }],
      ['real-time-data', {
        name: '实时数据缓存',
        storage: 'memory',
        ttl: 60 * 60 * 1000, // 1小时
        maxSize: 50 * 1024, // 50KB
        compression: false,
        encryption: false,
        evictionPolicy: 'lfu'
      }],
      ['journey-data', {
        name: '行程数据缓存',
        storage: 'indexedDB',
        ttl: 7 * 24 * 60 * 60 * 1000, // 7天
        maxSize: 1024 * 1024, // 1MB
        compression: true,
        encryption: false,
        evictionPolicy: 'lru'
      }],
      ['default', {
        name: '默认缓存',
        storage: 'memory',
        ttl: 30 * 60 * 1000, // 30分钟
        maxSize: 100 * 1024, // 100KB
        compression: false,
        encryption: false,
        evictionPolicy: 'lru'
      }]
    ];

    strategies.forEach(([category, strategy]) => {
      this.cacheStrategies.set(category, strategy);
    });

    console.log(`🔧 初始化${strategies.length}个缓存策略`);
  }

  /**
   * 🔧 辅助方法
   */
  private getCacheStrategy(category: string): CacheStrategy {
    return this.cacheStrategies.get(category) || this.cacheStrategies.get('default')!;
  }

  private isCacheValid(item: CacheItem): boolean {
    return Date.now() - item.timestamp < item.ttl;
  }

  private getFromMemory<T>(key: string): CacheItem<T> | null {
    return this.memoryCache.get(key) as CacheItem<T> || null;
  }

  private async getFromLocalStorage<T>(key: string): Promise<CacheItem<T> | null> {
    try {
      const cached = localStorage.getItem(`ultra_think_cache_${key}`);
      if (!cached) return null;
      
      return JSON.parse(cached) as CacheItem<T>;
    } catch (error) {
      console.warn('⚠️ localStorage读取失败:', error);
      return null;
    }
  }

  private async getFromIndexedDB<T>(key: string): Promise<CacheItem<T> | null> {
    // IndexedDB实现占位符
    // 实际项目中需要实现完整的IndexedDB操作
    return null;
  }

  private async setToLocalStorage<T>(key: string, item: CacheItem<T>): Promise<boolean> {
    try {
      localStorage.setItem(`ultra_think_cache_${key}`, JSON.stringify(item));
      return true;
    } catch (error) {
      console.warn('⚠️ localStorage写入失败:', error);
      return false;
    }
  }

  private async setToIndexedDB<T>(key: string, item: CacheItem<T>): Promise<boolean> {
    // IndexedDB实现占位符
    return false;
  }

  private deleteFromLocalStorage(key: string): boolean {
    try {
      localStorage.removeItem(`ultra_think_cache_${key}`);
      return true;
    } catch (error) {
      console.warn('⚠️ localStorage删除失败:', error);
      return false;
    }
  }

  private async deleteFromIndexedDB(key: string): Promise<boolean> {
    // IndexedDB实现占位符
    return false;
  }

  private async cleanupLocalStorage(): Promise<number> {
    let cleanedCount = 0;
    
    try {
      const keys = Object.keys(localStorage).filter(key => key.startsWith('ultra_think_cache_'));
      
      for (const key of keys) {
        try {
          const item = JSON.parse(localStorage.getItem(key) || '{}');
          if (!this.isCacheValid(item)) {
            localStorage.removeItem(key);
            cleanedCount++;
          }
        } catch (error) {
          // 删除损坏的缓存项
          localStorage.removeItem(key);
          cleanedCount++;
        }
      }
    } catch (error) {
      console.warn('⚠️ localStorage清理失败:', error);
    }
    
    return cleanedCount;
  }

  private async cleanupIndexedDB(): Promise<number> {
    // IndexedDB清理实现占位符
    return 0;
  }

  private async enforceCapacityLimits(strategy: CacheStrategy): Promise<void> {
    // 容量限制实现占位符
    // 实际项目中需要根据evictionPolicy实现不同的清理策略
  }

  private compressData<T>(data: T): T {
    // 简化的压缩实现
    // 实际项目中可以使用更高效的压缩算法
    return data;
  }

  private encryptData<T>(data: T): T {
    // 简化的加密实现
    // 实际项目中需要使用真正的加密算法
    return data;
  }

  private recordOperation(operation: CacheOperation): void {
    this.operationHistory.push(operation);
    
    // 保持历史记录在合理范围内
    if (this.operationHistory.length > 1000) {
      this.operationHistory = this.operationHistory.slice(-500);
    }
  }

  private initializeStats(): void {
    this.stats = {
      totalItems: 0,
      totalSize: 0,
      hitRate: 0,
      missRate: 0,
      evictionCount: 0,
      storageUsage: {
        memory: 0,
        localStorage: 0,
        indexedDB: 0
      }
    };
  }

  private updateStats(): void {
    const recentOps = this.operationHistory.slice(-100);
    const hits = recentOps.filter(op => op.operation === 'get' && op.cacheHit).length;
    const misses = recentOps.filter(op => op.operation === 'get' && !op.cacheHit).length;
    const total = hits + misses;
    
    this.stats.hitRate = total > 0 ? hits / total : 0;
    this.stats.missRate = total > 0 ? misses / total : 0;
    this.stats.totalItems = this.memoryCache.size;
  }

  private startCacheCleanup(): void {
    // 每30分钟执行一次清理
    setInterval(() => {
      this.cleanup();
    }, 30 * 60 * 1000);
  }

  /**
   * 📊 获取缓存统计
   */
  getStats(): CacheStats {
    this.updateStats();
    return { ...this.stats };
  }
}
