/**
 * ⏰ Timeline Optimizer - 时间线组件优化器
 * Phase 5: UI简化和体验优化 - 时间线组件优化
 * 优化时间线组件，实现灰色交通和统一活动颜色
 */

import { UltraThinkUIEngine } from './UltraThinkUIEngine';
import { UltraThinkCacheManager } from '../UltraThinkCacheManager';

// ===== 时间线优化接口定义 =====

export interface TimelineItem {
  id: string;
  type: 'activity' | 'transport' | 'break' | 'meal' | 'accommodation';
  title: string;
  description?: string;
  startTime: string;
  endTime: string;
  duration: number;
  location?: {
    name: string;
    address: string;
  };
  category: 'transport' | 'food' | 'culture' | 'nature' | 'shopping' | 'entertainment' | 'accommodation';
  priority: 'high' | 'medium' | 'low';
  status: 'planned' | 'confirmed' | 'completed' | 'cancelled';
  metadata: {
    cost?: number;
    bookingRequired?: boolean;
    weatherDependent?: boolean;
    groupSize?: number;
  };
}

export interface TimelineConfig {
  colorScheme: 'unified' | 'category-based' | 'custom';
  transportStyle: 'minimal' | 'detailed' | 'hidden';
  showDuration: boolean;
  showCost: boolean;
  compactMode: boolean;
  interactive: boolean;
  animations: boolean;
  groupByTime: boolean;
  timeFormat: '12h' | '24h';
}

export interface ColorMapping {
  transport: string;
  activity: string;
  meal: string;
  accommodation: string;
  break: string;
  default: string;
}

export interface TimelineTheme {
  id: string;
  name: string;
  colors: ColorMapping;
  styles: {
    lineColor: string;
    markerSize: number;
    spacing: number;
    borderRadius: number;
  };
}

export interface TimelineState {
  selectedItem?: string;
  expandedItems: Set<string>;
  filteredTypes: Set<string>;
  timeRange?: { start: string; end: string };
  viewMode: 'full' | 'compact' | 'minimal';
}

// ===== Timeline Optimizer 核心类 =====

export class TimelineOptimizer {
  private static instance: TimelineOptimizer;
  private uiEngine = UltraThinkUIEngine.getInstance();
  private cacheManager = UltraThinkCacheManager.getInstance();
  private themes = new Map<string, TimelineTheme>();
  private colorMappings = new Map<string, ColorMapping>();
  private optimizationRules = new Map<string, any>();

  private constructor() {
    this.initializeThemes();
    this.initializeColorMappings();
    this.initializeOptimizationRules();
    console.log('⏰ Timeline Optimizer 初始化完成');
  }

  static getInstance(): TimelineOptimizer {
    if (!TimelineOptimizer.instance) {
      TimelineOptimizer.instance = new TimelineOptimizer();
    }
    return TimelineOptimizer.instance;
  }

  /**
   * 🎨 优化时间线 - 主要入口方法
   */
  async optimizeTimeline(
    items: TimelineItem[], 
    config: TimelineConfig, 
    state?: TimelineState
  ): Promise<{ html: string; css: string; javascript: string }> {
    console.log(`⏰ 开始优化时间线: ${items.length}个项目`);

    try {
      // 1. 预处理时间线项目
      const processedItems = this.preprocessItems(items, config);

      // 2. 应用优化规则
      const optimizedItems = this.applyOptimizationRules(processedItems, config);

      // 3. 生成颜色方案
      const colorScheme = this.generateColorScheme(optimizedItems, config);

      // 4. 渲染HTML
      const html = this.renderTimeline(optimizedItems, config, colorScheme, state);

      // 5. 生成优化CSS
      const css = this.generateOptimizedCSS(config, colorScheme);

      // 6. 生成交互JavaScript
      const javascript = this.generateInteractiveJS(config, state);

      console.log(`✅ 时间线优化完成: ${optimizedItems.length}个优化项目`);
      return { html, css, javascript };

    } catch (error) {
      console.error('❌ 时间线优化失败:', error);
      return this.generateFallbackTimeline(items, config);
    }
  }

  /**
   * 🚌 优化交通显示
   */
  optimizeTransportDisplay(items: TimelineItem[], config: TimelineConfig): TimelineItem[] {
    return items.map(item => {
      if (item.type === 'transport') {
        // 简化交通显示
        if (config.transportStyle === 'minimal') {
          item.title = this.simplifyTransportTitle(item.title);
          item.description = undefined;
        }
        
        // 统一交通颜色为灰色
        item.metadata = {
          ...item.metadata,
          displayColor: '#6b7280', // 灰色
          simplified: true
        };
      }
      
      return item;
    });
  }

  /**
   * 🎨 统一活动颜色
   */
  unifyActivityColors(items: TimelineItem[], scheme: 'unified' | 'category-based'): TimelineItem[] {
    const unifiedColor = '#3b82f6'; // 统一蓝色
    
    return items.map(item => {
      if (item.type === 'activity') {
        if (scheme === 'unified') {
          item.metadata = {
            ...item.metadata,
            displayColor: unifiedColor
          };
        } else {
          // 基于类别的颜色
          const categoryColors = this.getCategoryColors();
          item.metadata = {
            ...item.metadata,
            displayColor: categoryColors[item.category] || unifiedColor
          };
        }
      }
      
      return item;
    });
  }

  /**
   * 🔧 初始化主题
   */
  private initializeThemes(): void {
    const unifiedTheme: TimelineTheme = {
      id: 'unified',
      name: '统一主题',
      colors: {
        transport: '#6b7280', // 灰色
        activity: '#3b82f6',  // 蓝色
        meal: '#f59e0b',      // 橙色
        accommodation: '#8b5cf6', // 紫色
        break: '#10b981',     // 绿色
        default: '#64748b'    // 默认灰色
      },
      styles: {
        lineColor: '#e2e8f0',
        markerSize: 12,
        spacing: 16,
        borderRadius: 6
      }
    };

    const categoryTheme: TimelineTheme = {
      id: 'category-based',
      name: '分类主题',
      colors: {
        transport: '#6b7280',
        activity: '#3b82f6',
        meal: '#f59e0b',
        accommodation: '#8b5cf6',
        break: '#10b981',
        default: '#64748b'
      },
      styles: {
        lineColor: '#e2e8f0',
        markerSize: 10,
        spacing: 12,
        borderRadius: 4
      }
    };

    this.themes.set('unified', unifiedTheme);
    this.themes.set('category-based', categoryTheme);
  }

  private initializeColorMappings(): void {
    const mappings = {
      transport: {
        transport: '#6b7280',
        activity: '#6b7280',
        meal: '#6b7280',
        accommodation: '#6b7280',
        break: '#6b7280',
        default: '#6b7280'
      },
      unified: {
        transport: '#6b7280',
        activity: '#3b82f6',
        meal: '#3b82f6',
        accommodation: '#3b82f6',
        break: '#3b82f6',
        default: '#3b82f6'
      },
      category: {
        transport: '#6b7280',
        activity: '#3b82f6',
        meal: '#f59e0b',
        accommodation: '#8b5cf6',
        break: '#10b981',
        default: '#64748b'
      }
    };

    Object.entries(mappings).forEach(([scheme, colors]) => {
      this.colorMappings.set(scheme, colors);
    });
  }

  private initializeOptimizationRules(): void {
    const rules = {
      transportSimplification: {
        enabled: true,
        rules: [
          { pattern: /从(.+)到(.+)/, replacement: '$1 → $2' },
          { pattern: /乘坐(.+)前往/, replacement: '$1' },
          { pattern: /步行至(.+)/, replacement: '🚶 $1' }
        ]
      },
      timeGrouping: {
        enabled: true,
        groupThreshold: 30, // 30分钟内的活动可以分组
        maxGroupSize: 3
      },
      durationOptimization: {
        enabled: true,
        minDisplayDuration: 15, // 最小显示时长15分钟
        transportDurationLimit: 120 // 交通时长超过2小时显示详情
      }
    };

    Object.entries(rules).forEach(([name, rule]) => {
      this.optimizationRules.set(name, rule);
    });
  }

  /**
   * 🔧 辅助方法
   */
  private preprocessItems(items: TimelineItem[], config: TimelineConfig): TimelineItem[] {
    let processed = [...items];

    // 排序
    processed.sort((a, b) => a.startTime.localeCompare(b.startTime));

    // 优化交通显示
    processed = this.optimizeTransportDisplay(processed, config);

    // 统一活动颜色
    processed = this.unifyActivityColors(processed, config.colorScheme as any);

    // 时间分组
    if (config.groupByTime) {
      processed = this.groupByTime(processed);
    }

    return processed;
  }

  private applyOptimizationRules(items: TimelineItem[], config: TimelineConfig): TimelineItem[] {
    let optimized = [...items];

    // 应用交通简化规则
    const transportRule = this.optimizationRules.get('transportSimplification');
    if (transportRule?.enabled) {
      optimized = optimized.map(item => {
        if (item.type === 'transport') {
          let title = item.title;
          transportRule.rules.forEach((rule: any) => {
            title = title.replace(rule.pattern, rule.replacement);
          });
          return { ...item, title };
        }
        return item;
      });
    }

    // 应用时长优化规则
    const durationRule = this.optimizationRules.get('durationOptimization');
    if (durationRule?.enabled) {
      optimized = optimized.filter(item => 
        item.duration >= durationRule.minDisplayDuration ||
        item.type === 'transport' ||
        item.priority === 'high'
      );
    }

    return optimized;
  }

  private generateColorScheme(items: TimelineItem[], config: TimelineConfig): ColorMapping {
    const scheme = config.colorScheme;
    return this.colorMappings.get(scheme) || this.colorMappings.get('unified')!;
  }

  private renderTimeline(
    items: TimelineItem[], 
    config: TimelineConfig, 
    colorScheme: ColorMapping, 
    state?: TimelineState
  ): string {
    const timelineClass = `timeline timeline--${config.colorScheme}${config.compactMode ? ' timeline--compact' : ''}`;
    
    return `
      <div class="${timelineClass}">
        <div class="timeline__line"></div>
        ${items.map((item, index) => this.renderTimelineItem(item, index, config, colorScheme, state)).join('')}
      </div>
    `;
  }

  private renderTimelineItem(
    item: TimelineItem, 
    index: number, 
    config: TimelineConfig, 
    colorScheme: ColorMapping, 
    state?: TimelineState
  ): string {
    const itemColor = this.getItemColor(item, colorScheme);
    const isExpanded = state?.expandedItems.has(item.id) || false;
    const isSelected = state?.selectedItem === item.id;
    
    const itemClass = `timeline-item timeline-item--${item.type}${isExpanded ? ' timeline-item--expanded' : ''}${isSelected ? ' timeline-item--selected' : ''}`;
    
    return `
      <div class="${itemClass}" data-id="${item.id}" data-type="${item.type}">
        <div class="timeline-item__marker" style="background-color: ${itemColor}"></div>
        <div class="timeline-item__content">
          <div class="timeline-item__header">
            <div class="timeline-item__time">${this.formatTime(item.startTime, config.timeFormat)}</div>
            <h4 class="timeline-item__title">${item.title}</h4>
            ${config.showDuration ? `<span class="timeline-item__duration">${item.duration}分钟</span>` : ''}
          </div>
          ${item.description && !config.compactMode ? `<p class="timeline-item__description">${item.description}</p>` : ''}
          ${item.location ? `<div class="timeline-item__location">${item.location.name}</div>` : ''}
          ${this.renderItemMetadata(item, config)}
          ${config.interactive ? this.renderItemActions(item) : ''}
        </div>
      </div>
    `;
  }

  private renderItemMetadata(item: TimelineItem, config: TimelineConfig): string {
    let metadata = '';
    
    if (config.showCost && item.metadata.cost) {
      metadata += `<span class="timeline-item__cost">¥${item.metadata.cost}</span>`;
    }
    
    if (item.metadata.bookingRequired) {
      metadata += `<span class="timeline-item__booking">需预订</span>`;
    }
    
    if (item.status !== 'planned') {
      metadata += `<span class="timeline-item__status timeline-item__status--${item.status}">${this.getStatusText(item.status)}</span>`;
    }
    
    return metadata ? `<div class="timeline-item__metadata">${metadata}</div>` : '';
  }

  private renderItemActions(item: TimelineItem): string {
    return `
      <div class="timeline-item__actions">
        <button class="timeline-action timeline-action--expand" data-action="expand">
          <span class="action-icon">▼</span>
        </button>
        <button class="timeline-action timeline-action--edit" data-action="edit">
          <span class="action-icon">✏️</span>
        </button>
      </div>
    `;
  }

  private generateOptimizedCSS(config: TimelineConfig, colorScheme: ColorMapping): string {
    const theme = this.themes.get(config.colorScheme) || this.themes.get('unified')!;
    
    return `
      .timeline {
        position: relative;
        padding: var(--spacing-lg) 0;
      }
      
      .timeline__line {
        position: absolute;
        left: 20px;
        top: 0;
        bottom: 0;
        width: 2px;
        background: ${theme.styles.lineColor};
      }
      
      .timeline-item {
        position: relative;
        padding-left: 50px;
        margin-bottom: ${config.compactMode ? '12px' : '24px'};
        transition: all 0.2s ease;
      }
      
      .timeline-item__marker {
        position: absolute;
        left: ${20 - theme.styles.markerSize / 2}px;
        top: 4px;
        width: ${theme.styles.markerSize}px;
        height: ${theme.styles.markerSize}px;
        border-radius: 50%;
        border: 2px solid white;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      }
      
      .timeline-item--transport .timeline-item__marker {
        background-color: ${colorScheme.transport} !important;
      }
      
      .timeline-item--activity .timeline-item__marker {
        background-color: ${colorScheme.activity} !important;
      }
      
      .timeline-item--meal .timeline-item__marker {
        background-color: ${colorScheme.meal} !important;
      }
      
      .timeline-item__content {
        background: var(--surface);
        border-radius: ${theme.styles.borderRadius}px;
        padding: ${config.compactMode ? '12px' : '16px'};
        box-shadow: var(--shadow-sm);
        transition: all 0.2s ease;
      }
      
      .timeline-item:hover .timeline-item__content {
        box-shadow: var(--shadow-md);
        transform: translateY(-1px);
      }
      
      .timeline-item__header {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        margin-bottom: ${config.compactMode ? '4px' : '8px'};
      }
      
      .timeline-item__time {
        font-size: var(--font-size-sm);
        color: var(--text-secondary);
        font-weight: var(--font-weight-medium);
        min-width: 60px;
      }
      
      .timeline-item__title {
        font-size: ${config.compactMode ? 'var(--font-size-base)' : 'var(--font-size-lg)'};
        font-weight: var(--font-weight-medium);
        color: var(--text-primary);
        margin: 0;
        flex: 1;
      }
      
      .timeline-item--transport .timeline-item__title {
        color: var(--text-secondary);
        font-size: var(--font-size-sm);
      }
      
      .timeline-item__duration {
        font-size: var(--font-size-xs);
        color: var(--text-secondary);
        background: var(--background);
        padding: 2px 6px;
        border-radius: var(--border-radius-sm);
      }
      
      .timeline-item__description {
        font-size: var(--font-size-sm);
        color: var(--text-secondary);
        margin: var(--spacing-xs) 0 0 0;
        line-height: 1.4;
      }
      
      .timeline-item__location {
        font-size: var(--font-size-xs);
        color: var(--text-secondary);
        margin-top: var(--spacing-xs);
      }
      
      .timeline-item__location::before {
        content: "📍 ";
        margin-right: 2px;
      }
      
      .timeline-item__metadata {
        display: flex;
        gap: var(--spacing-xs);
        margin-top: var(--spacing-sm);
        flex-wrap: wrap;
      }
      
      .timeline-item__cost,
      .timeline-item__booking,
      .timeline-item__status {
        font-size: var(--font-size-xs);
        padding: 2px 6px;
        border-radius: var(--border-radius-sm);
        background: var(--background);
        color: var(--text-secondary);
      }
      
      .timeline-item__status--confirmed {
        background: var(--color-success);
        color: white;
      }
      
      .timeline-item__status--completed {
        background: var(--color-info);
        color: white;
      }
      
      .timeline-item__actions {
        display: flex;
        gap: var(--spacing-xs);
        margin-top: var(--spacing-sm);
        opacity: 0;
        transition: opacity 0.2s ease;
      }
      
      .timeline-item:hover .timeline-item__actions {
        opacity: 1;
      }
      
      .timeline-action {
        background: none;
        border: 1px solid var(--color-secondary);
        border-radius: var(--border-radius-sm);
        padding: 4px 8px;
        cursor: pointer;
        font-size: var(--font-size-xs);
        transition: all 0.2s ease;
      }
      
      .timeline-action:hover {
        background: var(--color-secondary);
        color: white;
      }
      
      /* 紧凑模式样式 */
      .timeline--compact .timeline-item {
        margin-bottom: 8px;
      }
      
      .timeline--compact .timeline-item__content {
        padding: 8px 12px;
      }
      
      .timeline--compact .timeline-item__marker {
        width: 8px;
        height: 8px;
        left: 16px;
      }
      
      .timeline--compact .timeline__line {
        left: 20px;
      }
      
      /* 响应式设计 */
      @media (max-width: 768px) {
        .timeline-item {
          padding-left: 40px;
        }
        
        .timeline__line {
          left: 16px;
        }
        
        .timeline-item__marker {
          left: 12px;
        }
        
        .timeline-item__header {
          flex-direction: column;
          align-items: flex-start;
          gap: 4px;
        }
      }
      
      ${config.animations ? this.generateTimelineAnimations() : ''}
    `;
  }

  private generateTimelineAnimations(): string {
    return `
      @keyframes timelineSlideIn {
        from {
          opacity: 0;
          transform: translateX(-20px);
        }
        to {
          opacity: 1;
          transform: translateX(0);
        }
      }
      
      .timeline-item {
        animation: timelineSlideIn 0.3s ease-out;
        animation-fill-mode: both;
      }
      
      .timeline-item:nth-child(1) { animation-delay: 0.1s; }
      .timeline-item:nth-child(2) { animation-delay: 0.2s; }
      .timeline-item:nth-child(3) { animation-delay: 0.3s; }
      .timeline-item:nth-child(4) { animation-delay: 0.4s; }
      .timeline-item:nth-child(5) { animation-delay: 0.5s; }
    `;
  }

  private generateInteractiveJS(config: TimelineConfig, state?: TimelineState): string {
    if (!config.interactive) return '';
    
    return `
      // 时间线交互功能
      document.querySelectorAll('.timeline-item').forEach(item => {
        // 点击展开/收起
        item.addEventListener('click', function(e) {
          if (e.target.closest('.timeline-action')) return;
          
          this.classList.toggle('timeline-item--expanded');
          
          // 触发自定义事件
          this.dispatchEvent(new CustomEvent('timeline:toggle', {
            detail: { 
              itemId: this.dataset.id,
              expanded: this.classList.contains('timeline-item--expanded')
            }
          }));
        });
        
        // 操作按钮事件
        item.querySelectorAll('.timeline-action').forEach(btn => {
          btn.addEventListener('click', function(e) {
            e.stopPropagation();
            
            const action = this.dataset.action;
            const itemId = item.dataset.id;
            
            // 触发自定义事件
            item.dispatchEvent(new CustomEvent('timeline:action', {
              detail: { itemId, action }
            }));
          });
        });
      });
      
      // 时间线滚动优化
      let timelineObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            entry.target.classList.add('timeline-item--visible');
          }
        });
      }, { threshold: 0.1 });
      
      document.querySelectorAll('.timeline-item').forEach(item => {
        timelineObserver.observe(item);
      });
    `;
  }

  private simplifyTransportTitle(title: string): string {
    const rules = this.optimizationRules.get('transportSimplification')?.rules || [];
    let simplified = title;
    
    rules.forEach((rule: any) => {
      simplified = simplified.replace(rule.pattern, rule.replacement);
    });
    
    return simplified;
  }

  private getCategoryColors(): Record<string, string> {
    return {
      transport: '#6b7280',
      food: '#f59e0b',
      culture: '#8b5cf6',
      nature: '#10b981',
      shopping: '#ec4899',
      entertainment: '#f97316',
      accommodation: '#8b5cf6'
    };
  }

  private groupByTime(items: TimelineItem[]): TimelineItem[] {
    // 简化的时间分组实现
    return items;
  }

  private getItemColor(item: TimelineItem, colorScheme: ColorMapping): string {
    switch (item.type) {
      case 'transport':
        return colorScheme.transport;
      case 'activity':
        return colorScheme.activity;
      case 'meal':
        return colorScheme.meal;
      case 'accommodation':
        return colorScheme.accommodation;
      case 'break':
        return colorScheme.break;
      default:
        return colorScheme.default;
    }
  }

  private formatTime(time: string, format: '12h' | '24h'): string {
    if (format === '12h') {
      const [hours, minutes] = time.split(':');
      const hour = parseInt(hours);
      const ampm = hour >= 12 ? 'PM' : 'AM';
      const displayHour = hour % 12 || 12;
      return `${displayHour}:${minutes} ${ampm}`;
    }
    return time;
  }

  private getStatusText(status: string): string {
    const statusMap = {
      planned: '计划中',
      confirmed: '已确认',
      completed: '已完成',
      cancelled: '已取消'
    };
    return statusMap[status as keyof typeof statusMap] || status;
  }

  private generateFallbackTimeline(items: TimelineItem[], config: TimelineConfig): any {
    return {
      html: `<div class="timeline-fallback">时间线渲染失败，共${items.length}个项目</div>`,
      css: '.timeline-fallback { padding: 1rem; background: #fee; color: #c00; }',
      javascript: ''
    };
  }

  /**
   * 📊 获取优化统计
   */
  getOptimizationStats() {
    return {
      themes: this.themes.size,
      colorMappings: this.colorMappings.size,
      optimizationRules: this.optimizationRules.size,
      supportedTypes: ['activity', 'transport', 'meal', 'accommodation', 'break'],
      supportedColorSchemes: Array.from(this.colorMappings.keys())
    };
  }
}
