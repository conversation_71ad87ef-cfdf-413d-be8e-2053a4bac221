/**
 * 🌸 Seasonal AI - 季节性智能引擎
 * Phase 2: 智能偏好引擎构建 - 季节性智能分析
 * 根据季节、天气和时间进行智能推荐
 */

import { UltraThinkLLMManager } from '../../ai/UltraThinkLLMManager';
import { UltraThinkCacheManager } from '../UltraThinkCacheManager';

// ===== 季节性智能接口定义 =====

export interface SeasonalContext {
  destination: string;
  travelDate: Date;
  duration: number;
  season: 'spring' | 'summer' | 'autumn' | 'winter';
  weather: {
    temperature: { min: number; max: number };
    conditions: string[];
    precipitation: number;
    humidity: number;
  };
  localEvents: string[];
}

export interface SeasonalRecommendation {
  category: 'activity' | 'clothing' | 'timing' | 'food' | 'cultural';
  priority: 'high' | 'medium' | 'low';
  title: string;
  description: string;
  reasoning: string;
  seasonalAdvantage: string;
  alternatives?: string[];
  warnings?: string[];
}

export interface SeasonalAnalysis {
  season: string;
  seasonalScore: number; // 0-1, 季节适宜度
  recommendations: SeasonalRecommendation[];
  bestActivities: string[];
  avoidActivities: string[];
  clothingAdvice: string[];
  culturalHighlights: string[];
  weatherConsiderations: string[];
  localSpecialties: string[];
  metadata: {
    analysisDate: Date;
    confidence: number;
    dataSource: string[];
  };
}

// ===== Seasonal AI 核心类 =====

export class SeasonalAI {
  private static instance: SeasonalAI;
  private llmManager = UltraThinkLLMManager.getInstance();
  private cacheManager = UltraThinkCacheManager.getInstance();
  private seasonalKnowledge = new Map<string, any>();

  private constructor() {
    this.initializeSeasonalKnowledge();
    console.log('🌸 Seasonal AI 初始化完成');
  }

  static getInstance(): SeasonalAI {
    if (!SeasonalAI.instance) {
      SeasonalAI.instance = new SeasonalAI();
    }
    return SeasonalAI.instance;
  }

  /**
   * 🌍 季节性智能分析 - 主要入口方法
   */
  async analyzeSeasonalContext(context: SeasonalContext): Promise<SeasonalAnalysis> {
    console.log(`🌸 开始季节性分析: ${context.destination} (${context.season})`);

    try {
      // 1. 检查缓存
      const cacheKey = this.generateCacheKey(context);
      const cached = await this.cacheManager.get<SeasonalAnalysis>(cacheKey, 'seasonal-analysis');
      
      if (cached) {
        console.log('✅ 季节性分析缓存命中');
        return cached;
      }

      // 2. 确定季节和天气上下文
      const seasonalContext = this.enrichSeasonalContext(context);
      console.log(`📊 季节上下文: ${seasonalContext.season}, 温度${seasonalContext.weather.temperature.min}-${seasonalContext.weather.temperature.max}°C`);

      // 3. 生成季节性推荐
      const recommendations = await this.generateSeasonalRecommendations(seasonalContext);

      // 4. 分析最佳和避免的活动
      const activityAnalysis = this.analyzeSeasonalActivities(seasonalContext);

      // 5. 生成服装建议
      const clothingAdvice = this.generateClothingAdvice(seasonalContext);

      // 6. 识别文化亮点
      const culturalHighlights = await this.identifyCulturalHighlights(seasonalContext);

      // 7. 分析天气考虑因素
      const weatherConsiderations = this.analyzeWeatherConsiderations(seasonalContext);

      // 8. 发现当地特色
      const localSpecialties = await this.discoverLocalSpecialties(seasonalContext);

      // 9. 构建完整分析结果
      const analysis: SeasonalAnalysis = {
        season: seasonalContext.season,
        seasonalScore: this.calculateSeasonalScore(seasonalContext),
        recommendations,
        bestActivities: activityAnalysis.best,
        avoidActivities: activityAnalysis.avoid,
        clothingAdvice,
        culturalHighlights,
        weatherConsiderations,
        localSpecialties,
        metadata: {
          analysisDate: new Date(),
          confidence: this.calculateConfidence(seasonalContext, recommendations),
          dataSource: ['seasonal-knowledge', 'weather-data', 'cultural-calendar']
        }
      };

      // 10. 缓存结果
      await this.cacheManager.set(cacheKey, analysis, 'seasonal-analysis', 24 * 60 * 60 * 1000); // 24小时

      console.log(`✅ 季节性分析完成: 评分${analysis.seasonalScore.toFixed(2)}, ${analysis.recommendations.length}个推荐`);
      return analysis;

    } catch (error) {
      console.error('❌ 季节性分析失败:', error);
      return this.generateFallbackAnalysis(context);
    }
  }

  /**
   * 🌟 生成季节性推荐
   */
  private async generateSeasonalRecommendations(context: SeasonalContext): Promise<SeasonalRecommendation[]> {
    const recommendations: SeasonalRecommendation[] = [];

    // 基于季节的活动推荐
    const activityRecs = this.generateActivityRecommendations(context);
    recommendations.push(...activityRecs);

    // 基于天气的时间推荐
    const timingRecs = this.generateTimingRecommendations(context);
    recommendations.push(...timingRecs);

    // 基于季节的美食推荐
    const foodRecs = await this.generateFoodRecommendations(context);
    recommendations.push(...foodRecs);

    // 基于文化日历的推荐
    const culturalRecs = await this.generateCulturalRecommendations(context);
    recommendations.push(...culturalRecs);

    // 按优先级排序
    return recommendations.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });
  }

  /**
   * 🎯 生成活动推荐
   */
  private generateActivityRecommendations(context: SeasonalContext): SeasonalRecommendation[] {
    const season = context.season;
    const temp = context.weather.temperature;
    const recommendations: SeasonalRecommendation[] = [];

    // 春季推荐
    if (season === 'spring') {
      recommendations.push({
        category: 'activity',
        priority: 'high',
        title: '赏花踏青活动',
        description: '春季是观赏樱花、梅花等花卉的最佳时节，适合户外踏青活动',
        reasoning: '春季温度适宜，花卉盛开，是户外活动的黄金时期',
        seasonalAdvantage: '独特的春季花卉景观和舒适的气候条件',
        alternatives: ['公园漫步', '植物园参观', '户外摄影']
      });
    }

    // 夏季推荐
    if (season === 'summer') {
      if (temp.max > 30) {
        recommendations.push({
          category: 'activity',
          priority: 'high',
          title: '室内文化体验',
          description: '高温天气适合参观博物馆、艺术馆等室内文化场所',
          reasoning: '避开高温时段，享受空调环境下的文化体验',
          seasonalAdvantage: '避暑的同时获得丰富的文化体验',
          warnings: ['避免中午时段的户外活动', '注意防晒和补水']
        });
      } else {
        recommendations.push({
          category: 'activity',
          priority: 'high',
          title: '水上活动体验',
          description: '夏季适合各种水上活动和海滨度假',
          reasoning: '夏季高温适合水上运动，可以有效降温',
          seasonalAdvantage: '最佳的水上活动季节，体验丰富'
        });
      }
    }

    // 秋季推荐
    if (season === 'autumn') {
      recommendations.push({
        category: 'activity',
        priority: 'high',
        title: '红叶观赏之旅',
        description: '秋季是观赏红叶、银杏等秋色的最佳时节',
        reasoning: '秋季叶色变化丰富，气候凉爽适合户外活动',
        seasonalAdvantage: '独特的秋季色彩和舒适的徒步环境',
        alternatives: ['山区徒步', '古建筑参观', '温泉体验']
      });
    }

    // 冬季推荐
    if (season === 'winter') {
      if (temp.min < 0) {
        recommendations.push({
          category: 'activity',
          priority: 'high',
          title: '冬季温泉体验',
          description: '寒冷天气最适合享受温暖的温泉和室内活动',
          reasoning: '低温环境下温泉体验更加舒适和治愈',
          seasonalAdvantage: '冬季温泉的独特体验和保暖效果',
          alternatives: ['室内购物', '传统工艺体验', '美食探索']
        });
      }
    }

    return recommendations;
  }

  /**
   * ⏰ 生成时间推荐
   */
  private generateTimingRecommendations(context: SeasonalContext): SeasonalRecommendation[] {
    const recommendations: SeasonalRecommendation[] = [];
    const weather = context.weather;

    // 基于天气条件的时间建议
    if (weather.precipitation > 50) {
      recommendations.push({
        category: 'timing',
        priority: 'medium',
        title: '室内活动时间安排',
        description: '降水较多，建议安排更多室内活动时间',
        reasoning: '避开降水时段，确保旅行体验不受影响',
        seasonalAdvantage: '室内活动可以不受天气影响',
        warnings: ['携带雨具', '关注天气预报']
      });
    }

    if (weather.temperature.max > 35) {
      recommendations.push({
        category: 'timing',
        priority: 'high',
        title: '避开高温时段',
        description: '建议在早晨和傍晚安排户外活动，中午时段选择室内活动',
        reasoning: '避免高温中暑，提高旅行舒适度',
        seasonalAdvantage: '早晚时段温度适宜，光线柔和',
        warnings: ['中午避免户外暴晒', '及时补充水分']
      });
    }

    return recommendations;
  }

  /**
   * 🍽️ 生成美食推荐
   */
  private async generateFoodRecommendations(context: SeasonalContext): Promise<SeasonalRecommendation[]> {
    const recommendations: SeasonalRecommendation[] = [];
    const season = context.season;

    // 使用LLM生成季节性美食推荐
    try {
      const prompt = `为${context.destination}的${season}季节推荐3个当地特色美食，考虑季节性食材和气候特点。`;
      
      const llmResponse = await this.llmManager.callLLM({
        prompt,
        taskType: 'seasonal-food',
        context: {
          destination: context.destination,
          complexity: 'low'
        }
      });

      if (llmResponse.success) {
        // 解析LLM响应并转换为推荐格式
        const foodSuggestions = this.parseFoodSuggestions(llmResponse.content);
        recommendations.push(...foodSuggestions);
      }
    } catch (error) {
      console.warn('⚠️ LLM美食推荐失败，使用默认推荐');
    }

    // 默认季节性美食推荐
    if (recommendations.length === 0) {
      recommendations.push(this.getDefaultFoodRecommendation(season));
    }

    return recommendations;
  }

  /**
   * 🎭 生成文化推荐
   */
  private async generateCulturalRecommendations(context: SeasonalContext): Promise<SeasonalRecommendation[]> {
    const recommendations: SeasonalRecommendation[] = [];

    // 基于当地活动的文化推荐
    if (context.localEvents.length > 0) {
      context.localEvents.forEach(event => {
        recommendations.push({
          category: 'cultural',
          priority: 'high',
          title: `参与${event}`,
          description: `${context.season}季节特有的当地文化活动`,
          reasoning: '体验当地独特的季节性文化传统',
          seasonalAdvantage: '季节限定的文化体验机会'
        });
      });
    }

    return recommendations;
  }

  /**
   * 🔧 辅助方法
   */
  private initializeSeasonalKnowledge(): void {
    // 初始化季节性知识库
    const knowledge = {
      spring: {
        characteristics: ['温暖', '花开', '多雨', '生机勃勃'],
        bestActivities: ['赏花', '踏青', '户外摄影', '公园漫步'],
        clothing: ['轻薄外套', '长袖衬衫', '舒适鞋子', '雨具'],
        foods: ['时令蔬菜', '春茶', '清淡料理']
      },
      summer: {
        characteristics: ['炎热', '阳光充足', '雨季', '活力四射'],
        bestActivities: ['水上运动', '海滨度假', '夜市探索', '室内文化'],
        clothing: ['防晒衣物', '遮阳帽', '凉鞋', '泳装'],
        foods: ['冷饮', '水果', '清爽料理', '冰品']
      },
      autumn: {
        characteristics: ['凉爽', '红叶', '干燥', '收获季节'],
        bestActivities: ['红叶观赏', '徒步登山', '温泉体验', '美食品尝'],
        clothing: ['薄毛衣', '长裤', '舒适靴子', '轻便外套'],
        foods: ['时令水果', '温补料理', '热饮', '坚果']
      },
      winter: {
        characteristics: ['寒冷', '雪景', '干燥', '节庆氛围'],
        bestActivities: ['温泉浴', '室内购物', '传统工艺', '节庆活动'],
        clothing: ['厚外套', '保暖内衣', '防滑鞋', '围巾手套'],
        foods: ['热汤', '火锅', '温热饮品', '节庆美食']
      }
    };

    Object.entries(knowledge).forEach(([season, data]) => {
      this.seasonalKnowledge.set(season, data);
    });
  }

  private enrichSeasonalContext(context: SeasonalContext): SeasonalContext {
    // 如果没有提供季节，根据日期推断
    if (!context.season) {
      context.season = this.inferSeason(context.travelDate);
    }

    // 如果没有天气数据，使用默认值
    if (!context.weather) {
      context.weather = this.getDefaultWeather(context.season);
    }

    return context;
  }

  private inferSeason(date: Date): 'spring' | 'summer' | 'autumn' | 'winter' {
    const month = date.getMonth() + 1;
    
    if (month >= 3 && month <= 5) return 'spring';
    if (month >= 6 && month <= 8) return 'summer';
    if (month >= 9 && month <= 11) return 'autumn';
    return 'winter';
  }

  private getDefaultWeather(season: string): any {
    const defaultWeather = {
      spring: { temperature: { min: 10, max: 20 }, conditions: ['partly-cloudy'], precipitation: 30, humidity: 60 },
      summer: { temperature: { min: 20, max: 30 }, conditions: ['sunny'], precipitation: 10, humidity: 70 },
      autumn: { temperature: { min: 5, max: 15 }, conditions: ['clear'], precipitation: 20, humidity: 50 },
      winter: { temperature: { min: -5, max: 5 }, conditions: ['cloudy'], precipitation: 40, humidity: 55 }
    };

    return defaultWeather[season as keyof typeof defaultWeather];
  }

  private analyzeSeasonalActivities(context: SeasonalContext): { best: string[]; avoid: string[] } {
    const knowledge = this.seasonalKnowledge.get(context.season);
    
    return {
      best: knowledge?.bestActivities || [],
      avoid: this.getActivitiesToAvoid(context)
    };
  }

  private getActivitiesToAvoid(context: SeasonalContext): string[] {
    const avoid: string[] = [];
    
    if (context.weather.temperature.max > 35) {
      avoid.push('长时间户外暴晒活动', '中午时段徒步');
    }
    
    if (context.weather.precipitation > 70) {
      avoid.push('户外野餐', '海滨活动');
    }
    
    if (context.weather.temperature.min < -10) {
      avoid.push('水上活动', '长时间户外停留');
    }
    
    return avoid;
  }

  private generateClothingAdvice(context: SeasonalContext): string[] {
    const knowledge = this.seasonalKnowledge.get(context.season);
    const baseAdvice = knowledge?.clothing || [];
    
    // 根据具体天气调整
    const advice = [...baseAdvice];
    
    if (context.weather.precipitation > 50) {
      advice.push('防水外套', '防滑鞋');
    }
    
    if (context.weather.temperature.max > 30) {
      advice.push('防晒霜', '太阳镜');
    }
    
    return advice;
  }

  private async identifyCulturalHighlights(context: SeasonalContext): Promise<string[]> {
    // 基于季节和地点识别文化亮点
    const highlights: string[] = [];
    
    if (context.localEvents.length > 0) {
      highlights.push(...context.localEvents);
    }
    
    // 添加季节性文化特色
    const seasonalCulture = this.getSeasonalCulture(context.season, context.destination);
    highlights.push(...seasonalCulture);
    
    return highlights;
  }

  private getSeasonalCulture(season: string, destination: string): string[] {
    // 简化的季节性文化映射
    const cultureMap = {
      spring: ['花季庆典', '春季祭典'],
      summer: ['夏日祭', '音乐节'],
      autumn: ['收获节', '红叶祭'],
      winter: ['新年庆典', '冬季灯会']
    };
    
    return cultureMap[season as keyof typeof cultureMap] || [];
  }

  private analyzeWeatherConsiderations(context: SeasonalContext): string[] {
    const considerations: string[] = [];
    
    if (context.weather.precipitation > 50) {
      considerations.push('携带雨具，关注天气预报');
    }
    
    if (context.weather.temperature.max > 30) {
      considerations.push('注意防晒，及时补水');
    }
    
    if (context.weather.temperature.min < 0) {
      considerations.push('注意保暖，防止感冒');
    }
    
    if (context.weather.humidity > 80) {
      considerations.push('注意通风，选择透气衣物');
    }
    
    return considerations;
  }

  private async discoverLocalSpecialties(context: SeasonalContext): Promise<string[]> {
    const knowledge = this.seasonalKnowledge.get(context.season);
    return knowledge?.foods || [];
  }

  private calculateSeasonalScore(context: SeasonalContext): number {
    let score = 0.5; // 基础分数
    
    // 温度适宜性
    const temp = context.weather.temperature;
    if (temp.min >= 5 && temp.max <= 25) score += 0.2;
    
    // 降水适宜性
    if (context.weather.precipitation < 30) score += 0.1;
    
    // 季节特色
    if (context.localEvents.length > 0) score += 0.1;
    
    // 湿度舒适度
    if (context.weather.humidity >= 40 && context.weather.humidity <= 70) score += 0.1;
    
    return Math.min(1.0, score);
  }

  private calculateConfidence(context: SeasonalContext, recommendations: SeasonalRecommendation[]): number {
    let confidence = 0.7; // 基础置信度
    
    if (context.weather && Object.keys(context.weather).length > 0) confidence += 0.1;
    if (context.localEvents.length > 0) confidence += 0.1;
    if (recommendations.length >= 5) confidence += 0.1;
    
    return Math.min(1.0, confidence);
  }

  private generateCacheKey(context: SeasonalContext): string {
    return `seasonal_${context.destination}_${context.season}_${context.travelDate.toISOString().split('T')[0]}`;
  }

  private parseFoodSuggestions(content: string): SeasonalRecommendation[] {
    // 简化的LLM响应解析
    return [{
      category: 'food',
      priority: 'medium',
      title: '季节性当地美食',
      description: content.substring(0, 100) + '...',
      reasoning: '基于季节性食材和当地特色',
      seasonalAdvantage: '品尝最新鲜的季节性食材'
    }];
  }

  private getDefaultFoodRecommendation(season: string): SeasonalRecommendation {
    const knowledge = this.seasonalKnowledge.get(season);
    
    return {
      category: 'food',
      priority: 'medium',
      title: `${season}季特色美食`,
      description: `品尝${season}季节的特色料理和时令食材`,
      reasoning: '季节性食材最为新鲜美味',
      seasonalAdvantage: '体验当季最佳风味',
      alternatives: knowledge?.foods || []
    };
  }

  private generateFallbackAnalysis(context: SeasonalContext): SeasonalAnalysis {
    return {
      season: context.season,
      seasonalScore: 0.6,
      recommendations: [this.getDefaultFoodRecommendation(context.season)],
      bestActivities: ['观光游览', '文化体验'],
      avoidActivities: [],
      clothingAdvice: ['舒适服装', '根据天气调整'],
      culturalHighlights: ['当地特色文化'],
      weatherConsiderations: ['关注天气变化'],
      localSpecialties: ['当地特色美食'],
      metadata: {
        analysisDate: new Date(),
        confidence: 0.5,
        dataSource: ['fallback']
      }
    };
  }
}
