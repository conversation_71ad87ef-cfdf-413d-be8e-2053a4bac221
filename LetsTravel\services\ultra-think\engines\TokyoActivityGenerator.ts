/**
 * 🗼 东京活动生成器
 * 专门为东京生成高质量、真实的旅游活动
 */

export interface TokyoActivity {
  id: string;
  name: string;
  nameEn: string;
  description: string;
  location: {
    name: string;
    address: string;
    district: string;
    coordinates?: {
      lat: number;
      lng: number;
    };
  };
  timing: {
    duration: number; // 分钟
    bestTime: string[];
    openHours?: string;
  };
  cost: {
    amount: number;
    currency: string;
    category: 'free' | 'budget' | 'mid' | 'premium' | 'luxury';
  };
  category: string;
  tags: string[];
  difficulty: 'easy' | 'moderate' | 'challenging';
  crowdLevel: 'low' | 'medium' | 'high';
  seasonality: {
    best: string[];
    avoid?: string[];
  };
  tips: string[];
}

export class TokyoActivityGenerator {
  private static instance: TokyoActivityGenerator;
  
  // 东京经典活动数据库
  private readonly tokyoActivities: TokyoActivity[] = [
    {
      id: 'senso-ji-temple',
      name: '浅草寺参拜',
      nameEn: 'Senso-ji Temple Visit',
      description: '东京最古老的寺庙，体验传统日本文化和建筑之美',
      location: {
        name: '浅草寺',
        address: '东京都台东区浅草2-3-1',
        district: '浅草',
        coordinates: { lat: 35.7148, lng: 139.7967 }
      },
      timing: {
        duration: 120,
        bestTime: ['morning', 'afternoon'],
        openHours: '06:00-17:00'
      },
      cost: {
        amount: 0,
        currency: 'JPY',
        category: 'free'
      },
      category: 'cultural',
      tags: ['寺庙', '历史', '传统', '免费', '必游'],
      difficulty: 'easy',
      crowdLevel: 'high',
      seasonality: {
        best: ['spring', 'autumn', 'winter'],
        avoid: ['summer-peak']
      },
      tips: [
        '早上8点前到达可避开人群',
        '可以求签和购买护身符',
        '附近的仲见世通商店街值得一逛'
      ]
    },
    {
      id: 'tokyo-skytree',
      name: '东京晴空塔观景',
      nameEn: 'Tokyo Skytree Observatory',
      description: '登上634米高的东京地标，俯瞰整个东京都市景观',
      location: {
        name: '东京晴空塔',
        address: '东京都墨田区押上1-1-2',
        district: '墨田区',
        coordinates: { lat: 35.7101, lng: 139.8107 }
      },
      timing: {
        duration: 150,
        bestTime: ['afternoon', 'evening'],
        openHours: '08:00-22:00'
      },
      cost: {
        amount: 2100,
        currency: 'JPY',
        category: 'mid'
      },
      category: 'sightseeing',
      tags: ['观景', '地标', '现代', '摄影', '必游'],
      difficulty: 'easy',
      crowdLevel: 'high',
      seasonality: {
        best: ['all'],
      },
      tips: [
        '建议提前网上购票',
        '黄昏时分景色最美',
        '天气晴朗时可看到富士山'
      ]
    },
    {
      id: 'tsukiji-outer-market',
      name: '筑地外市场美食探索',
      nameEn: 'Tsukiji Outer Market Food Tour',
      description: '品尝最新鲜的海鲜和传统日式小食，体验东京美食文化',
      location: {
        name: '筑地外市场',
        address: '东京都中央区筑地4丁目',
        district: '中央区',
        coordinates: { lat: 35.6654, lng: 139.7707 }
      },
      timing: {
        duration: 180,
        bestTime: ['morning', 'early-afternoon'],
        openHours: '05:00-14:00'
      },
      cost: {
        amount: 3000,
        currency: 'JPY',
        category: 'mid'
      },
      category: 'food',
      tags: ['美食', '海鲜', '传统', '市场', '体验'],
      difficulty: 'easy',
      crowdLevel: 'medium',
      seasonality: {
        best: ['all'],
      },
      tips: [
        '早上6-8点是最佳时间',
        '带现金，很多店铺不接受信用卡',
        '尝试金枪鱼寿司和玉子烧'
      ]
    },
    {
      id: 'shibuya-crossing',
      name: '涩谷十字路口体验',
      nameEn: 'Shibuya Crossing Experience',
      description: '体验世界最繁忙的十字路口，感受东京都市的脉搏',
      location: {
        name: '涩谷十字路口',
        address: '东京都涩谷区道玄坂',
        district: '涩谷',
        coordinates: { lat: 35.6598, lng: 139.7006 }
      },
      timing: {
        duration: 60,
        bestTime: ['afternoon', 'evening'],
        openHours: '24小时'
      },
      cost: {
        amount: 0,
        currency: 'JPY',
        category: 'free'
      },
      category: 'urban',
      tags: ['都市', '体验', '免费', '摄影', '现代'],
      difficulty: 'easy',
      crowdLevel: 'high',
      seasonality: {
        best: ['all'],
      },
      tips: [
        '傍晚时分人流最多',
        '可以到星巴克二楼俯拍',
        '体验后可逛涩谷中心街'
      ]
    },
    {
      id: 'meiji-shrine',
      name: '明治神宫参拜',
      nameEn: 'Meiji Shrine Visit',
      description: '在都市中心的绿洲中感受日本神道文化的庄严与宁静',
      location: {
        name: '明治神宫',
        address: '东京都涩谷区代代木神园町1-1',
        district: '涩谷',
        coordinates: { lat: 35.6763, lng: 139.6993 }
      },
      timing: {
        duration: 90,
        bestTime: ['morning', 'afternoon'],
        openHours: '日出-日落'
      },
      cost: {
        amount: 0,
        currency: 'JPY',
        category: 'free'
      },
      category: 'cultural',
      tags: ['神社', '自然', '宁静', '免费', '文化'],
      difficulty: 'easy',
      crowdLevel: 'medium',
      seasonality: {
        best: ['spring', 'autumn'],
      },
      tips: [
        '进入前在手水舍净手',
        '可能遇到传统婚礼仪式',
        '周边的代代木公园也值得一游'
      ]
    },
    {
      id: 'tokyo-national-museum',
      name: '东京国立博物馆',
      nameEn: 'Tokyo National Museum',
      description: '探索日本最大的博物馆，了解日本艺术和文化的精髓',
      location: {
        name: '东京国立博物馆',
        address: '东京都台东区上野公园13-9',
        district: '上野',
        coordinates: { lat: 35.7188, lng: 139.7766 }
      },
      timing: {
        duration: 180,
        bestTime: ['morning', 'afternoon'],
        openHours: '09:30-17:00'
      },
      cost: {
        amount: 1000,
        currency: 'JPY',
        category: 'budget'
      },
      category: 'cultural',
      tags: ['博物馆', '艺术', '历史', '教育', '室内'],
      difficulty: 'easy',
      crowdLevel: 'medium',
      seasonality: {
        best: ['all'],
      },
      tips: [
        '周一闭馆',
        '有中文语音导览',
        '可与上野公园其他景点一起游览'
      ]
    },
    {
      id: 'harajuku-takeshita',
      name: '原宿竹下通购物',
      nameEn: 'Harajuku Takeshita Street Shopping',
      description: '体验东京年轻人的时尚文化和独特的街头风格',
      location: {
        name: '竹下通',
        address: '东京都涩谷区神宫前1丁目',
        district: '原宿',
        coordinates: { lat: 35.6702, lng: 139.7063 }
      },
      timing: {
        duration: 120,
        bestTime: ['afternoon', 'evening'],
        openHours: '10:00-20:00'
      },
      cost: {
        amount: 2000,
        currency: 'JPY',
        category: 'mid'
      },
      category: 'shopping',
      tags: ['购物', '时尚', '年轻', '文化', '街头'],
      difficulty: 'easy',
      crowdLevel: 'high',
      seasonality: {
        best: ['all'],
      },
      tips: [
        '周末人流量很大',
        '可以尝试彩虹棉花糖',
        '附近的表参道也值得一逛'
      ]
    },
    {
      id: 'tokyo-station-ramen',
      name: '东京站拉面街',
      nameEn: 'Tokyo Station Ramen Street',
      description: '在东京站地下品尝来自全日本的顶级拉面',
      location: {
        name: '东京站一番街拉面街',
        address: '东京都千代田区丸之内1-9-1',
        district: '丸之内',
        coordinates: { lat: 35.6812, lng: 139.7671 }
      },
      timing: {
        duration: 90,
        bestTime: ['lunch', 'dinner'],
        openHours: '10:30-23:00'
      },
      cost: {
        amount: 1200,
        currency: 'JPY',
        category: 'budget'
      },
      category: 'food',
      tags: ['拉面', '美食', '便宜', '地下街', '多选择'],
      difficulty: 'easy',
      crowdLevel: 'medium',
      seasonality: {
        best: ['all'],
      },
      tips: [
        '避开午餐高峰期12-13点',
        '推荐一兰拉面和六厘舍',
        '可以使用自动售票机点餐'
      ]
    }
  ];

  static getInstance(): TokyoActivityGenerator {
    if (!TokyoActivityGenerator.instance) {
      TokyoActivityGenerator.instance = new TokyoActivityGenerator();
    }
    return TokyoActivityGenerator.instance;
  }

  /**
   * 根据用户偏好生成东京活动
   */
  generateActivities(request: {
    duration: number; // 天数
    budget: number;
    preferences: {
      interests: string[];
      travelStyle: string[];
      accommodation: string[];
      transport: string[];
    };
    season?: string;
  }): TokyoActivity[] {
    console.log('🗼 开始生成东京活动:', request);

    // 1. 根据兴趣筛选活动
    let filteredActivities = this.filterByInterests(request.preferences.interests);
    
    // 2. 根据预算筛选
    filteredActivities = this.filterByBudget(filteredActivities, request.budget, request.duration);
    
    // 3. 根据季节筛选
    if (request.season) {
      filteredActivities = this.filterBySeason(filteredActivities, request.season);
    }
    
    // 4. 根据天数选择合适数量的活动
    const selectedActivities = this.selectActivitiesByDuration(filteredActivities, request.duration);
    
    console.log(`✅ 生成了${selectedActivities.length}个东京活动`);
    return selectedActivities;
  }

  private filterByInterests(interests: string[]): TokyoActivity[] {
    if (!interests || interests.length === 0) {
      return [...this.tokyoActivities];
    }

    const interestMap: { [key: string]: string[] } = {
      'cultural': ['cultural', 'historical'],
      'food': ['food'],
      'shopping': ['shopping'],
      'sightseeing': ['sightseeing', 'urban'],
      'nature': ['cultural'], // 明治神宫等
      'photography': ['sightseeing', 'urban'],
      'history': ['cultural'],
      'modern': ['urban', 'sightseeing'],
      'traditional': ['cultural']
    };

    const relevantCategories = new Set<string>();
    interests.forEach(interest => {
      const categories = interestMap[interest.toLowerCase()];
      if (categories) {
        categories.forEach(cat => relevantCategories.add(cat));
      }
    });

    if (relevantCategories.size === 0) {
      return [...this.tokyoActivities];
    }

    return this.tokyoActivities.filter(activity => 
      relevantCategories.has(activity.category) ||
      activity.tags.some(tag => interests.some(interest => 
        tag.toLowerCase().includes(interest.toLowerCase()) ||
        interest.toLowerCase().includes(tag.toLowerCase())
      ))
    );
  }

  private filterByBudget(activities: TokyoActivity[], totalBudget: number, days: number): TokyoActivity[] {
    const dailyBudget = totalBudget / days;
    const activityBudget = dailyBudget * 0.3; // 30%用于活动

    return activities.filter(activity => {
      switch (activity.cost.category) {
        case 'free': return true;
        case 'budget': return activityBudget >= 1000;
        case 'mid': return activityBudget >= 2000;
        case 'premium': return activityBudget >= 5000;
        case 'luxury': return activityBudget >= 10000;
        default: return true;
      }
    });
  }

  private filterBySeason(activities: TokyoActivity[], season: string): TokyoActivity[] {
    const seasonMap: { [key: string]: string } = {
      'winter': 'winter',
      '冬季': 'winter',
      'spring': 'spring',
      '春季': 'spring',
      'summer': 'summer',
      '夏季': 'summer',
      'autumn': 'autumn',
      'fall': 'autumn',
      '秋季': 'autumn'
    };

    const mappedSeason = seasonMap[season.toLowerCase()];
    if (!mappedSeason) {
      return activities;
    }

    return activities.filter(activity => 
      activity.seasonality.best.includes('all') ||
      activity.seasonality.best.includes(mappedSeason) ||
      !activity.seasonality.avoid?.includes(mappedSeason)
    );
  }

  private selectActivitiesByDuration(activities: TokyoActivity[], days: number): TokyoActivity[] {
    // 每天2-3个活动
    const targetCount = Math.min(days * 2.5, activities.length);
    
    // 确保包含必游景点
    const mustVisit = activities.filter(a => a.tags.includes('必游'));
    const others = activities.filter(a => !a.tags.includes('必游'));
    
    // 按类别平衡选择
    const selected: TokyoActivity[] = [];
    const categories = new Set(activities.map(a => a.category));
    
    // 先选择必游景点
    selected.push(...mustVisit.slice(0, Math.min(mustVisit.length, Math.ceil(targetCount * 0.6))));
    
    // 然后按类别平衡选择其他活动
    const remaining = targetCount - selected.length;
    const perCategory = Math.ceil(remaining / categories.size);
    
    categories.forEach(category => {
      const categoryActivities = others.filter(a => a.category === category);
      selected.push(...categoryActivities.slice(0, perCategory));
    });
    
    return selected.slice(0, targetCount);
  }

  /**
   * 将东京活动转换为标准活动格式
   */
  convertToStandardFormat(tokyoActivities: TokyoActivity[]): any[] {
    return tokyoActivities.map((activity, index) => ({
      id: activity.id,
      title: activity.name,
      name: activity.name,
      nameEn: activity.nameEn,
      description: activity.description,
      location: {
        name: activity.location.name,
        address: activity.location.address,
        district: activity.location.district,
        coordinates: activity.location.coordinates
      },
      timing: {
        day: Math.floor(index / 2.5) + 1, // 分配到不同天数
        duration: activity.timing.duration,
        startTime: this.generateStartTime(activity, index),
        endTime: this.generateEndTime(activity, index)
      },
      cost: activity.cost.amount,
      currency: activity.cost.currency,
      category: activity.category,
      type: activity.category,
      tags: activity.tags,
      difficulty: activity.difficulty,
      crowdLevel: activity.crowdLevel,
      tips: activity.tips,
      photos: [],
      notes: activity.tips.join('; '),
      isCompleted: false,
      createdAt: new Date(),
      updatedAt: new Date()
    }));
  }

  private generateStartTime(activity: TokyoActivity, index: number): string {
    const baseHours = [9, 11, 14, 16, 18]; // 不同时间段
    const hour = baseHours[index % baseHours.length];
    return `${hour.toString().padStart(2, '0')}:00`;
  }

  private generateEndTime(activity: TokyoActivity, index: number): string {
    const startHour = parseInt(this.generateStartTime(activity, index).split(':')[0]);
    const endHour = startHour + Math.floor(activity.timing.duration / 60);
    const endMinute = activity.timing.duration % 60;
    return `${endHour.toString().padStart(2, '0')}:${endMinute.toString().padStart(2, '0')}`;
  }
}

// 导出单例
export const tokyoActivityGenerator = TokyoActivityGenerator.getInstance();