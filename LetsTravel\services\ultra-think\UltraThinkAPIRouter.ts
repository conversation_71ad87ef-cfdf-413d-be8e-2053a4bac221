/**
 * 🌐 Ultra Think API Router - 智能API路由器
 * Phase 1: 核心架构重构 - API调用优先级管理
 * 管理三层API调用优先级策略，确保成本控制和性能优化
 */

import { ultraThinkConfig } from '../../config/UltraThinkConfig';

// ===== API路由器接口定义 =====

export interface APIEndpoint {
  name: string;
  url: string;
  tier: 1 | 2 | 3;
  priority: number;
  cost: number; // 每次调用成本
  rateLimit: {
    requests: number;
    window: number; // 时间窗口(毫秒)
  };
  timeout: number;
  retries: number;
  healthCheck: {
    enabled: boolean;
    interval: number;
    lastCheck?: Date;
    status: 'healthy' | 'degraded' | 'unhealthy';
  };
}

export interface APIRequest {
  type: 'places' | 'geocoding' | 'routing' | 'weather' | 'search' | 'osm';
  params: Record<string, any>;
  priority: 'low' | 'medium' | 'high';
  timeout?: number;
  fallbackEnabled?: boolean;
}

export interface APIResponse {
  success: boolean;
  data: any;
  source: string;
  tier: number;
  executionTime: number;
  cost: number;
  cached: boolean;
  fallbackUsed: boolean;
  metadata: {
    endpoint: string;
    retryCount: number;
    qualityScore: number;
    warnings: string[];
  };
}

export interface TierStrategy {
  tier: 1 | 2 | 3;
  name: string;
  description: string;
  endpoints: APIEndpoint[];
  fallbackTier?: number;
  costThreshold: number;
  qualityThreshold: number;
}

// ===== Ultra Think API Router 核心类 =====

export class UltraThinkAPIRouter {
  private static instance: UltraThinkAPIRouter;
  private config = ultraThinkConfig.getConfig();
  private tierStrategies = new Map<number, TierStrategy>();
  private requestHistory: APIResponse[] = [];
  private rateLimitTracker = new Map<string, { count: number; resetTime: number }>();
  private healthCheckInterval?: NodeJS.Timeout;

  private constructor() {
    this.initializeTierStrategies();
    this.startHealthChecking();
    console.log('🌐 Ultra Think API Router 初始化完成');
  }

  static getInstance(): UltraThinkAPIRouter {
    if (!UltraThinkAPIRouter.instance) {
      UltraThinkAPIRouter.instance = new UltraThinkAPIRouter();
    }
    return UltraThinkAPIRouter.instance;
  }

  /**
   * 🚀 智能API调用 - 主要入口方法
   */
  async callAPI(request: APIRequest): Promise<APIResponse> {
    const startTime = Date.now();
    console.log(`🌐 API路由器调用: ${request.type} (优先级: ${request.priority})`);

    try {
      // 1. 选择最优API层级
      const selectedTier = this.selectOptimalTier(request);
      console.log(`🎯 选择Tier ${selectedTier.tier}: ${selectedTier.name}`);

      // 2. 执行API调用
      const response = await this.executeAPICall(request, selectedTier, startTime);

      // 3. 记录调用历史
      this.recordAPICall(response);

      return response;

    } catch (error) {
      console.error('❌ API路由器调用失败:', error);
      return this.generateErrorResponse(request, startTime, error);
    }
  }

  /**
   * 🎯 选择最优API层级
   */
  private selectOptimalTier(request: APIRequest): TierStrategy {
    // 根据请求类型和优先级选择层级
    const availableTiers = Array.from(this.tierStrategies.values())
      .filter(tier => this.hasSuitableEndpoint(tier, request.type))
      .sort((a, b) => a.tier - b.tier); // 优先使用低层级(高优先级)

    // 高优先级请求优先使用Tier 1
    if (request.priority === 'high' && availableTiers.find(t => t.tier === 1)) {
      return availableTiers.find(t => t.tier === 1)!;
    }

    // 中等优先级请求优先使用Tier 2
    if (request.priority === 'medium' && availableTiers.find(t => t.tier === 2)) {
      return availableTiers.find(t => t.tier === 2)!;
    }

    // 低优先级请求使用Tier 3，如果不可用则降级
    return availableTiers.find(t => t.tier === 3) || availableTiers[0];
  }

  /**
   * ⚡ 执行API调用
   */
  private async executeAPICall(
    request: APIRequest,
    tier: TierStrategy,
    startTime: number
  ): Promise<APIResponse> {
    const suitableEndpoints = tier.endpoints.filter(endpoint => 
      this.isEndpointSuitable(endpoint, request.type)
    );

    // 按优先级和健康状态排序
    suitableEndpoints.sort((a, b) => {
      if (a.healthCheck.status !== b.healthCheck.status) {
        const statusPriority = { healthy: 3, degraded: 2, unhealthy: 1 };
        return statusPriority[b.healthCheck.status] - statusPriority[a.healthCheck.status];
      }
      return b.priority - a.priority;
    });

    let lastError: any;
    let retryCount = 0;

    for (const endpoint of suitableEndpoints) {
      try {
        // 检查速率限制
        if (!this.checkRateLimit(endpoint)) {
          console.warn(`⚠️ ${endpoint.name} 达到速率限制，跳过`);
          continue;
        }

        // 执行API调用
        console.log(`📡 调用API: ${endpoint.name}`);
        const response = await this.makeHTTPRequest(endpoint, request);

        return {
          success: true,
          data: response.data,
          source: endpoint.name,
          tier: tier.tier,
          executionTime: Date.now() - startTime,
          cost: endpoint.cost,
          cached: false,
          fallbackUsed: retryCount > 0,
          metadata: {
            endpoint: endpoint.url,
            retryCount,
            qualityScore: this.calculateQualityScore(response.data, endpoint),
            warnings: response.warnings || []
          }
        };

      } catch (error) {
        console.warn(`⚠️ ${endpoint.name} 调用失败:`, error);
        lastError = error;
        retryCount++;

        // 更新端点健康状态
        this.updateEndpointHealth(endpoint, false);
      }
    }

    // 所有端点都失败，尝试降级到下一层级
    if (tier.fallbackTier && request.fallbackEnabled !== false) {
      console.log(`🔄 降级到Tier ${tier.fallbackTier}`);
      const fallbackTier = this.tierStrategies.get(tier.fallbackTier);
      if (fallbackTier) {
        return await this.executeAPICall(request, fallbackTier, startTime);
      }
    }

    throw lastError || new Error('所有API端点都不可用');
  }

  /**
   * 🌐 执行HTTP请求
   */
  private async makeHTTPRequest(endpoint: APIEndpoint, request: APIRequest): Promise<any> {
    const url = this.buildRequestURL(endpoint, request);
    const options = this.buildRequestOptions(endpoint, request);

    console.log(`🔗 请求URL: ${url}`);

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), endpoint.timeout);

    try {
      const response = await fetch(url, {
        ...options,
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      // 更新端点健康状态
      this.updateEndpointHealth(endpoint, true);
      
      // 更新速率限制计数
      this.updateRateLimit(endpoint);

      return {
        data,
        warnings: this.extractWarnings(data, endpoint)
      };

    } catch (error) {
      clearTimeout(timeoutId);
      throw error;
    }
  }

  /**
   * 🔧 初始化层级策略
   */
  private initializeTierStrategies(): void {
    // Tier 1: 核心数据API (最高优先级)
    const tier1: TierStrategy = {
      tier: 1,
      name: 'Core Data APIs',
      description: '核心数据API，免费且高质量',
      costThreshold: 0,
      qualityThreshold: 0.9,
      fallbackTier: 2,
      endpoints: [
        {
          name: 'Google Places API',
          url: 'https://maps.googleapis.com/maps/api/place',
          tier: 1,
          priority: 100,
          cost: 0, // 免费额度内
          rateLimit: { requests: 1000, window: 60000 },
          timeout: 10000,
          retries: 2,
          healthCheck: { enabled: true, interval: 300000, status: 'healthy' }
        },
        {
          name: 'Nominatim API',
          url: 'https://nominatim.openstreetmap.org',
          tier: 1,
          priority: 90,
          cost: 0,
          rateLimit: { requests: 60, window: 60000 },
          timeout: 8000,
          retries: 2,
          healthCheck: { enabled: true, interval: 300000, status: 'healthy' }
        },
        {
          name: 'OSRM API',
          url: 'https://router.project-osrm.org',
          tier: 1,
          priority: 85,
          cost: 0,
          rateLimit: { requests: 100, window: 60000 },
          timeout: 12000,
          retries: 2,
          healthCheck: { enabled: true, interval: 300000, status: 'healthy' }
        }
      ]
    };

    // Tier 2: 增强数据API (高优先级)
    const tier2: TierStrategy = {
      tier: 2,
      name: 'Enhanced Data APIs',
      description: '增强数据API，免费但有限制',
      costThreshold: 0.001,
      qualityThreshold: 0.8,
      fallbackTier: 3,
      endpoints: [
        {
          name: 'Overpass API',
          url: 'https://overpass-api.de/api',
          tier: 2,
          priority: 80,
          cost: 0,
          rateLimit: { requests: 50, window: 60000 },
          timeout: 15000,
          retries: 2,
          healthCheck: { enabled: true, interval: 300000, status: 'healthy' }
        },
        {
          name: 'Wikipedia API',
          url: 'https://en.wikipedia.org/api/rest_v1',
          tier: 2,
          priority: 75,
          cost: 0,
          rateLimit: { requests: 200, window: 60000 },
          timeout: 8000,
          retries: 2,
          healthCheck: { enabled: true, interval: 300000, status: 'healthy' }
        },
        {
          name: 'OpenWeatherMap API',
          url: 'https://api.openweathermap.org/data/2.5',
          tier: 2,
          priority: 70,
          cost: 0, // 免费额度内
          rateLimit: { requests: 60, window: 60000 },
          timeout: 10000,
          retries: 2,
          healthCheck: { enabled: true, interval: 300000, status: 'healthy' }
        }
      ]
    };

    // Tier 3: 补充数据API (中等优先级)
    const tier3: TierStrategy = {
      tier: 3,
      name: 'Supplementary APIs',
      description: '补充数据API，付费但功能强大',
      costThreshold: 0.01,
      qualityThreshold: 0.7,
      endpoints: [
        {
          name: 'SerpAPI',
          url: 'https://serpapi.com/search',
          tier: 3,
          priority: 60,
          cost: 0.002,
          rateLimit: { requests: 100, window: 60000 },
          timeout: 12000,
          retries: 2,
          healthCheck: { enabled: true, interval: 300000, status: 'healthy' }
        },
        {
          name: 'Foursquare API',
          url: 'https://api.foursquare.com/v3',
          tier: 3,
          priority: 55,
          cost: 0.001,
          rateLimit: { requests: 50, window: 60000 },
          timeout: 10000,
          retries: 2,
          healthCheck: { enabled: true, interval: 300000, status: 'healthy' }
        }
      ]
    };

    this.tierStrategies.set(1, tier1);
    this.tierStrategies.set(2, tier2);
    this.tierStrategies.set(3, tier3);

    console.log('🔧 初始化3层API策略完成');
  }

  /**
   * 🔧 辅助方法
   */
  private hasSuitableEndpoint(tier: TierStrategy, requestType: string): boolean {
    return tier.endpoints.some(endpoint => this.isEndpointSuitable(endpoint, requestType));
  }

  private isEndpointSuitable(endpoint: APIEndpoint, requestType: string): boolean {
    const suitabilityMap = {
      'places': ['Google Places API', 'Foursquare API'],
      'geocoding': ['Nominatim API', 'Google Places API'],
      'routing': ['OSRM API', 'Google Places API'],
      'weather': ['OpenWeatherMap API'],
      'search': ['SerpAPI', 'Wikipedia API'],
      'osm': ['Overpass API', 'Nominatim API']
    };

    const suitableEndpoints = suitabilityMap[requestType as keyof typeof suitabilityMap] || [];
    return suitableEndpoints.includes(endpoint.name);
  }

  private checkRateLimit(endpoint: APIEndpoint): boolean {
    const key = endpoint.name;
    const now = Date.now();
    const tracker = this.rateLimitTracker.get(key);

    if (!tracker || now > tracker.resetTime) {
      this.rateLimitTracker.set(key, {
        count: 1,
        resetTime: now + endpoint.rateLimit.window
      });
      return true;
    }

    if (tracker.count >= endpoint.rateLimit.requests) {
      return false;
    }

    tracker.count++;
    return true;
  }

  private updateRateLimit(endpoint: APIEndpoint): void {
    const key = endpoint.name;
    const tracker = this.rateLimitTracker.get(key);
    if (tracker) {
      tracker.count++;
    }
  }

  private updateEndpointHealth(endpoint: APIEndpoint, success: boolean): void {
    if (success) {
      if (endpoint.healthCheck.status === 'unhealthy') {
        endpoint.healthCheck.status = 'degraded';
      } else if (endpoint.healthCheck.status === 'degraded') {
        endpoint.healthCheck.status = 'healthy';
      }
    } else {
      if (endpoint.healthCheck.status === 'healthy') {
        endpoint.healthCheck.status = 'degraded';
      } else if (endpoint.healthCheck.status === 'degraded') {
        endpoint.healthCheck.status = 'unhealthy';
      }
    }
    endpoint.healthCheck.lastCheck = new Date();
  }

  private calculateQualityScore(data: any, endpoint: APIEndpoint): number {
    let score = 0.5;
    
    if (data && typeof data === 'object') score += 0.2;
    if (endpoint.tier === 1) score += 0.2;
    if (endpoint.healthCheck.status === 'healthy') score += 0.1;
    
    return Math.min(1.0, score);
  }

  private buildRequestURL(endpoint: APIEndpoint, request: APIRequest): string {
    // 根据不同的API构建URL
    let url = endpoint.url;
    
    // 添加查询参数
    const params = new URLSearchParams();
    Object.entries(request.params).forEach(([key, value]) => {
      params.append(key, String(value));
    });
    
    return `${url}?${params.toString()}`;
  }

  private buildRequestOptions(endpoint: APIEndpoint, request: APIRequest): RequestInit {
    const options: RequestInit = {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'UltraThink/1.0'
      }
    };

    // 添加API密钥等认证信息
    if (endpoint.name === 'Google Places API') {
      // options.headers['Authorization'] = `Bearer ${process.env.GOOGLE_API_KEY}`;
    }

    return options;
  }

  private extractWarnings(data: any, endpoint: APIEndpoint): string[] {
    const warnings: string[] = [];
    
    if (!data || Object.keys(data).length === 0) {
      warnings.push('API返回空数据');
    }
    
    if (endpoint.healthCheck.status !== 'healthy') {
      warnings.push(`端点${endpoint.name}健康状态: ${endpoint.healthCheck.status}`);
    }
    
    return warnings;
  }

  private startHealthChecking(): void {
    this.healthCheckInterval = setInterval(() => {
      this.performHealthChecks();
    }, 5 * 60 * 1000); // 每5分钟检查一次
  }

  private async performHealthChecks(): Promise<void> {
    console.log('🏥 执行API健康检查');
    
    for (const tier of this.tierStrategies.values()) {
      for (const endpoint of tier.endpoints) {
        if (endpoint.healthCheck.enabled) {
          try {
            // 简单的健康检查请求
            const response = await fetch(endpoint.url, { 
              method: 'HEAD',
              timeout: 5000 
            } as any);
            
            this.updateEndpointHealth(endpoint, response.ok);
          } catch (error) {
            this.updateEndpointHealth(endpoint, false);
          }
        }
      }
    }
  }

  private recordAPICall(response: APIResponse): void {
    this.requestHistory.push(response);
    
    // 保持历史记录在合理范围内
    if (this.requestHistory.length > 1000) {
      this.requestHistory = this.requestHistory.slice(-500);
    }
  }

  private generateErrorResponse(request: APIRequest, startTime: number, error: any): APIResponse {
    return {
      success: false,
      data: null,
      source: 'error-fallback',
      tier: 3,
      executionTime: Date.now() - startTime,
      cost: 0,
      cached: false,
      fallbackUsed: true,
      metadata: {
        endpoint: 'none',
        retryCount: 0,
        qualityScore: 0,
        warnings: [`API调用失败: ${error?.message || '未知错误'}`]
      }
    };
  }

  /**
   * 📊 获取API使用统计
   */
  getAPIStats() {
    const totalCalls = this.requestHistory.length;
    const successfulCalls = this.requestHistory.filter(r => r.success).length;
    const totalCost = this.requestHistory.reduce((sum, r) => sum + r.cost, 0);
    
    return {
      totalCalls,
      successRate: totalCalls > 0 ? successfulCalls / totalCalls : 0,
      totalCost,
      averageCost: totalCalls > 0 ? totalCost / totalCalls : 0,
      tierUsage: this.getTierUsageStats()
    };
  }

  private getTierUsageStats() {
    const tierCounts = { 1: 0, 2: 0, 3: 0 };
    this.requestHistory.forEach(r => {
      tierCounts[r.tier as keyof typeof tierCounts]++;
    });
    return tierCounts;
  }
}
