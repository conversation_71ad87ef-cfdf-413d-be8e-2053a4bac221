/**
 * 🔀 Decision Fusion - 智能决策融合器
 * Phase 2: 智能偏好引擎构建 - 多维度数据融合
 * 实现多维度数据融合和智能决策算法
 */

import { SeasonalAI, SeasonalAnalysis } from './SeasonalAI';
import { PersonalityAI, TravelPersonality } from './PersonalityAI';
import { FestivalCore, CulturalCalendar } from './FestivalCore';
import { UltraThinkCacheManager } from '../UltraThinkCacheManager';

// ===== 决策融合接口定义 =====

export interface FusionInput {
  seasonalAnalysis: SeasonalAnalysis;
  personalityProfile: TravelPersonality;
  culturalCalendar: CulturalCalendar;
  contextData: {
    destination: string;
    duration: number;
    budget: number;
    travelers: number;
    travelDates: { start: Date; end: Date };
    preferences: Record<string, any>;
  };
}

export interface DecisionWeight {
  source: 'seasonal' | 'personality' | 'cultural' | 'context';
  factor: string;
  weight: number; // 0-1
  confidence: number; // 0-1
  reasoning: string;
}

export interface FusedRecommendation {
  id: string;
  type: 'activity' | 'timing' | 'cultural' | 'food' | 'accommodation' | 'transport';
  title: string;
  description: string;
  priority: 'high' | 'medium' | 'low';
  confidence: number;
  fusionScore: number; // 综合评分 0-1
  supportingFactors: {
    seasonal: number;
    personality: number;
    cultural: number;
    context: number;
  };
  reasoning: string[];
  alternatives: string[];
  warnings?: string[];
  metadata: {
    sources: string[];
    weights: DecisionWeight[];
    qualityScore: number;
  };
}

export interface FusionResult {
  recommendations: FusedRecommendation[];
  overallStrategy: {
    primaryFocus: string;
    secondaryFocus: string[];
    avoidanceFactors: string[];
    optimizationGoals: string[];
  };
  confidenceMetrics: {
    overall: number;
    seasonal: number;
    personality: number;
    cultural: number;
    dataQuality: number;
  };
  decisionPath: string[];
  metadata: {
    fusionDate: Date;
    inputSources: string[];
    processingTime: number;
    version: string;
  };
}

// ===== Decision Fusion 核心类 =====

export class DecisionFusion {
  private static instance: DecisionFusion;
  private seasonalAI = SeasonalAI.getInstance();
  private personalityAI = PersonalityAI.getInstance();
  private festivalCore = FestivalCore.getInstance();
  private cacheManager = UltraThinkCacheManager.getInstance();
  private fusionRules = new Map<string, any>();

  private constructor() {
    this.initializeFusionRules();
    console.log('🔀 Decision Fusion 初始化完成');
  }

  static getInstance(): DecisionFusion {
    if (!DecisionFusion.instance) {
      DecisionFusion.instance = new DecisionFusion();
    }
    return DecisionFusion.instance;
  }

  /**
   * 🧠 智能决策融合 - 主要入口方法
   */
  async fuseDecisions(input: FusionInput): Promise<FusionResult> {
    const startTime = Date.now();
    console.log(`🔀 开始决策融合: ${input.contextData.destination}`);

    try {
      // 1. 检查缓存
      const cacheKey = this.generateCacheKey(input);
      const cached = await this.cacheManager.get<FusionResult>(cacheKey, 'decision-fusion');
      
      if (cached && this.isCacheValid(cached)) {
        console.log('✅ 决策融合缓存命中');
        return cached;
      }

      // 2. 计算决策权重
      const decisionWeights = this.calculateDecisionWeights(input);
      console.log(`⚖️ 决策权重: 季节${decisionWeights.seasonal.toFixed(2)}, 个性${decisionWeights.personality.toFixed(2)}, 文化${decisionWeights.cultural.toFixed(2)}`);

      // 3. 融合推荐
      const fusedRecommendations = await this.fuseRecommendations(input, decisionWeights);
      console.log(`🎯 生成${fusedRecommendations.length}个融合推荐`);

      // 4. 确定整体策略
      const overallStrategy = this.determineOverallStrategy(input, decisionWeights);

      // 5. 计算置信度指标
      const confidenceMetrics = this.calculateConfidenceMetrics(input, fusedRecommendations);

      // 6. 生成决策路径
      const decisionPath = this.generateDecisionPath(input, decisionWeights);

      // 7. 构建融合结果
      const result: FusionResult = {
        recommendations: fusedRecommendations,
        overallStrategy,
        confidenceMetrics,
        decisionPath,
        metadata: {
          fusionDate: new Date(),
          inputSources: ['seasonal', 'personality', 'cultural', 'context'],
          processingTime: Date.now() - startTime,
          version: '2.0'
        }
      };

      // 8. 缓存结果
      await this.cacheManager.set(cacheKey, result, 'decision-fusion', 2 * 60 * 60 * 1000); // 2小时

      console.log(`✅ 决策融合完成: 整体置信度${confidenceMetrics.overall.toFixed(2)}`);
      return result;

    } catch (error) {
      console.error('❌ 决策融合失败:', error);
      return this.generateFallbackResult(input, startTime);
    }
  }

  /**
   * ⚖️ 计算决策权重
   */
  private calculateDecisionWeights(input: FusionInput): any {
    const weights = {
      seasonal: 0.25,
      personality: 0.35,
      cultural: 0.25,
      context: 0.15
    };

    // 根据数据质量调整权重
    const seasonalConfidence = input.seasonalAnalysis.metadata.confidence;
    const personalityConfidence = input.personalityProfile.metadata.confidence;
    const culturalConfidence = input.culturalCalendar.metadata.confidence;

    // 高置信度数据获得更高权重
    if (seasonalConfidence > 0.8) weights.seasonal += 0.1;
    if (personalityConfidence > 0.8) weights.personality += 0.1;
    if (culturalConfidence > 0.8) weights.cultural += 0.1;

    // 根据旅行类型调整权重
    const travelStyle = input.personalityProfile.travelStyle;
    if (travelStyle.includes('文化')) {
      weights.cultural += 0.15;
      weights.personality -= 0.05;
    }

    // 根据季节重要性调整权重
    const seasonalScore = input.seasonalAnalysis.seasonalScore;
    if (seasonalScore > 0.8) {
      weights.seasonal += 0.1;
      weights.context -= 0.05;
    }

    // 归一化权重
    const totalWeight = Object.values(weights).reduce((sum, w) => sum + w, 0);
    Object.keys(weights).forEach(key => {
      weights[key as keyof typeof weights] /= totalWeight;
    });

    return weights;
  }

  /**
   * 🎯 融合推荐
   */
  private async fuseRecommendations(input: FusionInput, weights: any): Promise<FusedRecommendation[]> {
    const fusedRecommendations: FusedRecommendation[] = [];

    // 1. 融合季节性推荐
    const seasonalRecs = this.fuseSeasonalRecommendations(input, weights);
    fusedRecommendations.push(...seasonalRecs);

    // 2. 融合个性化推荐
    const personalityRecs = this.fusePersonalityRecommendations(input, weights);
    fusedRecommendations.push(...personalityRecs);

    // 3. 融合文化推荐
    const culturalRecs = this.fuseCulturalRecommendations(input, weights);
    fusedRecommendations.push(...culturalRecs);

    // 4. 融合上下文推荐
    const contextRecs = this.fuseContextRecommendations(input, weights);
    fusedRecommendations.push(...contextRecs);

    // 5. 去重和排序
    const uniqueRecs = this.deduplicateRecommendations(fusedRecommendations);
    return this.sortRecommendationsByScore(uniqueRecs);
  }

  /**
   * 🌸 融合季节性推荐
   */
  private fuseSeasonalRecommendations(input: FusionInput, weights: any): FusedRecommendation[] {
    const recommendations: FusedRecommendation[] = [];
    const seasonal = input.seasonalAnalysis;

    seasonal.recommendations.forEach((rec, index) => {
      const fusionScore = this.calculateFusionScore({
        seasonal: 0.9,
        personality: this.getPersonalityMatch(rec, input.personalityProfile),
        cultural: this.getCulturalMatch(rec, input.culturalCalendar),
        context: this.getContextMatch(rec, input.contextData)
      }, weights);

      recommendations.push({
        id: `seasonal_${index}`,
        type: rec.category as any,
        title: rec.title,
        description: rec.description,
        priority: rec.priority,
        confidence: seasonal.metadata.confidence,
        fusionScore,
        supportingFactors: {
          seasonal: 0.9,
          personality: this.getPersonalityMatch(rec, input.personalityProfile),
          cultural: this.getCulturalMatch(rec, input.culturalCalendar),
          context: this.getContextMatch(rec, input.contextData)
        },
        reasoning: [rec.reasoning, rec.seasonalAdvantage],
        alternatives: rec.alternatives || [],
        warnings: rec.warnings,
        metadata: {
          sources: ['seasonal'],
          weights: [{ source: 'seasonal', factor: rec.category, weight: weights.seasonal, confidence: seasonal.metadata.confidence, reasoning: rec.reasoning }],
          qualityScore: seasonal.seasonalScore
        }
      });
    });

    return recommendations;
  }

  /**
   * 🧠 融合个性化推荐
   */
  private fusePersonalityRecommendations(input: FusionInput, weights: any): FusedRecommendation[] {
    const recommendations: FusedRecommendation[] = [];
    const personality = input.personalityProfile;

    personality.preferences.activities.forEach((activity, index) => {
      const fusionScore = this.calculateFusionScore({
        seasonal: this.getSeasonalMatch(activity, input.seasonalAnalysis),
        personality: 0.9,
        cultural: this.getCulturalActivityMatch(activity, input.culturalCalendar),
        context: this.getContextActivityMatch(activity, input.contextData)
      }, weights);

      recommendations.push({
        id: `personality_${index}`,
        type: 'activity',
        title: `个性化${activity}体验`,
        description: `基于您的${personality.personalityType}特质推荐的${activity}活动`,
        priority: 'medium',
        confidence: personality.metadata.confidence,
        fusionScore,
        supportingFactors: {
          seasonal: this.getSeasonalMatch(activity, input.seasonalAnalysis),
          personality: 0.9,
          cultural: this.getCulturalActivityMatch(activity, input.culturalCalendar),
          context: this.getContextActivityMatch(activity, input.contextData)
        },
        reasoning: [`符合您的${personality.personalityType}特质`, `匹配您的${personality.travelStyle}风格`],
        alternatives: personality.recommendations.recommendedActivities.filter(a => a !== activity).slice(0, 2),
        metadata: {
          sources: ['personality'],
          weights: [{ source: 'personality', factor: 'activity-preference', weight: weights.personality, confidence: personality.metadata.confidence, reasoning: '基于个性分析' }],
          qualityScore: personality.metadata.confidence
        }
      });
    });

    return recommendations;
  }

  /**
   * 🎭 融合文化推荐
   */
  private fuseCulturalRecommendations(input: FusionInput, weights: any): FusedRecommendation[] {
    const recommendations: FusedRecommendation[] = [];
    const cultural = input.culturalCalendar;

    cultural.recommendations.forEach((rec, index) => {
      const fusionScore = this.calculateFusionScore({
        seasonal: this.getSeasonalCulturalMatch(rec, input.seasonalAnalysis),
        personality: this.getPersonalityCulturalMatch(rec, input.personalityProfile),
        cultural: rec.relevanceScore,
        context: this.getContextCulturalMatch(rec, input.contextData)
      }, weights);

      recommendations.push({
        id: `cultural_${index}`,
        type: 'cultural',
        title: rec.festival.name,
        description: rec.festival.description,
        priority: rec.relevanceScore > 0.8 ? 'high' : rec.relevanceScore > 0.6 ? 'medium' : 'low',
        confidence: cultural.metadata.confidence,
        fusionScore,
        supportingFactors: {
          seasonal: this.getSeasonalCulturalMatch(rec, input.seasonalAnalysis),
          personality: this.getPersonalityCulturalMatch(rec, input.personalityProfile),
          cultural: rec.relevanceScore,
          context: this.getContextCulturalMatch(rec, input.contextData)
        },
        reasoning: rec.matchReasons,
        alternatives: rec.alternatives.map(alt => alt.name),
        warnings: rec.warnings,
        metadata: {
          sources: ['cultural'],
          weights: [{ source: 'cultural', factor: 'festival', weight: weights.cultural, confidence: cultural.metadata.confidence, reasoning: '基于文化节日分析' }],
          qualityScore: rec.festival.culturalValue
        }
      });
    });

    return recommendations;
  }

  /**
   * 📊 融合上下文推荐
   */
  private fuseContextRecommendations(input: FusionInput, weights: any): FusedRecommendation[] {
    const recommendations: FusedRecommendation[] = [];
    const context = input.contextData;

    // 基于预算的推荐
    if (context.budget) {
      const budgetRec = this.generateBudgetRecommendation(input, weights);
      recommendations.push(budgetRec);
    }

    // 基于时长的推荐
    if (context.duration) {
      const durationRec = this.generateDurationRecommendation(input, weights);
      recommendations.push(durationRec);
    }

    // 基于人数的推荐
    if (context.travelers > 1) {
      const groupRec = this.generateGroupRecommendation(input, weights);
      recommendations.push(groupRec);
    }

    return recommendations;
  }

  /**
   * 🔧 初始化融合规则
   */
  private initializeFusionRules(): void {
    const rules = {
      activityMatching: {
        '文化体验': ['博物馆', '历史', '传统', '艺术'],
        '户外活动': ['徒步', '登山', '海滨', '自然'],
        '美食探索': ['餐厅', '小吃', '市场', '料理'],
        '购物娱乐': ['商场', '市集', '夜市', '娱乐']
      },
      seasonalBonus: {
        spring: ['赏花', '踏青', '户外'],
        summer: ['海滨', '水上', '夜市'],
        autumn: ['红叶', '徒步', '温泉'],
        winter: ['温泉', '室内', '节庆']
      },
      personalityMapping: {
        '自由探险家': ['冒险', '探索', '未知'],
        '文化学者': ['博物馆', '历史', '传统'],
        '安全规划者': ['安全', '计划', '舒适'],
        '平衡旅行者': ['多样', '平衡', '适中']
      }
    };

    Object.entries(rules).forEach(([category, data]) => {
      this.fusionRules.set(category, data);
    });
  }

  /**
   * 🔧 辅助方法
   */
  private calculateFusionScore(factors: any, weights: any): number {
    return (
      factors.seasonal * weights.seasonal +
      factors.personality * weights.personality +
      factors.cultural * weights.cultural +
      factors.context * weights.context
    );
  }

  private getPersonalityMatch(rec: any, personality: TravelPersonality): number {
    const activityRules = this.fusionRules.get('activityMatching');
    const personalityRules = this.fusionRules.get('personalityMapping');
    
    const personalityKeywords = personalityRules?.[personality.personalityType] || [];
    const recKeywords = rec.title.toLowerCase() + ' ' + rec.description.toLowerCase();
    
    const matches = personalityKeywords.filter((keyword: string) => 
      recKeywords.includes(keyword.toLowerCase())
    ).length;
    
    return Math.min(1.0, matches / personalityKeywords.length);
  }

  private getCulturalMatch(rec: any, cultural: CulturalCalendar): number {
    const culturalKeywords = cultural.culturalHighlights.join(' ').toLowerCase();
    const recKeywords = rec.title.toLowerCase() + ' ' + rec.description.toLowerCase();
    
    return culturalKeywords.split(' ').some(keyword => 
      recKeywords.includes(keyword)
    ) ? 0.8 : 0.3;
  }

  private getContextMatch(rec: any, context: any): number {
    let score = 0.5;
    
    // 基于预算匹配
    if (context.budget > 5000 && rec.category === 'luxury') score += 0.3;
    if (context.budget < 2000 && rec.category === 'budget') score += 0.3;
    
    // 基于时长匹配
    if (context.duration > 7 && rec.category === 'cultural') score += 0.2;
    if (context.duration <= 3 && rec.category === 'activity') score += 0.2;
    
    return Math.min(1.0, score);
  }

  private getSeasonalMatch(activity: string, seasonal: SeasonalAnalysis): number {
    const seasonalBonus = this.fusionRules.get('seasonalBonus');
    const seasonActivities = seasonalBonus?.[seasonal.season] || [];
    
    return seasonActivities.some((sa: string) => 
      activity.toLowerCase().includes(sa.toLowerCase())
    ) ? 0.8 : 0.4;
  }

  private getCulturalActivityMatch(activity: string, cultural: CulturalCalendar): number {
    return cultural.culturalHighlights.some(highlight => 
      activity.toLowerCase().includes(highlight.toLowerCase())
    ) ? 0.7 : 0.3;
  }

  private getContextActivityMatch(activity: string, context: any): number {
    // 简化的上下文匹配
    return 0.5;
  }

  private getSeasonalCulturalMatch(rec: any, seasonal: SeasonalAnalysis): number {
    return seasonal.bestActivities.some(activity => 
      rec.festival.activities.includes(activity)
    ) ? 0.8 : 0.4;
  }

  private getPersonalityCulturalMatch(rec: any, personality: TravelPersonality): number {
    return personality.preferences.activities.some(activity => 
      rec.festival.activities.some((fa: string) => fa.toLowerCase().includes(activity.toLowerCase()))
    ) ? 0.8 : 0.4;
  }

  private getContextCulturalMatch(rec: any, context: any): number {
    // 基于预算和人群的文化匹配
    let score = 0.5;
    
    if (rec.festival.costLevel === 'free' && context.budget < 2000) score += 0.3;
    if (rec.festival.touristFriendly > 0.8) score += 0.2;
    
    return Math.min(1.0, score);
  }

  private generateBudgetRecommendation(input: FusionInput, weights: any): FusedRecommendation {
    const budget = input.contextData.budget;
    let budgetLevel = 'mid-range';
    
    if (budget < 2000) budgetLevel = 'budget';
    else if (budget > 8000) budgetLevel = 'luxury';
    
    return {
      id: 'context_budget',
      type: 'accommodation',
      title: `${budgetLevel}住宿推荐`,
      description: `基于您的预算${budget}元推荐的住宿选择`,
      priority: 'medium',
      confidence: 0.8,
      fusionScore: 0.7,
      supportingFactors: { seasonal: 0.3, personality: 0.5, cultural: 0.3, context: 0.9 },
      reasoning: [`预算${budget}元适合${budgetLevel}级别住宿`],
      alternatives: [],
      metadata: {
        sources: ['context'],
        weights: [{ source: 'context', factor: 'budget', weight: weights.context, confidence: 0.8, reasoning: '基于预算分析' }],
        qualityScore: 0.7
      }
    };
  }

  private generateDurationRecommendation(input: FusionInput, weights: any): FusedRecommendation {
    const duration = input.contextData.duration;
    let paceStyle = 'moderate';
    
    if (duration <= 3) paceStyle = 'intensive';
    else if (duration >= 7) paceStyle = 'relaxed';
    
    return {
      id: 'context_duration',
      type: 'timing',
      title: `${duration}天${paceStyle}行程安排`,
      description: `基于${duration}天行程的节奏建议`,
      priority: 'high',
      confidence: 0.9,
      fusionScore: 0.8,
      supportingFactors: { seasonal: 0.4, personality: 0.6, cultural: 0.4, context: 0.9 },
      reasoning: [`${duration}天行程适合${paceStyle}节奏`],
      alternatives: [],
      metadata: {
        sources: ['context'],
        weights: [{ source: 'context', factor: 'duration', weight: weights.context, confidence: 0.9, reasoning: '基于行程时长分析' }],
        qualityScore: 0.8
      }
    };
  }

  private generateGroupRecommendation(input: FusionInput, weights: any): FusedRecommendation {
    const travelers = input.contextData.travelers;
    
    return {
      id: 'context_group',
      type: 'activity',
      title: `${travelers}人团体活动推荐`,
      description: `适合${travelers}人参与的团体活动`,
      priority: 'medium',
      confidence: 0.7,
      fusionScore: 0.6,
      supportingFactors: { seasonal: 0.4, personality: 0.5, cultural: 0.5, context: 0.8 },
      reasoning: [`${travelers}人团体适合集体活动`],
      alternatives: [],
      metadata: {
        sources: ['context'],
        weights: [{ source: 'context', factor: 'group-size', weight: weights.context, confidence: 0.7, reasoning: '基于团体规模分析' }],
        qualityScore: 0.6
      }
    };
  }

  private determineOverallStrategy(input: FusionInput, weights: any): any {
    const maxWeight = Math.max(...Object.values(weights));
    const primarySource = Object.keys(weights).find(key => weights[key] === maxWeight);
    
    return {
      primaryFocus: this.getPrimaryFocus(primarySource, input),
      secondaryFocus: this.getSecondaryFocus(weights, input),
      avoidanceFactors: this.getAvoidanceFactors(input),
      optimizationGoals: this.getOptimizationGoals(input)
    };
  }

  private getPrimaryFocus(source: string | undefined, input: FusionInput): string {
    switch (source) {
      case 'seasonal': return `${input.seasonalAnalysis.season}季节体验`;
      case 'personality': return `${input.personalityProfile.personalityType}风格`;
      case 'cultural': return '文化深度体验';
      case 'context': return '实用性优先';
      default: return '平衡体验';
    }
  }

  private getSecondaryFocus(weights: any, input: FusionInput): string[] {
    return Object.entries(weights)
      .sort(([,a], [,b]) => (b as number) - (a as number))
      .slice(1, 3)
      .map(([key]) => key);
  }

  private getAvoidanceFactors(input: FusionInput): string[] {
    const factors: string[] = [];
    
    if (input.personalityProfile.preferences.avoidances.length > 0) {
      factors.push(...input.personalityProfile.preferences.avoidances);
    }
    
    if (input.seasonalAnalysis.avoidActivities.length > 0) {
      factors.push(...input.seasonalAnalysis.avoidActivities);
    }
    
    return factors;
  }

  private getOptimizationGoals(input: FusionInput): string[] {
    return [
      '最大化文化体验',
      '优化时间安排',
      '控制预算成本',
      '提升舒适度'
    ];
  }

  private calculateConfidenceMetrics(input: FusionInput, recommendations: FusedRecommendation[]): any {
    const seasonal = input.seasonalAnalysis.metadata.confidence;
    const personality = input.personalityProfile.metadata.confidence;
    const cultural = input.culturalCalendar.metadata.confidence;
    const dataQuality = (seasonal + personality + cultural) / 3;
    
    const avgRecommendationConfidence = recommendations.length > 0 
      ? recommendations.reduce((sum, rec) => sum + rec.confidence, 0) / recommendations.length
      : 0.5;
    
    return {
      overall: (dataQuality + avgRecommendationConfidence) / 2,
      seasonal,
      personality,
      cultural,
      dataQuality
    };
  }

  private generateDecisionPath(input: FusionInput, weights: any): string[] {
    return [
      '数据收集和验证',
      '权重计算和调整',
      '多维度推荐融合',
      '质量评估和排序',
      '策略确定和优化'
    ];
  }

  private deduplicateRecommendations(recommendations: FusedRecommendation[]): FusedRecommendation[] {
    const seen = new Set<string>();
    return recommendations.filter(rec => {
      const key = `${rec.type}_${rec.title}`;
      if (seen.has(key)) return false;
      seen.add(key);
      return true;
    });
  }

  private sortRecommendationsByScore(recommendations: FusedRecommendation[]): FusedRecommendation[] {
    return recommendations.sort((a, b) => b.fusionScore - a.fusionScore);
  }

  private generateCacheKey(input: FusionInput): string {
    return `fusion_${input.contextData.destination}_${input.contextData.duration}_${input.contextData.budget}`;
  }

  private isCacheValid(result: FusionResult): boolean {
    const hoursSinceCreation = (Date.now() - result.metadata.fusionDate.getTime()) / (1000 * 60 * 60);
    return hoursSinceCreation < 2; // 2小时内有效
  }

  private generateFallbackResult(input: FusionInput, startTime: number): FusionResult {
    return {
      recommendations: [],
      overallStrategy: {
        primaryFocus: '平衡体验',
        secondaryFocus: ['实用性', '舒适度'],
        avoidanceFactors: [],
        optimizationGoals: ['基础体验保障']
      },
      confidenceMetrics: {
        overall: 0.5,
        seasonal: 0.5,
        personality: 0.5,
        cultural: 0.5,
        dataQuality: 0.5
      },
      decisionPath: ['降级处理'],
      metadata: {
        fusionDate: new Date(),
        inputSources: ['fallback'],
        processingTime: Date.now() - startTime,
        version: '2.0'
      }
    };
  }
}
