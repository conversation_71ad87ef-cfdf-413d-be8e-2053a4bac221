/**
 * 🎨 Ultra Think UI Engine - UI渲染引擎
 * Phase 5: UI简化和体验优化 - UI渲染引擎
 * 实现统一颜色系统和智能图标匹配，简化UI设计
 */

import { UltraThinkCacheManager } from '../UltraThinkCacheManager';

// ===== UI引擎接口定义 =====

export interface ColorScheme {
  primary: string;
  secondary: string;
  accent: string;
  background: string;
  surface: string;
  text: {
    primary: string;
    secondary: string;
    disabled: string;
  };
  semantic: {
    success: string;
    warning: string;
    error: string;
    info: string;
  };
  activity: {
    transport: string;
    food: string;
    culture: string;
    nature: string;
    shopping: string;
    entertainment: string;
  };
}

export interface IconMapping {
  activity: string;
  category: string;
  icon: string;
  color: string;
  size: 'small' | 'medium' | 'large';
  style: 'outline' | 'filled' | 'duotone';
}

export interface UITheme {
  id: string;
  name: string;
  colorScheme: ColorScheme;
  typography: {
    fontFamily: string;
    sizes: Record<string, string>;
    weights: Record<string, number>;
  };
  spacing: Record<string, string>;
  borderRadius: Record<string, string>;
  shadows: Record<string, string>;
  animations: Record<string, string>;
}

export interface ComponentStyle {
  component: string;
  variant: string;
  styles: Record<string, any>;
  responsive: {
    mobile: Record<string, any>;
    tablet: Record<string, any>;
    desktop: Record<string, any>;
  };
}

export interface RenderRequest {
  component: string;
  data: any;
  theme?: string;
  variant?: string;
  customStyles?: Record<string, any>;
  responsive?: boolean;
  accessibility?: boolean;
}

export interface RenderResult {
  html: string;
  css: string;
  javascript?: string;
  metadata: {
    component: string;
    theme: string;
    renderTime: number;
    cacheHit: boolean;
  };
}

// ===== Ultra Think UI Engine 核心类 =====

export class UltraThinkUIEngine {
  private static instance: UltraThinkUIEngine;
  private cacheManager = UltraThinkCacheManager.getInstance();
  private themes = new Map<string, UITheme>();
  private iconMappings = new Map<string, IconMapping>();
  private componentStyles = new Map<string, ComponentStyle>();
  private colorPalette = new Map<string, string>();

  private constructor() {
    this.initializeThemes();
    this.initializeIconMappings();
    this.initializeComponentStyles();
    this.initializeColorPalette();
    console.log('🎨 Ultra Think UI Engine 初始化完成');
  }

  static getInstance(): UltraThinkUIEngine {
    if (!UltraThinkUIEngine.instance) {
      UltraThinkUIEngine.instance = new UltraThinkUIEngine();
    }
    return UltraThinkUIEngine.instance;
  }

  /**
   * 🎨 渲染组件 - 主要入口方法
   */
  async renderComponent(request: RenderRequest): Promise<RenderResult> {
    const startTime = Date.now();
    console.log(`🎨 开始渲染组件: ${request.component}`);

    try {
      // 1. 检查缓存
      const cacheKey = this.generateCacheKey(request);
      const cached = await this.cacheManager.get<RenderResult>(cacheKey, 'ui-components');
      
      if (cached && this.isCacheValid(cached)) {
        console.log('✅ UI组件缓存命中');
        cached.metadata.cacheHit = true;
        return cached;
      }

      // 2. 获取主题
      const theme = this.getTheme(request.theme || 'default');

      // 3. 获取组件样式
      const componentStyle = this.getComponentStyle(request.component, request.variant || 'default');

      // 4. 智能图标匹配
      const icons = this.matchIcons(request.data);

      // 5. 生成响应式样式
      const responsiveStyles = request.responsive ? this.generateResponsiveStyles(componentStyle, theme) : {};

      // 6. 渲染HTML
      const html = this.renderHTML(request, theme, icons);

      // 7. 生成CSS
      const css = this.generateCSS(componentStyle, theme, responsiveStyles, request.customStyles);

      // 8. 生成JavaScript (如果需要)
      const javascript = this.generateJavaScript(request);

      // 9. 构建结果
      const result: RenderResult = {
        html,
        css,
        javascript,
        metadata: {
          component: request.component,
          theme: theme.name,
          renderTime: Date.now() - startTime,
          cacheHit: false
        }
      };

      // 10. 缓存结果
      await this.cacheRenderResult(result, cacheKey);

      console.log(`✅ 组件渲染完成: ${request.component}, 耗时${result.metadata.renderTime}ms`);
      return result;

    } catch (error) {
      console.error('❌ 组件渲染失败:', error);
      return this.generateFallbackResult(request, startTime);
    }
  }

  /**
   * 🎯 智能图标匹配
   */
  matchIcons(data: any): IconMapping[] {
    const matchedIcons: IconMapping[] = [];

    if (data.activities) {
      data.activities.forEach((activity: any) => {
        const icon = this.findBestIcon(activity);
        if (icon) {
          matchedIcons.push(icon);
        }
      });
    }

    if (data.category) {
      const categoryIcon = this.findBestIcon({ category: data.category });
      if (categoryIcon) {
        matchedIcons.push(categoryIcon);
      }
    }

    return matchedIcons;
  }

  /**
   * 🔧 初始化主题
   */
  private initializeThemes(): void {
    const defaultTheme: UITheme = {
      id: 'default',
      name: 'Ultra Think Default',
      colorScheme: {
        primary: '#2563eb',
        secondary: '#64748b',
        accent: '#f59e0b',
        background: '#ffffff',
        surface: '#f8fafc',
        text: {
          primary: '#1e293b',
          secondary: '#64748b',
          disabled: '#cbd5e1'
        },
        semantic: {
          success: '#10b981',
          warning: '#f59e0b',
          error: '#ef4444',
          info: '#3b82f6'
        },
        activity: {
          transport: '#6b7280',
          food: '#f59e0b',
          culture: '#8b5cf6',
          nature: '#10b981',
          shopping: '#ec4899',
          entertainment: '#f97316'
        }
      },
      typography: {
        fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
        sizes: {
          xs: '0.75rem',
          sm: '0.875rem',
          base: '1rem',
          lg: '1.125rem',
          xl: '1.25rem',
          '2xl': '1.5rem',
          '3xl': '1.875rem'
        },
        weights: {
          normal: 400,
          medium: 500,
          semibold: 600,
          bold: 700
        }
      },
      spacing: {
        xs: '0.25rem',
        sm: '0.5rem',
        md: '1rem',
        lg: '1.5rem',
        xl: '2rem',
        '2xl': '3rem'
      },
      borderRadius: {
        sm: '0.25rem',
        md: '0.375rem',
        lg: '0.5rem',
        xl: '0.75rem',
        full: '9999px'
      },
      shadows: {
        sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
        md: '0 4px 6px -1px rgb(0 0 0 / 0.1)',
        lg: '0 10px 15px -3px rgb(0 0 0 / 0.1)',
        xl: '0 20px 25px -5px rgb(0 0 0 / 0.1)'
      },
      animations: {
        fadeIn: 'fadeIn 0.3s ease-in-out',
        slideUp: 'slideUp 0.3s ease-out',
        bounce: 'bounce 0.5s ease-in-out'
      }
    };

    const darkTheme: UITheme = {
      ...defaultTheme,
      id: 'dark',
      name: 'Ultra Think Dark',
      colorScheme: {
        ...defaultTheme.colorScheme,
        background: '#0f172a',
        surface: '#1e293b',
        text: {
          primary: '#f1f5f9',
          secondary: '#94a3b8',
          disabled: '#475569'
        }
      }
    };

    this.themes.set('default', defaultTheme);
    this.themes.set('dark', darkTheme);
  }

  private initializeIconMappings(): void {
    const mappings: IconMapping[] = [
      // 交通图标
      { activity: 'flight', category: 'transport', icon: '✈️', color: '#6b7280', size: 'medium', style: 'filled' },
      { activity: 'train', category: 'transport', icon: '🚄', color: '#6b7280', size: 'medium', style: 'filled' },
      { activity: 'bus', category: 'transport', icon: '🚌', color: '#6b7280', size: 'medium', style: 'filled' },
      { activity: 'taxi', category: 'transport', icon: '🚕', color: '#6b7280', size: 'medium', style: 'filled' },
      { activity: 'walk', category: 'transport', icon: '🚶', color: '#6b7280', size: 'medium', style: 'filled' },

      // 美食图标
      { activity: 'restaurant', category: 'food', icon: '🍽️', color: '#f59e0b', size: 'medium', style: 'filled' },
      { activity: 'cafe', category: 'food', icon: '☕', color: '#f59e0b', size: 'medium', style: 'filled' },
      { activity: 'street-food', category: 'food', icon: '🍜', color: '#f59e0b', size: 'medium', style: 'filled' },
      { activity: 'market', category: 'food', icon: '🏪', color: '#f59e0b', size: 'medium', style: 'filled' },

      // 文化图标
      { activity: 'museum', category: 'culture', icon: '🏛️', color: '#8b5cf6', size: 'medium', style: 'filled' },
      { activity: 'temple', category: 'culture', icon: '⛩️', color: '#8b5cf6', size: 'medium', style: 'filled' },
      { activity: 'art', category: 'culture', icon: '🎨', color: '#8b5cf6', size: 'medium', style: 'filled' },
      { activity: 'festival', category: 'culture', icon: '🎭', color: '#8b5cf6', size: 'medium', style: 'filled' },

      // 自然图标
      { activity: 'park', category: 'nature', icon: '🌳', color: '#10b981', size: 'medium', style: 'filled' },
      { activity: 'beach', category: 'nature', icon: '🏖️', color: '#10b981', size: 'medium', style: 'filled' },
      { activity: 'mountain', category: 'nature', icon: '⛰️', color: '#10b981', size: 'medium', style: 'filled' },
      { activity: 'garden', category: 'nature', icon: '🌸', color: '#10b981', size: 'medium', style: 'filled' },

      // 购物图标
      { activity: 'shopping', category: 'shopping', icon: '🛍️', color: '#ec4899', size: 'medium', style: 'filled' },
      { activity: 'mall', category: 'shopping', icon: '🏬', color: '#ec4899', size: 'medium', style: 'filled' },
      { activity: 'souvenir', category: 'shopping', icon: '🎁', color: '#ec4899', size: 'medium', style: 'filled' },

      // 娱乐图标
      { activity: 'show', category: 'entertainment', icon: '🎪', color: '#f97316', size: 'medium', style: 'filled' },
      { activity: 'nightlife', category: 'entertainment', icon: '🌃', color: '#f97316', size: 'medium', style: 'filled' },
      { activity: 'game', category: 'entertainment', icon: '🎮', color: '#f97316', size: 'medium', style: 'filled' }
    ];

    mappings.forEach(mapping => {
      const key = `${mapping.activity}_${mapping.category}`;
      this.iconMappings.set(key, mapping);
    });
  }

  private initializeComponentStyles(): void {
    const dayCardStyle: ComponentStyle = {
      component: 'day-card',
      variant: 'default',
      styles: {
        container: {
          backgroundColor: 'var(--surface)',
          borderRadius: 'var(--border-radius-lg)',
          padding: 'var(--spacing-lg)',
          boxShadow: 'var(--shadow-md)',
          marginBottom: 'var(--spacing-md)',
          transition: 'all 0.3s ease'
        },
        header: {
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: 'var(--spacing-md)'
        },
        title: {
          fontSize: 'var(--font-size-xl)',
          fontWeight: 'var(--font-weight-semibold)',
          color: 'var(--text-primary)'
        },
        timeline: {
          position: 'relative',
          paddingLeft: 'var(--spacing-lg)'
        },
        timelineItem: {
          position: 'relative',
          paddingBottom: 'var(--spacing-md)',
          borderLeft: '2px solid var(--color-secondary)'
        },
        timelineIcon: {
          position: 'absolute',
          left: '-8px',
          top: '4px',
          width: '16px',
          height: '16px',
          borderRadius: '50%',
          backgroundColor: 'var(--color-primary)'
        }
      },
      responsive: {
        mobile: {
          container: {
            padding: 'var(--spacing-md)',
            marginBottom: 'var(--spacing-sm)'
          },
          title: {
            fontSize: 'var(--font-size-lg)'
          }
        },
        tablet: {
          container: {
            padding: 'var(--spacing-lg)'
          }
        },
        desktop: {
          container: {
            padding: 'var(--spacing-xl)'
          }
        }
      }
    };

    const activityCardStyle: ComponentStyle = {
      component: 'activity-card',
      variant: 'default',
      styles: {
        container: {
          display: 'flex',
          alignItems: 'center',
          padding: 'var(--spacing-md)',
          backgroundColor: 'var(--surface)',
          borderRadius: 'var(--border-radius-md)',
          marginBottom: 'var(--spacing-sm)',
          cursor: 'pointer',
          transition: 'all 0.2s ease'
        },
        icon: {
          marginRight: 'var(--spacing-md)',
          fontSize: 'var(--font-size-xl)'
        },
        content: {
          flex: 1
        },
        title: {
          fontSize: 'var(--font-size-base)',
          fontWeight: 'var(--font-weight-medium)',
          color: 'var(--text-primary)',
          marginBottom: 'var(--spacing-xs)'
        },
        description: {
          fontSize: 'var(--font-size-sm)',
          color: 'var(--text-secondary)'
        },
        time: {
          fontSize: 'var(--font-size-sm)',
          color: 'var(--text-secondary)',
          fontWeight: 'var(--font-weight-medium)'
        }
      },
      responsive: {
        mobile: {
          container: {
            padding: 'var(--spacing-sm)'
          },
          icon: {
            marginRight: 'var(--spacing-sm)'
          }
        },
        tablet: {},
        desktop: {}
      }
    };

    this.componentStyles.set('day-card_default', dayCardStyle);
    this.componentStyles.set('activity-card_default', activityCardStyle);
  }

  private initializeColorPalette(): void {
    const colors = {
      // 主色调
      'blue-50': '#eff6ff',
      'blue-500': '#3b82f6',
      'blue-600': '#2563eb',
      'blue-700': '#1d4ed8',

      // 灰色调
      'gray-50': '#f9fafb',
      'gray-100': '#f3f4f6',
      'gray-200': '#e5e7eb',
      'gray-300': '#d1d5db',
      'gray-400': '#9ca3af',
      'gray-500': '#6b7280',
      'gray-600': '#4b5563',
      'gray-700': '#374151',
      'gray-800': '#1f2937',
      'gray-900': '#111827',

      // 语义色彩
      'green-500': '#10b981',
      'yellow-500': '#f59e0b',
      'red-500': '#ef4444',
      'purple-500': '#8b5cf6',
      'pink-500': '#ec4899',
      'orange-500': '#f97316'
    };

    Object.entries(colors).forEach(([name, value]) => {
      this.colorPalette.set(name, value);
    });
  }

  /**
   * 🔧 辅助方法
   */
  private getTheme(themeId: string): UITheme {
    return this.themes.get(themeId) || this.themes.get('default')!;
  }

  private getComponentStyle(component: string, variant: string): ComponentStyle {
    const key = `${component}_${variant}`;
    return this.componentStyles.get(key) || this.createDefaultComponentStyle(component, variant);
  }

  private createDefaultComponentStyle(component: string, variant: string): ComponentStyle {
    return {
      component,
      variant,
      styles: {
        container: {
          padding: 'var(--spacing-md)',
          backgroundColor: 'var(--surface)',
          borderRadius: 'var(--border-radius-md)'
        }
      },
      responsive: {
        mobile: {},
        tablet: {},
        desktop: {}
      }
    };
  }

  private findBestIcon(activity: any): IconMapping | null {
    // 尝试精确匹配
    const exactKey = `${activity.type || activity.activity}_${activity.category}`;
    let icon = this.iconMappings.get(exactKey);
    
    if (icon) return icon;

    // 尝试类别匹配
    const categoryKey = `${activity.category}_${activity.category}`;
    icon = this.iconMappings.get(categoryKey);
    
    if (icon) return icon;

    // 尝试活动类型匹配
    for (const [key, mapping] of this.iconMappings.entries()) {
      if (key.includes(activity.type || activity.activity || '')) {
        return mapping;
      }
    }

    return null;
  }

  private generateResponsiveStyles(componentStyle: ComponentStyle, theme: UITheme): Record<string, any> {
    return {
      mobile: `@media (max-width: 768px) { ${this.stylesToCSS(componentStyle.responsive.mobile)} }`,
      tablet: `@media (min-width: 769px) and (max-width: 1024px) { ${this.stylesToCSS(componentStyle.responsive.tablet)} }`,
      desktop: `@media (min-width: 1025px) { ${this.stylesToCSS(componentStyle.responsive.desktop)} }`
    };
  }

  private renderHTML(request: RenderRequest, theme: UITheme, icons: IconMapping[]): string {
    switch (request.component) {
      case 'day-card':
        return this.renderDayCard(request.data, theme, icons);
      case 'activity-card':
        return this.renderActivityCard(request.data, theme, icons);
      default:
        return `<div class="ultra-think-component">${JSON.stringify(request.data)}</div>`;
    }
  }

  private renderDayCard(data: any, theme: UITheme, icons: IconMapping[]): string {
    const activities = data.activities || [];
    
    return `
      <div class="day-card" data-day="${data.day}">
        <div class="day-card-header">
          <h3 class="day-card-title">第${data.day}天 - ${data.title || '精彩一天'}</h3>
          <span class="day-card-date">${data.date || ''}</span>
        </div>
        <div class="day-card-timeline">
          ${activities.map((activity: any, index: number) => {
            const icon = icons.find(i => i.activity === activity.type) || { icon: '📍', color: theme.colorScheme.primary };
            return `
              <div class="timeline-item" data-time="${activity.time}">
                <div class="timeline-icon" style="background-color: ${icon.color}">
                  ${icon.icon}
                </div>
                <div class="timeline-content">
                  <h4 class="activity-title">${activity.title}</h4>
                  <p class="activity-description">${activity.description || ''}</p>
                  <span class="activity-time">${activity.time} - ${activity.duration || '1小时'}</span>
                </div>
              </div>
            `;
          }).join('')}
        </div>
      </div>
    `;
  }

  private renderActivityCard(data: any, theme: UITheme, icons: IconMapping[]): string {
    const icon = icons.find(i => i.activity === data.type) || { icon: '📍', color: theme.colorScheme.primary };
    
    return `
      <div class="activity-card" data-type="${data.type}">
        <div class="activity-icon" style="color: ${icon.color}">
          ${icon.icon}
        </div>
        <div class="activity-content">
          <h4 class="activity-title">${data.title}</h4>
          <p class="activity-description">${data.description || ''}</p>
        </div>
        <div class="activity-time">
          ${data.time || ''}
        </div>
      </div>
    `;
  }

  private generateCSS(componentStyle: ComponentStyle, theme: UITheme, responsiveStyles: Record<string, any>, customStyles?: Record<string, any>): string {
    let css = this.generateThemeVariables(theme);
    css += this.stylesToCSS(componentStyle.styles);
    
    if (responsiveStyles) {
      css += Object.values(responsiveStyles).join('\n');
    }
    
    if (customStyles) {
      css += this.stylesToCSS(customStyles);
    }
    
    return css;
  }

  private generateThemeVariables(theme: UITheme): string {
    return `
      :root {
        --color-primary: ${theme.colorScheme.primary};
        --color-secondary: ${theme.colorScheme.secondary};
        --color-accent: ${theme.colorScheme.accent};
        --background: ${theme.colorScheme.background};
        --surface: ${theme.colorScheme.surface};
        --text-primary: ${theme.colorScheme.text.primary};
        --text-secondary: ${theme.colorScheme.text.secondary};
        --text-disabled: ${theme.colorScheme.text.disabled};
        
        --font-family: ${theme.typography.fontFamily};
        --font-size-xs: ${theme.typography.sizes.xs};
        --font-size-sm: ${theme.typography.sizes.sm};
        --font-size-base: ${theme.typography.sizes.base};
        --font-size-lg: ${theme.typography.sizes.lg};
        --font-size-xl: ${theme.typography.sizes.xl};
        --font-size-2xl: ${theme.typography.sizes['2xl']};
        --font-size-3xl: ${theme.typography.sizes['3xl']};
        
        --font-weight-normal: ${theme.typography.weights.normal};
        --font-weight-medium: ${theme.typography.weights.medium};
        --font-weight-semibold: ${theme.typography.weights.semibold};
        --font-weight-bold: ${theme.typography.weights.bold};
        
        --spacing-xs: ${theme.spacing.xs};
        --spacing-sm: ${theme.spacing.sm};
        --spacing-md: ${theme.spacing.md};
        --spacing-lg: ${theme.spacing.lg};
        --spacing-xl: ${theme.spacing.xl};
        --spacing-2xl: ${theme.spacing['2xl']};
        
        --border-radius-sm: ${theme.borderRadius.sm};
        --border-radius-md: ${theme.borderRadius.md};
        --border-radius-lg: ${theme.borderRadius.lg};
        --border-radius-xl: ${theme.borderRadius.xl};
        --border-radius-full: ${theme.borderRadius.full};
        
        --shadow-sm: ${theme.shadows.sm};
        --shadow-md: ${theme.shadows.md};
        --shadow-lg: ${theme.shadows.lg};
        --shadow-xl: ${theme.shadows.xl};
      }
    `;
  }

  private stylesToCSS(styles: Record<string, any>): string {
    let css = '';
    
    Object.entries(styles).forEach(([selector, rules]) => {
      css += `.${selector.replace(/([A-Z])/g, '-$1').toLowerCase()} {\n`;
      
      if (typeof rules === 'object') {
        Object.entries(rules).forEach(([property, value]) => {
          const cssProperty = property.replace(/([A-Z])/g, '-$1').toLowerCase();
          css += `  ${cssProperty}: ${value};\n`;
        });
      }
      
      css += '}\n\n';
    });
    
    return css;
  }

  private generateJavaScript(request: RenderRequest): string | undefined {
    if (request.component === 'day-card') {
      return `
        document.querySelectorAll('.day-card').forEach(card => {
          card.addEventListener('click', function() {
            this.classList.toggle('expanded');
          });
        });
      `;
    }
    
    return undefined;
  }

  private generateCacheKey(request: RenderRequest): string {
    return `ui_${request.component}_${request.variant || 'default'}_${request.theme || 'default'}`;
  }

  private isCacheValid(result: RenderResult): boolean {
    // UI组件缓存1小时
    return true;
  }

  private async cacheRenderResult(result: RenderResult, cacheKey: string): Promise<void> {
    await this.cacheManager.set(cacheKey, result, 'ui-components', 60 * 60 * 1000); // 1小时
  }

  private generateFallbackResult(request: RenderRequest, startTime: number): RenderResult {
    return {
      html: `<div class="fallback-component">组件渲染失败: ${request.component}</div>`,
      css: '.fallback-component { padding: 1rem; background: #fee; color: #c00; border-radius: 0.5rem; }',
      javascript: undefined,
      metadata: {
        component: request.component,
        theme: 'fallback',
        renderTime: Date.now() - startTime,
        cacheHit: false
      }
    };
  }

  /**
   * 📊 获取UI统计
   */
  getUIStats() {
    return {
      themes: this.themes.size,
      iconMappings: this.iconMappings.size,
      componentStyles: this.componentStyles.size,
      colorPalette: this.colorPalette.size,
      availableComponents: Array.from(this.componentStyles.keys()).map(key => key.split('_')[0])
    };
  }

  /**
   * 🎨 获取颜色建议
   */
  suggestColors(category: string): string[] {
    const theme = this.getTheme('default');
    const activityColors = theme.colorScheme.activity;
    
    if (activityColors[category as keyof typeof activityColors]) {
      return [activityColors[category as keyof typeof activityColors]];
    }
    
    return [theme.colorScheme.primary, theme.colorScheme.secondary, theme.colorScheme.accent];
  }

  /**
   * 🔍 搜索图标
   */
  searchIcons(query: string): IconMapping[] {
    const results: IconMapping[] = [];

    for (const [key, mapping] of this.iconMappings.entries()) {
      if (key.includes(query.toLowerCase()) ||
          mapping.activity.includes(query.toLowerCase()) ||
          mapping.category.includes(query.toLowerCase())) {
        results.push(mapping);
      }
    }

    return results;
  }

  /**
   * 🎨 创建自定义主题
   */
  createCustomTheme(baseTheme: string, customizations: Partial<ColorScheme>): UITheme {
    const base = this.getTheme(baseTheme);
    const customTheme: UITheme = {
      ...base,
      id: `custom_${Date.now()}`,
      name: 'Custom Theme',
      colorScheme: {
        ...base.colorScheme,
        ...customizations
      }
    };

    this.themes.set(customTheme.id, customTheme);
    return customTheme;
  }

  /**
   * 📱 生成响应式断点
   */
  generateResponsiveBreakpoints(): Record<string, string> {
    return {
      mobile: '(max-width: 768px)',
      tablet: '(min-width: 769px) and (max-width: 1024px)',
      desktop: '(min-width: 1025px)',
      'mobile-only': '(max-width: 768px)',
      'tablet-up': '(min-width: 769px)',
      'desktop-up': '(min-width: 1025px)'
    };
  }

  /**
   * ♿ 生成无障碍属性
   */
  generateAccessibilityAttributes(component: string, data: any): Record<string, string> {
    const attributes: Record<string, string> = {};

    switch (component) {
      case 'day-card':
        attributes['role'] = 'article';
        attributes['aria-label'] = `第${data.day}天行程`;
        attributes['tabindex'] = '0';
        break;
      case 'activity-card':
        attributes['role'] = 'button';
        attributes['aria-label'] = `活动: ${data.title}`;
        attributes['tabindex'] = '0';
        break;
    }

    return attributes;
  }
}
