{"expo": {"name": "Trekmate", "slug": "trekmate", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/LETSTRAVELLOGIN.png", "userInterfaceStyle": "automatic", "splash": {"image": "./assets/images/LETSTRAVELWELCOME.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "infoPlist": {"NSCameraUsageDescription": "此应用需要访问您的相机以扫描文本和翻译内容", "NSMicrophoneUsageDescription": "此应用需要访问您的麦克风以录制音频", "NSLocationWhenInUseUsageDescription": "此应用需要访问您的位置以提供个性化的旅行建议和目的地推荐", "NSLocationAlwaysAndWhenInUseUsageDescription": "此应用需要访问您的位置以提供个性化的旅行建议和目的地推荐"}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/LETSTRAVELLOGO.png", "backgroundColor": "#ffffff"}, "package": "com.ckong0826.trekmate", "permissions": ["CAMERA", "RECORD_AUDIO", "RECEIVE_BOOT_COMPLETED", "VIBRATE", "com.android.alarm.permission.SET_ALARM", "ACCESS_FINE_LOCATION", "ACCESS_COARSE_LOCATION", "ACCESS_BACKGROUND_LOCATION"]}, "web": {"favicon": "./assets/images/LETSTRAVELLOGO.png"}, "scheme": "trekmate", "newArchEnabled": true, "experiments": {"typedRoutes": true}, "plugins": ["expo-dev-client", "expo-av", "expo-file-system", "expo-system-ui", "expo-notifications"], "extra": {"eas": {"projectId": "9b03ac62-03f6-4a91-af81-750047aa5bf2"}}}}