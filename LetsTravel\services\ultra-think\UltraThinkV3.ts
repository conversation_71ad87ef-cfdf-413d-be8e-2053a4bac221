/**
 * 🚀 Ultra Think V3.0 - 主入口文件
 * 整合所有V3.0系统组件，提供统一的API接口
 * 替代旧的UltraThinkMasterSolverV2系统
 */

import { UltraThinkMasterBrain } from './UltraThinkMasterBrain';
import { ContentOptimizer } from './ContentOptimizer';
import { SurpriseGenerator } from './engines/SurpriseGenerator';
import { UltraThinkUIEngine } from './ui/UltraThinkUIEngine';
import { UltraThinkSystemIntegrator } from './UltraThinkSystemIntegrator';

// ===== V3.0 统一接口定义 =====

export interface UltraThinkV3Request {
  destination: string;
  duration: number;
  budget: number;
  currency: string;
  travelers: number;
  startDate: Date;
  endDate?: Date;
  preferences: {
    travelStyle: string[];
    accommodation: string[];
    transport: string[];
    interests: string[];
  };
  options?: {
    minActivitiesPerDay?: number;
    maxActivitiesPerDay?: number;
    includeTransport?: boolean;
    includeAccommodation?: boolean;
    includeFlights?: boolean;
    includeMeals?: boolean;
    enableSeasonalOptimization?: boolean;
    enableSurprises?: boolean;
    qualityLevel?: 'standard' | 'premium' | 'luxury';
  };
}

export interface UltraThinkV3Response {
  success: boolean;
  journeyData: any;
  activities: any[];
  summary: any;
  surprises?: any[];
  uiComponents?: any;
  optimization: {
    timeEfficiency: number;
    budgetOptimization: number;
    personalizedScore: number;
    surpriseIndex: number;
    qualityScore: number;
  };
  metadata: {
    version: string;
    generationTime: number;
    componentsUsed: string[];
    fallbackUsed: boolean;
  };
  fixes?: string[];
  warnings?: string[];
}

// ===== Ultra Think V3.0 主类 =====

export class UltraThinkV3 {
  private static instance: UltraThinkV3;
  private masterBrain = UltraThinkMasterBrain.getInstance();
  private contentOptimizer = ContentOptimizer.getInstance();
  private surpriseGenerator = SurpriseGenerator.getInstance();
  private uiEngine = UltraThinkUIEngine.getInstance();
  private systemIntegrator = UltraThinkSystemIntegrator.getInstance();
  private initialized = false;

  private constructor() {
    console.log('🚀 Ultra Think V3.0 初始化');
  }

  static getInstance(): UltraThinkV3 {
    if (!UltraThinkV3.instance) {
      UltraThinkV3.instance = new UltraThinkV3();
    }
    return UltraThinkV3.instance;
  }

  /**
   * 🎯 生成完整旅行行程 - V3.0主入口方法
   */
  async generateJourney(request: UltraThinkV3Request): Promise<UltraThinkV3Response> {
    const startTime = Date.now();
    console.log(`🚀 Ultra Think V3.0 开始生成行程: ${request.destination}`);

    try {
      // 1. 系统初始化检查
      if (!this.initialized) {
        await this.initializeSystem();
      }

      // 2. 智能决策
      const decision = await this.masterBrain.makeDecision({
        destination: request.destination,
        userProfile: {
          interests: request.preferences.interests,
          personality: this.inferPersonality(request.preferences),
          budget: request.budget,
          duration: request.duration
        },
        requirements: {
          quality: request.options?.qualityLevel === 'luxury' ? 0.95 : 
                  request.options?.qualityLevel === 'premium' ? 0.85 : 0.75,
          creativity: 0.8,
          uniqueness: 0.7
        }
      });

      if (!decision.success) {
        throw new Error('智能决策失败');
      }

      // 3. 内容优化生成
      const optimization = await this.contentOptimizer.optimizeContent({
        destination: request.destination,
        contentType: 'journey',
        requirements: {
          quality: decision.requirements?.quality || 0.8,
          creativity: 0.8,
          uniqueness: 0.7,
          realtime: true
        },
        context: {
          ...decision.context,
          duration: request.duration,
          budget: request.budget,
          travelers: request.travelers,
          preferences: request.preferences
        },
        constraints: {
          maxProcessingTime: 30000,
          budgetLimit: 0.02,
          fallbackEnabled: true
        }
      });

      // 4. 生成真实活动（优先）
      let realActivities: any[] = [];
      if (request.destination.toLowerCase().includes('东京') || request.destination.toLowerCase().includes('tokyo')) {
        try {
          const { tokyoActivityGenerator } = await import('./engines/TokyoActivityGenerator');
          const tokyoActivities = tokyoActivityGenerator.generateActivities({
            duration: request.duration,
            budget: request.budget,
            preferences: request.preferences,
            season: this.inferSeason(request.startDate)
          });
          realActivities = tokyoActivityGenerator.convertToStandardFormat(tokyoActivities);
          console.log(`✅ 生成了${realActivities.length}个真实东京活动`);
        } catch (error) {
          console.warn('⚠️ 东京活动生成失败，使用降级方案:', error);
        }
      }

      // 5. 惊喜体验生成（作为补充）
      let surprisePackage = null;
      if (request.options?.enableSurprises !== false && realActivities.length < request.duration * 2) {
        try {
          surprisePackage = await this.surpriseGenerator.generateSurprisePackage({
            destination: request.destination,
            userProfile: {
              personality: this.inferPersonality(request.preferences),
              interests: request.preferences.interests,
              surprisePreference: 0.7,
              adventureLevel: this.inferAdventureLevel(request.preferences),
              emotionalOpenness: 0.8
            },
            journeyContext: {
              duration: request.duration,
              budget: request.budget,
              companions: this.inferCompanions(request.travelers),
              purpose: 'vacation',
              previousExperiences: []
            },
            constraints: {
              timeFlexibility: 0.7,
              budgetFlexibility: 0.6,
              comfortZone: 0.8,
              riskTolerance: 0.6
            }
          });
        } catch (error) {
          console.warn('⚠️ 惊喜生成失败，继续使用基础行程:', error);
        }
      }

      // 5. UI组件生成
      let uiComponents = null;
      try {
        uiComponents = await this.generateUIComponents(optimization.content, surprisePackage);
      } catch (error) {
        console.warn('⚠️ UI组件生成失败:', error);
      }

      // 6. 构建响应
      const allActivities = [
        ...realActivities,
        ...this.extractActivities(optimization.content),
        ...(surprisePackage?.surprises?.map(s => this.convertSurpriseToActivity(s)) || [])
      ];

      const response: UltraThinkV3Response = {
        success: true,
        journeyData: this.buildJourneyData(optimization.content, surprisePackage, realActivities),
        activities: allActivities,
        summary: this.buildSummary(optimization.content, request, realActivities),
        surprises: surprisePackage?.surprises || [],
        uiComponents,
        optimization: {
          timeEfficiency: optimization.optimizationScore,
          budgetOptimization: this.calculateBudgetOptimization(optimization.content, request.budget),
          personalizedScore: decision.confidence || 0.8,
          surpriseIndex: surprisePackage?.overallImpact || 0,
          qualityScore: optimization.qualityMetrics?.overall || 0.8
        },
        metadata: {
          version: '3.0',
          generationTime: Date.now() - startTime,
          componentsUsed: this.getUsedComponents(optimization, surprisePackage),
          fallbackUsed: optimization.metadata.fallbackUsed
        },
        fixes: optimization.metadata.fixes || [],
        warnings: optimization.metadata.warnings || []
      };

      console.log(`✅ Ultra Think V3.0 行程生成完成: ${response.activities.length}个活动, 质量评分${response.optimization.qualityScore.toFixed(2)}`);
      return response;

    } catch (error) {
      console.error('❌ Ultra Think V3.0 行程生成失败:', error);
      return this.generateFallbackResponse(request, startTime, error);
    }
  }

  /**
   * 推断季节
   */
  private inferSeason(startDate?: string): string {
    if (!startDate) return 'winter';

    const date = new Date(startDate);
    const month = date.getMonth() + 1;

    if (month >= 3 && month <= 5) return 'spring';
    if (month >= 6 && month <= 8) return 'summer';
    if (month >= 9 && month <= 11) return 'autumn';
    return 'winter';
  }

  /**
   * 将惊喜转换为活动格式
   */
  private convertSurpriseToActivity(surprise: any): any {
    return {
      id: surprise.id || `surprise_${Date.now()}`,
      title: surprise.name || '惊喜体验',
      name: surprise.name || '惊喜体验',
      description: surprise.description || '基于惊喜模板的个性化体验',
      location: {
        name: '惊喜地点',
        address: '待发现'
      },
      timing: {
        duration: surprise.timing?.duration || 90,
        startTime: new Date(),
        endTime: new Date(Date.now() + (surprise.timing?.duration || 90) * 60000)
      },
      cost: 0,
      currency: 'MYR',
      type: 'surprise',
      category: 'surprise',
      tags: ['惊喜', '体验'],
      photos: [],
      notes: '暂无特别说明',
      isCompleted: false,
      createdAt: new Date(),
      updatedAt: new Date()
    };
  }

  /**
   * 🔧 系统初始化
   */
  private async initializeSystem(): Promise<void> {
    try {
      console.log('🔧 初始化Ultra Think V3.0系统');
      
      const integrationResult = await this.systemIntegrator.integrateSystem({
        environment: 'production',
        performance: {
          maxResponseTime: 30000,
          cacheStrategy: 'balanced',
          concurrencyLimit: 10,
          memoryLimit: 512
        },
        features: {
          realTimeData: true,
          creativeContent: true,
          surpriseGeneration: true,
          responsiveUI: true,
          analytics: true
        }
      });

      if (!integrationResult.success) {
        console.warn('⚠️ 系统集成部分失败，使用降级模式');
      }

      this.initialized = true;
      console.log('✅ Ultra Think V3.0系统初始化完成');
    } catch (error) {
      console.error('❌ 系统初始化失败:', error);
      this.initialized = false;
    }
  }

  /**
   * 🔧 辅助方法
   */
  private inferPersonality(preferences: any): string {
    const interests = preferences.interests || [];
    const travelStyle = preferences.travelStyle || [];
    
    if (interests.includes('culture') || interests.includes('history')) {
      return 'cultural-explorer';
    }
    if (interests.includes('adventure') || travelStyle.includes('adventure')) {
      return 'adventure-seeker';
    }
    if (interests.includes('food') || interests.includes('dining')) {
      return 'foodie';
    }
    if (interests.includes('nature') || interests.includes('outdoor')) {
      return 'nature-lover';
    }
    return 'balanced-traveler';
  }

  private inferAdventureLevel(preferences: any): 'conservative' | 'moderate' | 'adventurous' {
    const travelStyle = preferences.travelStyle || [];
    const interests = preferences.interests || [];
    
    if (travelStyle.includes('adventure') || interests.includes('extreme')) {
      return 'adventurous';
    }
    if (travelStyle.includes('cultural') || interests.includes('sightseeing')) {
      return 'moderate';
    }
    return 'conservative';
  }

  private inferCompanions(travelers: number): string {
    if (travelers === 1) return 'solo';
    if (travelers === 2) return 'couple';
    if (travelers <= 4) return 'family';
    return 'group';
  }

  private async generateUIComponents(content: any, surprisePackage: any): Promise<any> {
    if (!content.dayPlans || content.dayPlans.length === 0) return null;

    const dayCard = await this.uiEngine.renderComponent({
      component: 'day-card',
      data: {
        day: 1,
        title: content.dayPlans[0]?.title || '精彩一天',
        activities: content.dayPlans[0]?.activities?.slice(0, 3) || [],
        surprises: surprisePackage?.surprises?.slice(0, 2) || []
      },
      theme: 'default'
    });

    return {
      dayCard: dayCard.html,
      styles: dayCard.css,
      scripts: dayCard.javascript
    };
  }

  private buildJourneyData(content: any, surprisePackage: any, realActivities: any[] = []): any {
    return {
      title: content.title || '精彩旅程',
      description: content.description || '为您精心规划的旅行体验',
      dayPlans: content.dayPlans || [],
      surprises: surprisePackage?.surprises || [],
      metadata: {
        generatedBy: 'Ultra Think V3.0',
        version: '3.0',
        timestamp: new Date().toISOString()
      }
    };
  }

  private extractActivities(content: any): any[] {
    const activities: any[] = [];
    
    if (content.dayPlans) {
      content.dayPlans.forEach((day: any, dayIndex: number) => {
        if (day.activities) {
          day.activities.forEach((activity: any, actIndex: number) => {
            activities.push({
              id: `day${dayIndex + 1}_activity${actIndex + 1}`,
              day: dayIndex + 1,
              title: activity.title || '未知活动',
              description: activity.description || '',
              type: activity.type || 'sightseeing',
              duration: activity.duration || 120,
              cost: activity.cost || 0,
              location: activity.location || {},
              time: activity.time || '09:00'
            });
          });
        }
      });
    }

    return activities;
  }

  private buildSummary(content: any, request: UltraThinkV3Request, realActivities: any[] = []): any {
    const contentActivities = this.extractActivities(content);
    const allActivities = [...realActivities, ...contentActivities];
    const totalCost = allActivities.reduce((sum, act) => sum + (act.cost || 0), 0);

    return {
      destination: request.destination,
      duration: request.duration,
      totalActivities: allActivities.length,
      estimatedCost: totalCost,
      currency: request.currency,
      highlights: content.highlights || realActivities.slice(0, 3).map(a => a.title || a.name),
      recommendations: content.recommendations || [
        '建议提前预订热门景点门票',
        '携带现金，部分地方不接受信用卡',
        '下载翻译应用以便沟通'
      ]
    };
  }

  private calculateBudgetOptimization(content: any, budget: number): number {
    const activities = this.extractActivities(content);
    const totalCost = activities.reduce((sum, act) => sum + (act.cost || 0), 0);
    
    if (totalCost === 0) return 0.8; // 默认值
    
    const utilization = totalCost / budget;
    if (utilization > 1) return 0.3; // 超预算
    if (utilization > 0.9) return 1.0; // 充分利用
    if (utilization > 0.7) return 0.9; // 良好利用
    return 0.6; // 保守利用
  }

  private getUsedComponents(optimization: any, surprisePackage: any): string[] {
    const components = ['MasterBrain', 'ContentOptimizer'];
    
    if (surprisePackage) {
      components.push('SurpriseGenerator');
    }
    
    if (optimization.metadata.realTimeDataUsed) {
      components.push('RealTimeDataFusion');
    }
    
    if (optimization.metadata.creativeContentUsed) {
      components.push('CreativeAI');
    }
    
    return components;
  }

  private generateFallbackResponse(request: UltraThinkV3Request, startTime: number, error: any): UltraThinkV3Response {
    console.log('🔄 生成降级响应');
    
    return {
      success: false,
      journeyData: {
        title: `${request.destination}基础行程`,
        description: '系统生成的基础旅行计划',
        dayPlans: [],
        metadata: {
          generatedBy: 'Ultra Think V3.0 Fallback',
          version: '3.0',
          timestamp: new Date().toISOString()
        }
      },
      activities: [],
      summary: {
        destination: request.destination,
        duration: request.duration,
        totalActivities: 0,
        estimatedCost: 0,
        currency: request.currency,
        highlights: [],
        recommendations: ['请稍后重试以获得完整行程']
      },
      optimization: {
        timeEfficiency: 0.3,
        budgetOptimization: 0.3,
        personalizedScore: 0.3,
        surpriseIndex: 0,
        qualityScore: 0.3
      },
      metadata: {
        version: '3.0',
        generationTime: Date.now() - startTime,
        componentsUsed: ['Fallback'],
        fallbackUsed: true
      },
      fixes: [],
      warnings: ['系统生成失败，使用降级模式'],
      surprises: []
    };
  }

  /**
   * 📊 获取系统状态
   */
  getSystemStatus() {
    return {
      version: '3.0',
      initialized: this.initialized,
      components: {
        masterBrain: !!this.masterBrain,
        contentOptimizer: !!this.contentOptimizer,
        surpriseGenerator: !!this.surpriseGenerator,
        uiEngine: !!this.uiEngine,
        systemIntegrator: !!this.systemIntegrator
      }
    };
  }
}

// 导出单例实例
export const ultraThinkV3 = UltraThinkV3.getInstance();

// 兼容性导出（替代旧的UltraThinkMasterSolverV2）
export class UltraThinkMasterSolverV3 {
  static async generateJourney(request: any): Promise<UltraThinkV3Response> {
    console.log('🔄 兼容性调用: UltraThinkMasterSolverV3.generateJourney');
    return ultraThinkV3.generateJourney(request);
  }
}

export default UltraThinkV3;
