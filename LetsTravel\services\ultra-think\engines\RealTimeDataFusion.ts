/**
 * 🔄 Real Time Data Fusion - 实时数据融合引擎
 * Phase 3: 实时内容生成引擎 - 实时数据融合
 * 整合多个API数据源，提供实时数据融合服务
 */

import { UltraThinkAPIRouter } from '../UltraThinkAPIRouter';
import { UltraThinkCacheManager } from '../UltraThinkCacheManager';
import { UltraThinkDataPipeline } from '../UltraThinkDataPipeline';

// ===== 实时数据融合接口定义 =====

export interface DataSource {
  id: string;
  name: string;
  type: 'places' | 'weather' | 'events' | 'transport' | 'reviews' | 'prices';
  priority: number; // 1-10, 数据源优先级
  reliability: number; // 0-1, 可靠性评分
  freshness: number; // 数据新鲜度(分钟)
  endpoint: string;
  rateLimit: { requests: number; window: number };
  lastUpdate: Date;
  status: 'active' | 'degraded' | 'offline';
}

export interface FusionRequest {
  destination: string;
  dataTypes: string[];
  priority: 'low' | 'medium' | 'high' | 'realtime';
  maxAge: number; // 最大数据年龄(分钟)
  fallbackEnabled: boolean;
  qualityThreshold: number; // 0-1, 最低质量要求
  timeout: number;
}

export interface DataPoint {
  source: string;
  type: string;
  data: any;
  timestamp: Date;
  quality: number; // 0-1, 数据质量评分
  confidence: number; // 0-1, 置信度
  metadata: {
    apiResponse: boolean;
    cached: boolean;
    processed: boolean;
    version: string;
  };
}

export interface FusedData {
  destination: string;
  dataPoints: DataPoint[];
  fusionScore: number; // 0-1, 融合质量评分
  completeness: number; // 0-1, 数据完整性
  freshness: number; // 0-1, 数据新鲜度
  reliability: number; // 0-1, 数据可靠性
  processingTime: number;
  sources: string[];
  warnings: string[];
  metadata: {
    fusionDate: Date;
    algorithm: string;
    version: string;
    conflicts: number;
    resolutions: string[];
  };
}

// ===== Real Time Data Fusion 核心类 =====

export class RealTimeDataFusion {
  private static instance: RealTimeDataFusion;
  private apiRouter = UltraThinkAPIRouter.getInstance();
  private cacheManager = UltraThinkCacheManager.getInstance();
  private dataPipeline = UltraThinkDataPipeline.getInstance();
  private dataSources = new Map<string, DataSource>();
  private fusionHistory: FusedData[] = [];

  private constructor() {
    this.initializeDataSources();
    this.startDataSourceMonitoring();
    console.log('🔄 Real Time Data Fusion 初始化完成');
  }

  static getInstance(): RealTimeDataFusion {
    if (!RealTimeDataFusion.instance) {
      RealTimeDataFusion.instance = new RealTimeDataFusion();
    }
    return RealTimeDataFusion.instance;
  }

  /**
   * 🚀 实时数据融合 - 主要入口方法
   */
  async fuseRealTimeData(request: FusionRequest): Promise<FusedData> {
    const startTime = Date.now();
    console.log(`🔄 开始实时数据融合: ${request.destination} (${request.dataTypes.join(', ')})`);

    try {
      // 1. 验证请求参数
      this.validateFusionRequest(request);

      // 2. 选择最优数据源
      const selectedSources = this.selectOptimalDataSources(request);
      console.log(`📊 选择${selectedSources.length}个数据源`);

      // 3. 并行获取数据
      const rawDataPoints = await this.fetchDataInParallel(selectedSources, request);
      console.log(`📥 获取${rawDataPoints.length}个数据点`);

      // 4. 数据质量评估
      const qualityAssessment = this.assessDataQuality(rawDataPoints);

      // 5. 数据冲突检测和解决
      const resolvedData = this.resolveDataConflicts(rawDataPoints);

      // 6. 数据融合和优化
      const fusedData = this.performDataFusion(resolvedData, request);

      // 7. 质量验证
      const validatedData = this.validateFusedData(fusedData, request);

      // 8. 构建最终结果
      const result: FusedData = {
        destination: request.destination,
        dataPoints: validatedData,
        fusionScore: this.calculateFusionScore(validatedData),
        completeness: this.calculateCompleteness(validatedData, request),
        freshness: this.calculateFreshness(validatedData),
        reliability: this.calculateReliability(validatedData),
        processingTime: Date.now() - startTime,
        sources: selectedSources.map(s => s.name),
        warnings: this.collectWarnings(validatedData, qualityAssessment),
        metadata: {
          fusionDate: new Date(),
          algorithm: 'weighted-consensus-v2',
          version: '3.0',
          conflicts: qualityAssessment.conflicts,
          resolutions: qualityAssessment.resolutions
        }
      };

      // 9. 缓存结果
      await this.cacheFusedData(result);

      // 10. 记录融合历史
      this.recordFusionHistory(result);

      console.log(`✅ 数据融合完成: 评分${result.fusionScore.toFixed(2)}, 耗时${result.processingTime}ms`);
      return result;

    } catch (error) {
      console.error('❌ 实时数据融合失败:', error);
      return this.generateFallbackData(request, startTime);
    }
  }

  /**
   * 📊 选择最优数据源
   */
  private selectOptimalDataSources(request: FusionRequest): DataSource[] {
    const availableSources = Array.from(this.dataSources.values())
      .filter(source => this.isSourceSuitable(source, request))
      .filter(source => source.status !== 'offline');

    // 按优先级和可靠性排序
    availableSources.sort((a, b) => {
      const scoreA = a.priority * 0.6 + a.reliability * 0.4;
      const scoreB = b.priority * 0.6 + b.reliability * 0.4;
      return scoreB - scoreA;
    });

    // 根据请求优先级选择数据源数量
    const maxSources = this.getMaxSourcesForPriority(request.priority);
    return availableSources.slice(0, maxSources);
  }

  /**
   * ⚡ 并行获取数据
   */
  private async fetchDataInParallel(sources: DataSource[], request: FusionRequest): Promise<DataPoint[]> {
    const fetchPromises = sources.map(source => 
      this.fetchFromSource(source, request).catch(error => {
        console.warn(`⚠️ 数据源${source.name}获取失败:`, error);
        return null;
      })
    );

    const results = await Promise.allSettled(fetchPromises);
    const dataPoints: DataPoint[] = [];

    results.forEach((result, index) => {
      if (result.status === 'fulfilled' && result.value) {
        dataPoints.push(...result.value);
      } else {
        console.warn(`⚠️ 数据源${sources[index].name}返回空结果`);
      }
    });

    return dataPoints;
  }

  /**
   * 📡 从单个数据源获取数据
   */
  private async fetchFromSource(source: DataSource, request: FusionRequest): Promise<DataPoint[]> {
    const cacheKey = `realtime_${source.id}_${request.destination}`;
    
    // 检查缓存
    const cached = await this.cacheManager.get<DataPoint[]>(cacheKey, 'real-time-data');
    if (cached && this.isCacheValid(cached, request.maxAge)) {
      console.log(`✅ 数据源${source.name}缓存命中`);
      return cached.map(dp => ({ ...dp, metadata: { ...dp.metadata, cached: true } }));
    }

    // 调用API获取数据
    const apiResponse = await this.apiRouter.callAPI({
      type: source.type,
      params: this.buildAPIParams(source, request),
      priority: this.mapPriorityToAPI(request.priority),
      timeout: request.timeout
    });

    if (!apiResponse.success) {
      throw new Error(`API调用失败: ${apiResponse.metadata.warnings.join(', ')}`);
    }

    // 处理API响应
    const dataPoints = this.processAPIResponse(apiResponse, source);

    // 缓存数据
    await this.cacheManager.set(cacheKey, dataPoints, 'real-time-data', source.freshness * 60 * 1000);

    return dataPoints;
  }

  /**
   * 🔍 数据质量评估
   */
  private assessDataQuality(dataPoints: DataPoint[]): any {
    const assessment = {
      totalPoints: dataPoints.length,
      highQuality: 0,
      mediumQuality: 0,
      lowQuality: 0,
      conflicts: 0,
      resolutions: [] as string[]
    };

    dataPoints.forEach(dp => {
      if (dp.quality >= 0.8) assessment.highQuality++;
      else if (dp.quality >= 0.6) assessment.mediumQuality++;
      else assessment.lowQuality++;
    });

    // 检测数据冲突
    const conflicts = this.detectDataConflicts(dataPoints);
    assessment.conflicts = conflicts.length;
    assessment.resolutions = conflicts.map(c => c.resolution);

    return assessment;
  }

  /**
   * ⚖️ 解决数据冲突
   */
  private resolveDataConflicts(dataPoints: DataPoint[]): DataPoint[] {
    const groupedData = this.groupDataByType(dataPoints);
    const resolvedData: DataPoint[] = [];

    Object.entries(groupedData).forEach(([type, points]) => {
      if (points.length === 1) {
        resolvedData.push(points[0]);
      } else {
        // 多个数据点冲突，使用加权平均或选择最高质量的
        const resolved = this.resolveConflictForType(type, points);
        resolvedData.push(resolved);
      }
    });

    return resolvedData;
  }

  /**
   * 🔧 初始化数据源
   */
  private initializeDataSources(): void {
    const sources: DataSource[] = [
      {
        id: 'google_places',
        name: 'Google Places API',
        type: 'places',
        priority: 9,
        reliability: 0.95,
        freshness: 60, // 1小时
        endpoint: 'https://maps.googleapis.com/maps/api/place',
        rateLimit: { requests: 1000, window: 60000 },
        lastUpdate: new Date(),
        status: 'active'
      },
      {
        id: 'openweather',
        name: 'OpenWeatherMap API',
        type: 'weather',
        priority: 8,
        reliability: 0.90,
        freshness: 30, // 30分钟
        endpoint: 'https://api.openweathermap.org/data/2.5',
        rateLimit: { requests: 60, window: 60000 },
        lastUpdate: new Date(),
        status: 'active'
      },
      {
        id: 'nominatim',
        name: 'Nominatim Geocoding',
        type: 'places',
        priority: 7,
        reliability: 0.85,
        freshness: 120, // 2小时
        endpoint: 'https://nominatim.openstreetmap.org',
        rateLimit: { requests: 60, window: 60000 },
        lastUpdate: new Date(),
        status: 'active'
      },
      {
        id: 'osrm',
        name: 'OSRM Routing',
        type: 'transport',
        priority: 8,
        reliability: 0.88,
        freshness: 60, // 1小时
        endpoint: 'https://router.project-osrm.org',
        rateLimit: { requests: 100, window: 60000 },
        lastUpdate: new Date(),
        status: 'active'
      },
      {
        id: 'wikipedia',
        name: 'Wikipedia API',
        type: 'events',
        priority: 6,
        reliability: 0.80,
        freshness: 240, // 4小时
        endpoint: 'https://en.wikipedia.org/api/rest_v1',
        rateLimit: { requests: 200, window: 60000 },
        lastUpdate: new Date(),
        status: 'active'
      }
    ];

    sources.forEach(source => {
      this.dataSources.set(source.id, source);
    });

    console.log(`🔧 初始化${sources.length}个数据源`);
  }

  /**
   * 🔧 辅助方法
   */
  private validateFusionRequest(request: FusionRequest): void {
    if (!request.destination) throw new Error('目的地不能为空');
    if (!request.dataTypes || request.dataTypes.length === 0) throw new Error('数据类型不能为空');
    if (request.qualityThreshold < 0 || request.qualityThreshold > 1) throw new Error('质量阈值必须在0-1之间');
  }

  private isSourceSuitable(source: DataSource, request: FusionRequest): boolean {
    return request.dataTypes.includes(source.type) && source.reliability >= request.qualityThreshold;
  }

  private getMaxSourcesForPriority(priority: string): number {
    const limits = { low: 2, medium: 3, high: 4, realtime: 5 };
    return limits[priority as keyof typeof limits] || 3;
  }

  private isCacheValid(cached: any, maxAge: number): boolean {
    if (!cached || !cached[0]) return false;
    const age = (Date.now() - new Date(cached[0].timestamp).getTime()) / (1000 * 60);
    return age <= maxAge;
  }

  private buildAPIParams(source: DataSource, request: FusionRequest): Record<string, any> {
    const baseParams = { query: request.destination };
    
    switch (source.type) {
      case 'places':
        return { ...baseParams, type: 'establishment', radius: 5000 };
      case 'weather':
        return { ...baseParams, units: 'metric' };
      case 'transport':
        return { ...baseParams, profile: 'driving' };
      default:
        return baseParams;
    }
  }

  private mapPriorityToAPI(priority: string): 'low' | 'medium' | 'high' {
    const mapping = { low: 'low', medium: 'medium', high: 'high', realtime: 'high' };
    return mapping[priority as keyof typeof mapping] || 'medium';
  }

  private processAPIResponse(apiResponse: any, source: DataSource): DataPoint[] {
    const dataPoints: DataPoint[] = [];
    
    // 简化的API响应处理
    if (apiResponse.data) {
      dataPoints.push({
        source: source.name,
        type: source.type,
        data: apiResponse.data,
        timestamp: new Date(),
        quality: this.calculateDataQuality(apiResponse.data, source),
        confidence: source.reliability,
        metadata: {
          apiResponse: true,
          cached: false,
          processed: false,
          version: '1.0'
        }
      });
    }

    return dataPoints;
  }

  private calculateDataQuality(data: any, source: DataSource): number {
    let quality = source.reliability;
    
    // 基于数据完整性调整质量
    if (data && typeof data === 'object') {
      const fields = Object.keys(data).length;
      if (fields > 5) quality += 0.1;
      if (fields < 3) quality -= 0.1;
    }
    
    return Math.max(0, Math.min(1, quality));
  }

  private detectDataConflicts(dataPoints: DataPoint[]): any[] {
    // 简化的冲突检测
    return [];
  }

  private groupDataByType(dataPoints: DataPoint[]): Record<string, DataPoint[]> {
    const grouped: Record<string, DataPoint[]> = {};
    
    dataPoints.forEach(dp => {
      if (!grouped[dp.type]) grouped[dp.type] = [];
      grouped[dp.type].push(dp);
    });
    
    return grouped;
  }

  private resolveConflictForType(type: string, points: DataPoint[]): DataPoint {
    // 选择质量最高的数据点
    return points.reduce((best, current) => 
      current.quality > best.quality ? current : best
    );
  }

  private performDataFusion(dataPoints: DataPoint[], request: FusionRequest): DataPoint[] {
    // 数据融合处理
    return dataPoints.map(dp => ({
      ...dp,
      metadata: { ...dp.metadata, processed: true }
    }));
  }

  private validateFusedData(dataPoints: DataPoint[], request: FusionRequest): DataPoint[] {
    return dataPoints.filter(dp => dp.quality >= request.qualityThreshold);
  }

  private calculateFusionScore(dataPoints: DataPoint[]): number {
    if (dataPoints.length === 0) return 0;
    return dataPoints.reduce((sum, dp) => sum + dp.quality, 0) / dataPoints.length;
  }

  private calculateCompleteness(dataPoints: DataPoint[], request: FusionRequest): number {
    const requiredTypes = request.dataTypes.length;
    const availableTypes = new Set(dataPoints.map(dp => dp.type)).size;
    return availableTypes / requiredTypes;
  }

  private calculateFreshness(dataPoints: DataPoint[]): number {
    if (dataPoints.length === 0) return 0;
    
    const now = Date.now();
    const avgAge = dataPoints.reduce((sum, dp) => {
      const age = now - dp.timestamp.getTime();
      return sum + age;
    }, 0) / dataPoints.length;
    
    // 转换为0-1评分，1小时内为1.0
    return Math.max(0, 1 - (avgAge / (60 * 60 * 1000)));
  }

  private calculateReliability(dataPoints: DataPoint[]): number {
    if (dataPoints.length === 0) return 0;
    return dataPoints.reduce((sum, dp) => sum + dp.confidence, 0) / dataPoints.length;
  }

  private collectWarnings(dataPoints: DataPoint[], assessment: any): string[] {
    const warnings: string[] = [];
    
    if (assessment.lowQuality > assessment.highQuality) {
      warnings.push('低质量数据点较多，建议谨慎使用');
    }
    
    if (assessment.conflicts > 0) {
      warnings.push(`检测到${assessment.conflicts}个数据冲突`);
    }
    
    return warnings;
  }

  private async cacheFusedData(result: FusedData): Promise<void> {
    const cacheKey = `fused_${result.destination}_${result.metadata.fusionDate.getTime()}`;
    await this.cacheManager.set(cacheKey, result, 'real-time-data', 30 * 60 * 1000); // 30分钟
  }

  private recordFusionHistory(result: FusedData): void {
    this.fusionHistory.push(result);
    
    // 保持历史记录在合理范围内
    if (this.fusionHistory.length > 50) {
      this.fusionHistory = this.fusionHistory.slice(-25);
    }
  }

  private startDataSourceMonitoring(): void {
    // 每5分钟检查数据源状态
    setInterval(() => {
      this.monitorDataSources();
    }, 5 * 60 * 1000);
  }

  private async monitorDataSources(): Promise<void> {
    console.log('🔍 监控数据源状态');
    
    for (const source of this.dataSources.values()) {
      try {
        // 简单的健康检查
        const isHealthy = await this.checkSourceHealth(source);
        source.status = isHealthy ? 'active' : 'degraded';
        source.lastUpdate = new Date();
      } catch (error) {
        console.warn(`⚠️ 数据源${source.name}健康检查失败:`, error);
        source.status = 'offline';
      }
    }
  }

  private async checkSourceHealth(source: DataSource): Promise<boolean> {
    // 简化的健康检查
    return true;
  }

  private generateFallbackData(request: FusionRequest, startTime: number): FusedData {
    return {
      destination: request.destination,
      dataPoints: [],
      fusionScore: 0.3,
      completeness: 0.2,
      freshness: 0.1,
      reliability: 0.3,
      processingTime: Date.now() - startTime,
      sources: ['fallback'],
      warnings: ['数据融合失败，使用降级数据'],
      metadata: {
        fusionDate: new Date(),
        algorithm: 'fallback',
        version: '3.0',
        conflicts: 0,
        resolutions: []
      }
    };
  }

  /**
   * 📊 获取融合统计
   */
  getFusionStats() {
    const recentFusions = this.fusionHistory.slice(-10);
    
    return {
      totalFusions: this.fusionHistory.length,
      recentFusions: recentFusions.length,
      averageScore: recentFusions.length > 0 
        ? recentFusions.reduce((sum, f) => sum + f.fusionScore, 0) / recentFusions.length 
        : 0,
      averageTime: recentFusions.length > 0 
        ? recentFusions.reduce((sum, f) => sum + f.processingTime, 0) / recentFusions.length 
        : 0,
      dataSourceStatus: Array.from(this.dataSources.values()).map(s => ({
        name: s.name,
        status: s.status,
        reliability: s.reliability
      }))
    };
  }
}
