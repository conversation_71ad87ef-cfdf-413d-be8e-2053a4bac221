/**
 * 🔄 Ultra Think Data Pipeline - 智能数据管道
 * Phase 1: 核心架构重构 - 数据处理和缓存系统
 * 处理实时数据流和智能缓存策略
 */

import { ultraThinkConfig } from '../../config/UltraThinkConfig';

// ===== 数据管道接口定义 =====

export interface PipelineData {
  id: string;
  type: 'journey' | 'activity' | 'preference' | 'content';
  source: string;
  timestamp: Date;
  data: any;
  metadata: {
    version: string;
    quality: number;
    processed: boolean;
    cached: boolean;
  };
}

export interface ProcessingStage {
  name: string;
  processor: (data: PipelineData) => Promise<PipelineData>;
  enabled: boolean;
  priority: number;
  timeout: number;
}

export interface CacheStrategy {
  key: string;
  ttl: number; // Time to live in milliseconds
  storage: 'memory' | 'localStorage' | 'indexedDB';
  compression: boolean;
  encryption: boolean;
}

export interface PipelineResult {
  success: boolean;
  data: PipelineData;
  processingTime: number;
  stagesExecuted: string[];
  cacheHit: boolean;
  qualityScore: number;
  warnings: string[];
  errors: string[];
}

// ===== Ultra Think Data Pipeline 核心类 =====

export class UltraThinkDataPipeline {
  private static instance: UltraThinkDataPipeline;
  private config = ultraThinkConfig.getConfig();
  private processingStages = new Map<string, ProcessingStage>();
  private memoryCache = new Map<string, { data: any; timestamp: number; ttl: number }>();
  private processingQueue: PipelineData[] = [];
  private isProcessing = false;

  private constructor() {
    this.initializeProcessingStages();
    this.initializeCacheCleanup();
    console.log('🔄 Ultra Think Data Pipeline 初始化完成');
  }

  static getInstance(): UltraThinkDataPipeline {
    if (!UltraThinkDataPipeline.instance) {
      UltraThinkDataPipeline.instance = new UltraThinkDataPipeline();
    }
    return UltraThinkDataPipeline.instance;
  }

  /**
   * 🚀 处理数据流 - 主要入口方法
   */
  async processData(inputData: any, dataType: string, source: string): Promise<PipelineResult> {
    const startTime = Date.now();
    console.log(`🔄 数据管道处理开始: ${dataType} from ${source}`);

    try {
      // 1. 创建管道数据对象
      const pipelineData: PipelineData = {
        id: this.generateDataId(dataType, source),
        type: dataType as any,
        source,
        timestamp: new Date(),
        data: inputData,
        metadata: {
          version: '1.0',
          quality: 0.5,
          processed: false,
          cached: false
        }
      };

      // 2. 检查缓存
      const cacheResult = await this.checkCache(pipelineData);
      if (cacheResult.hit) {
        console.log('✅ 缓存命中，直接返回数据');
        return {
          success: true,
          data: cacheResult.data,
          processingTime: Date.now() - startTime,
          stagesExecuted: ['cache-hit'],
          cacheHit: true,
          qualityScore: cacheResult.data.metadata.quality,
          warnings: [],
          errors: []
        };
      }

      // 3. 执行处理流水线
      const processedData = await this.executeProcessingPipeline(pipelineData);

      // 4. 缓存处理结果
      await this.cacheData(processedData);

      // 5. 返回处理结果
      return {
        success: true,
        data: processedData,
        processingTime: Date.now() - startTime,
        stagesExecuted: this.getExecutedStages(processedData),
        cacheHit: false,
        qualityScore: processedData.metadata.quality,
        warnings: this.extractWarnings(processedData),
        errors: []
      };

    } catch (error) {
      console.error('❌ 数据管道处理失败:', error);
      return this.generateErrorResult(error, startTime);
    }
  }

  /**
   * 🔧 执行处理流水线
   */
  private async executeProcessingPipeline(data: PipelineData): Promise<PipelineData> {
    let currentData = { ...data };
    const stages = Array.from(this.processingStages.values())
      .filter(stage => stage.enabled)
      .sort((a, b) => b.priority - a.priority);

    console.log(`🔄 执行${stages.length}个处理阶段`);

    for (const stage of stages) {
      try {
        console.log(`⚡ 执行阶段: ${stage.name}`);
        const stageStartTime = Date.now();
        
        // 设置超时处理
        const processedData = await Promise.race([
          stage.processor(currentData),
          this.createTimeoutPromise(stage.timeout, stage.name)
        ]);

        const stageTime = Date.now() - stageStartTime;
        console.log(`✅ 阶段${stage.name}完成: ${stageTime}ms`);
        
        currentData = processedData;
        currentData.metadata.processed = true;

      } catch (error) {
        console.warn(`⚠️ 阶段${stage.name}失败:`, error);
        // 继续执行下一阶段，保持管道稳定性
      }
    }

    return currentData;
  }

  /**
   * 💾 缓存管理
   */
  private async checkCache(data: PipelineData): Promise<{ hit: boolean; data?: PipelineData }> {
    const cacheKey = this.generateCacheKey(data);
    
    // 检查内存缓存
    const memoryResult = this.memoryCache.get(cacheKey);
    if (memoryResult && Date.now() - memoryResult.timestamp < memoryResult.ttl) {
      return { hit: true, data: memoryResult.data };
    }

    // 检查本地存储缓存
    const localStorageResult = await this.checkLocalStorageCache(cacheKey);
    if (localStorageResult.hit) {
      // 将数据加载到内存缓存中
      this.memoryCache.set(cacheKey, {
        data: localStorageResult.data,
        timestamp: Date.now(),
        ttl: 30 * 60 * 1000 // 30分钟
      });
      return localStorageResult;
    }

    return { hit: false };
  }

  private async cacheData(data: PipelineData): Promise<void> {
    const cacheKey = this.generateCacheKey(data);
    const strategy = this.getCacheStrategy(data.type);

    // 内存缓存
    if (strategy.storage === 'memory' || strategy.storage === 'localStorage') {
      this.memoryCache.set(cacheKey, {
        data,
        timestamp: Date.now(),
        ttl: strategy.ttl
      });
    }

    // 本地存储缓存
    if (strategy.storage === 'localStorage') {
      try {
        const cacheData = {
          data,
          timestamp: Date.now(),
          ttl: strategy.ttl
        };
        
        const serializedData = strategy.compression 
          ? this.compressData(JSON.stringify(cacheData))
          : JSON.stringify(cacheData);
          
        localStorage.setItem(`ultra_think_cache_${cacheKey}`, serializedData);
        console.log(`💾 数据已缓存到localStorage: ${cacheKey}`);
      } catch (error) {
        console.warn('⚠️ localStorage缓存失败:', error);
      }
    }

    data.metadata.cached = true;
  }

  /**
   * 🔧 初始化处理阶段
   */
  private initializeProcessingStages(): void {
    const stages: ProcessingStage[] = [
      {
        name: 'data-validation',
        processor: this.validateDataStage.bind(this),
        enabled: true,
        priority: 100,
        timeout: 5000
      },
      {
        name: 'data-enrichment',
        processor: this.enrichDataStage.bind(this),
        enabled: true,
        priority: 80,
        timeout: 10000
      },
      {
        name: 'quality-enhancement',
        processor: this.enhanceQualityStage.bind(this),
        enabled: true,
        priority: 60,
        timeout: 15000
      },
      {
        name: 'format-optimization',
        processor: this.optimizeFormatStage.bind(this),
        enabled: true,
        priority: 40,
        timeout: 5000
      },
      {
        name: 'metadata-update',
        processor: this.updateMetadataStage.bind(this),
        enabled: true,
        priority: 20,
        timeout: 3000
      }
    ];

    stages.forEach(stage => {
      this.processingStages.set(stage.name, stage);
    });

    console.log(`🔧 初始化${stages.length}个处理阶段`);
  }

  /**
   * 🔧 处理阶段实现
   */
  private async validateDataStage(data: PipelineData): Promise<PipelineData> {
    // 数据验证逻辑
    const isValid = data.data && typeof data.data === 'object';
    
    if (!isValid) {
      throw new Error('数据验证失败: 无效的数据格式');
    }

    data.metadata.quality = Math.max(data.metadata.quality, 0.6);
    return data;
  }

  private async enrichDataStage(data: PipelineData): Promise<PipelineData> {
    // 数据丰富化逻辑
    if (data.type === 'journey') {
      data.data.enriched = {
        timestamp: new Date().toISOString(),
        source: 'ultra-think-pipeline',
        version: data.metadata.version
      };
    }

    data.metadata.quality = Math.max(data.metadata.quality, 0.7);
    return data;
  }

  private async enhanceQualityStage(data: PipelineData): Promise<PipelineData> {
    // 质量增强逻辑
    if (data.data && data.data.journey) {
      data.data.journey.qualityEnhanced = true;
      data.data.journey.enhancementTimestamp = new Date().toISOString();
    }

    data.metadata.quality = Math.max(data.metadata.quality, 0.8);
    return data;
  }

  private async optimizeFormatStage(data: PipelineData): Promise<PipelineData> {
    // 格式优化逻辑
    data.data.formatOptimized = true;
    data.metadata.quality = Math.max(data.metadata.quality, 0.85);
    return data;
  }

  private async updateMetadataStage(data: PipelineData): Promise<PipelineData> {
    // 元数据更新逻辑
    data.metadata.processed = true;
    data.metadata.quality = Math.max(data.metadata.quality, 0.9);
    data.metadata.version = '2.0';
    return data;
  }

  /**
   * 🔧 辅助方法
   */
  private generateDataId(type: string, source: string): string {
    return `${type}_${source}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateCacheKey(data: PipelineData): string {
    const keyData = {
      type: data.type,
      source: data.source,
      dataHash: this.hashData(data.data)
    };
    return btoa(JSON.stringify(keyData)).replace(/[^a-zA-Z0-9]/g, '');
  }

  private hashData(data: any): string {
    return btoa(JSON.stringify(data)).substr(0, 16);
  }

  private getCacheStrategy(dataType: string): CacheStrategy {
    const strategies = {
      journey: {
        key: 'journey',
        ttl: 60 * 60 * 1000, // 1小时
        storage: 'localStorage' as const,
        compression: true,
        encryption: false
      },
      activity: {
        key: 'activity',
        ttl: 30 * 60 * 1000, // 30分钟
        storage: 'memory' as const,
        compression: false,
        encryption: false
      },
      preference: {
        key: 'preference',
        ttl: 24 * 60 * 60 * 1000, // 24小时
        storage: 'localStorage' as const,
        compression: true,
        encryption: true
      },
      content: {
        key: 'content',
        ttl: 15 * 60 * 1000, // 15分钟
        storage: 'memory' as const,
        compression: false,
        encryption: false
      }
    };

    return strategies[dataType as keyof typeof strategies] || strategies.content;
  }

  private async checkLocalStorageCache(cacheKey: string): Promise<{ hit: boolean; data?: PipelineData }> {
    try {
      const cached = localStorage.getItem(`ultra_think_cache_${cacheKey}`);
      if (!cached) return { hit: false };

      const parsedCache = JSON.parse(cached);
      if (Date.now() - parsedCache.timestamp > parsedCache.ttl) {
        localStorage.removeItem(`ultra_think_cache_${cacheKey}`);
        return { hit: false };
      }

      return { hit: true, data: parsedCache.data };
    } catch (error) {
      console.warn('⚠️ localStorage缓存检查失败:', error);
      return { hit: false };
    }
  }

  private compressData(data: string): string {
    // 简单的压缩实现，实际项目中可以使用更高效的压缩算法
    return btoa(data);
  }

  private createTimeoutPromise(timeout: number, stageName: string): Promise<never> {
    return new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error(`阶段${stageName}超时: ${timeout}ms`));
      }, timeout);
    });
  }

  private initializeCacheCleanup(): void {
    // 每30分钟清理一次过期缓存
    setInterval(() => {
      this.cleanupExpiredCache();
    }, 30 * 60 * 1000);
  }

  private cleanupExpiredCache(): void {
    const now = Date.now();
    let cleanedCount = 0;

    // 清理内存缓存
    for (const [key, value] of this.memoryCache.entries()) {
      if (now - value.timestamp > value.ttl) {
        this.memoryCache.delete(key);
        cleanedCount++;
      }
    }

    console.log(`🧹 缓存清理完成: 清理了${cleanedCount}个过期项`);
  }

  private getExecutedStages(data: PipelineData): string[] {
    return Array.from(this.processingStages.keys()).filter(stage => 
      this.processingStages.get(stage)?.enabled
    );
  }

  private extractWarnings(data: PipelineData): string[] {
    const warnings: string[] = [];
    
    if (data.metadata.quality < 0.7) {
      warnings.push('数据质量较低，建议优化输入');
    }
    
    return warnings;
  }

  private generateErrorResult(error: any, startTime: number): PipelineResult {
    return {
      success: false,
      data: {} as PipelineData,
      processingTime: Date.now() - startTime,
      stagesExecuted: [],
      cacheHit: false,
      qualityScore: 0,
      warnings: [],
      errors: [error?.message || '未知错误']
    };
  }
}
