/**
 * 🧠 Personality AI - 个性化分析器
 * Phase 2: 智能偏好引擎构建 - 29维度个性化分析
 * 深度理解用户偏好和旅行风格
 */

import { UltraThinkLLMManager } from '../../ai/UltraThinkLLMManager';
import { UltraThinkCacheManager } from '../UltraThinkCacheManager';

// ===== 个性化分析接口定义 =====

export interface PersonalityDimension {
  name: string;
  category: 'travel-style' | 'activity-preference' | 'social-behavior' | 'comfort-level' | 'cultural-interest';
  weight: number; // 0-1, 维度重要性
  value: number; // 0-1, 用户在该维度的得分
  confidence: number; // 0-1, 分析置信度
  evidence: string[]; // 支持该分析的证据
}

export interface TravelPersonality {
  userId: string;
  profileVersion: string;
  dimensions: PersonalityDimension[];
  personalityType: string;
  travelStyle: string;
  preferences: {
    activities: string[];
    avoidances: string[];
    priorities: string[];
    budgetStyle: 'budget' | 'mid-range' | 'luxury' | 'mixed';
    socialStyle: 'solo' | 'couple' | 'group' | 'family';
    paceStyle: 'relaxed' | 'moderate' | 'intensive' | 'flexible';
  };
  recommendations: {
    idealDestinations: string[];
    recommendedActivities: string[];
    suggestedDuration: number;
    bestTravelTimes: string[];
    compatibleTravelStyles: string[];
  };
  metadata: {
    analysisDate: Date;
    dataPoints: number;
    confidence: number;
    lastUpdated: Date;
  };
}

export interface UserBehaviorData {
  searchHistory: string[];
  bookingHistory: any[];
  preferences: Record<string, any>;
  feedback: Array<{ rating: number; comment: string; category: string }>;
  demographics: {
    age?: number;
    location?: string;
    travelExperience?: 'beginner' | 'intermediate' | 'expert';
  };
}

// ===== Personality AI 核心类 =====

export class PersonalityAI {
  private static instance: PersonalityAI;
  private llmManager = UltraThinkLLMManager.getInstance();
  private cacheManager = UltraThinkCacheManager.getInstance();
  private personalityDimensions: PersonalityDimension[] = [];

  private constructor() {
    this.initializePersonalityDimensions();
    console.log('🧠 Personality AI 初始化完成');
  }

  static getInstance(): PersonalityAI {
    if (!PersonalityAI.instance) {
      PersonalityAI.instance = new PersonalityAI();
    }
    return PersonalityAI.instance;
  }

  /**
   * 🎯 个性化分析 - 主要入口方法
   */
  async analyzePersonality(userId: string, behaviorData: UserBehaviorData): Promise<TravelPersonality> {
    console.log(`🧠 开始个性化分析: ${userId}`);

    try {
      // 1. 检查缓存
      const cacheKey = `personality_${userId}`;
      const cached = await this.cacheManager.get<TravelPersonality>(cacheKey, 'user-preferences');
      
      if (cached && this.isCacheValid(cached)) {
        console.log('✅ 个性化分析缓存命中');
        return cached;
      }

      // 2. 分析29个维度
      const dimensions = await this.analyze29Dimensions(behaviorData);
      console.log(`📊 完成29维度分析: 平均置信度${this.calculateAverageConfidence(dimensions).toFixed(2)}`);

      // 3. 确定个性类型
      const personalityType = this.determinePersonalityType(dimensions);
      console.log(`🎭 个性类型: ${personalityType}`);

      // 4. 分析旅行风格
      const travelStyle = this.analyzeTravelStyle(dimensions);
      console.log(`✈️ 旅行风格: ${travelStyle}`);

      // 5. 生成偏好配置
      const preferences = this.generatePreferences(dimensions, behaviorData);

      // 6. 生成推荐
      const recommendations = await this.generateRecommendations(dimensions, preferences);

      // 7. 构建完整个性档案
      const personality: TravelPersonality = {
        userId,
        profileVersion: '2.0',
        dimensions,
        personalityType,
        travelStyle,
        preferences,
        recommendations,
        metadata: {
          analysisDate: new Date(),
          dataPoints: this.countDataPoints(behaviorData),
          confidence: this.calculateOverallConfidence(dimensions),
          lastUpdated: new Date()
        }
      };

      // 8. 缓存结果
      await this.cacheManager.set(cacheKey, personality, 'user-preferences', 7 * 24 * 60 * 60 * 1000); // 7天

      console.log(`✅ 个性化分析完成: ${personalityType} (置信度: ${personality.metadata.confidence.toFixed(2)})`);
      return personality;

    } catch (error) {
      console.error('❌ 个性化分析失败:', error);
      return this.generateFallbackPersonality(userId);
    }
  }

  /**
   * 📊 分析29个维度
   */
  private async analyze29Dimensions(behaviorData: UserBehaviorData): Promise<PersonalityDimension[]> {
    const dimensions: PersonalityDimension[] = [];

    // 分批分析维度以提高效率
    const dimensionBatches = this.groupDimensionsByCategory();

    for (const [category, categoryDimensions] of dimensionBatches) {
      console.log(`🔍 分析${category}类别维度`);
      
      const batchResults = await this.analyzeDimensionBatch(category, categoryDimensions, behaviorData);
      dimensions.push(...batchResults);
    }

    return dimensions;
  }

  /**
   * 🔍 分析维度批次
   */
  private async analyzeDimensionBatch(
    category: string, 
    dimensionNames: string[], 
    behaviorData: UserBehaviorData
  ): Promise<PersonalityDimension[]> {
    const results: PersonalityDimension[] = [];

    for (const dimensionName of dimensionNames) {
      try {
        const dimension = await this.analyzeSingleDimension(dimensionName, category, behaviorData);
        results.push(dimension);
      } catch (error) {
        console.warn(`⚠️ 维度${dimensionName}分析失败:`, error);
        // 使用默认值
        results.push(this.getDefaultDimension(dimensionName, category));
      }
    }

    return results;
  }

  /**
   * 🎯 分析单个维度
   */
  private async analyzeSingleDimension(
    dimensionName: string, 
    category: string, 
    behaviorData: UserBehaviorData
  ): Promise<PersonalityDimension> {
    // 基于行为数据分析维度
    const analysis = this.analyzeBasedOnBehavior(dimensionName, behaviorData);
    
    // 如果数据不足，使用LLM增强分析
    if (analysis.confidence < 0.6) {
      const enhancedAnalysis = await this.enhanceWithLLM(dimensionName, behaviorData, analysis);
      return enhancedAnalysis;
    }

    return analysis;
  }

  /**
   * 📈 基于行为数据分析
   */
  private analyzeBasedOnBehavior(dimensionName: string, behaviorData: UserBehaviorData): PersonalityDimension {
    const analysisRules = this.getDimensionAnalysisRules();
    const rule = analysisRules[dimensionName];
    
    if (!rule) {
      return this.getDefaultDimension(dimensionName, 'travel-style');
    }

    let value = 0.5; // 默认中性值
    let confidence = 0.3; // 低置信度
    const evidence: string[] = [];

    // 分析搜索历史
    if (behaviorData.searchHistory.length > 0) {
      const searchAnalysis = this.analyzeSearchHistory(behaviorData.searchHistory, rule);
      value = (value + searchAnalysis.value) / 2;
      confidence = Math.max(confidence, searchAnalysis.confidence);
      evidence.push(...searchAnalysis.evidence);
    }

    // 分析预订历史
    if (behaviorData.bookingHistory.length > 0) {
      const bookingAnalysis = this.analyzeBookingHistory(behaviorData.bookingHistory, rule);
      value = (value + bookingAnalysis.value) / 2;
      confidence = Math.max(confidence, bookingAnalysis.confidence);
      evidence.push(...bookingAnalysis.evidence);
    }

    // 分析反馈数据
    if (behaviorData.feedback.length > 0) {
      const feedbackAnalysis = this.analyzeFeedback(behaviorData.feedback, rule);
      value = (value + feedbackAnalysis.value) / 2;
      confidence = Math.max(confidence, feedbackAnalysis.confidence);
      evidence.push(...feedbackAnalysis.evidence);
    }

    return {
      name: dimensionName,
      category: rule.category,
      weight: rule.weight,
      value: Math.max(0, Math.min(1, value)),
      confidence: Math.max(0, Math.min(1, confidence)),
      evidence
    };
  }

  /**
   * 🤖 使用LLM增强分析
   */
  private async enhanceWithLLM(
    dimensionName: string, 
    behaviorData: UserBehaviorData, 
    baseAnalysis: PersonalityDimension
  ): Promise<PersonalityDimension> {
    try {
      const prompt = this.buildLLMPrompt(dimensionName, behaviorData, baseAnalysis);
      
      const llmResponse = await this.llmManager.callLLM({
        prompt,
        taskType: 'personality-analysis',
        context: {
          dimension: dimensionName,
          complexity: 'high'
        }
      });

      if (llmResponse.success) {
        const enhancedAnalysis = this.parseLLMResponse(llmResponse.content, baseAnalysis);
        return enhancedAnalysis;
      }
    } catch (error) {
      console.warn('⚠️ LLM增强分析失败:', error);
    }

    return baseAnalysis;
  }

  /**
   * 🔧 初始化29个个性维度
   */
  private initializePersonalityDimensions(): void {
    const dimensions = [
      // 旅行风格类 (8维度)
      { name: '冒险倾向', category: 'travel-style', weight: 0.9 },
      { name: '计划性', category: 'travel-style', weight: 0.8 },
      { name: '预算敏感度', category: 'travel-style', weight: 0.7 },
      { name: '舒适度要求', category: 'travel-style', weight: 0.8 },
      { name: '时间灵活性', category: 'travel-style', weight: 0.6 },
      { name: '探索深度', category: 'travel-style', weight: 0.7 },
      { name: '节奏偏好', category: 'travel-style', weight: 0.8 },
      { name: '安全意识', category: 'travel-style', weight: 0.7 },

      // 活动偏好类 (8维度)
      { name: '户外活动偏好', category: 'activity-preference', weight: 0.8 },
      { name: '文化体验偏好', category: 'activity-preference', weight: 0.9 },
      { name: '美食探索偏好', category: 'activity-preference', weight: 0.7 },
      { name: '购物兴趣', category: 'activity-preference', weight: 0.5 },
      { name: '夜生活偏好', category: 'activity-preference', weight: 0.6 },
      { name: '运动活动偏好', category: 'activity-preference', weight: 0.6 },
      { name: '艺术鉴赏偏好', category: 'activity-preference', weight: 0.7 },
      { name: '自然景观偏好', category: 'activity-preference', weight: 0.8 },

      // 社交行为类 (5维度)
      { name: '社交活跃度', category: 'social-behavior', weight: 0.7 },
      { name: '群体适应性', category: 'social-behavior', weight: 0.6 },
      { name: '当地人交流意愿', category: 'social-behavior', weight: 0.8 },
      { name: '分享倾向', category: 'social-behavior', weight: 0.5 },
      { name: '独处需求', category: 'social-behavior', weight: 0.6 },

      // 舒适度类 (4维度)
      { name: '住宿标准要求', category: 'comfort-level', weight: 0.8 },
      { name: '交通舒适度要求', category: 'comfort-level', weight: 0.7 },
      { name: '饮食适应性', category: 'comfort-level', weight: 0.6 },
      { name: '环境适应性', category: 'comfort-level', weight: 0.7 },

      // 文化兴趣类 (4维度)
      { name: '历史文化兴趣', category: 'cultural-interest', weight: 0.9 },
      { name: '现代文化兴趣', category: 'cultural-interest', weight: 0.7 },
      { name: '宗教文化开放度', category: 'cultural-interest', weight: 0.6 },
      { name: '语言学习兴趣', category: 'cultural-interest', weight: 0.5 }
    ];

    this.personalityDimensions = dimensions.map(d => ({
      name: d.name,
      category: d.category as any,
      weight: d.weight,
      value: 0.5,
      confidence: 0.5,
      evidence: []
    }));

    console.log(`🔧 初始化${dimensions.length}个个性维度`);
  }

  /**
   * 🔧 辅助方法
   */
  private groupDimensionsByCategory(): Map<string, string[]> {
    const groups = new Map<string, string[]>();
    
    this.personalityDimensions.forEach(dim => {
      if (!groups.has(dim.category)) {
        groups.set(dim.category, []);
      }
      groups.get(dim.category)!.push(dim.name);
    });

    return groups;
  }

  private getDimensionAnalysisRules(): Record<string, any> {
    return {
      '冒险倾向': {
        category: 'travel-style',
        weight: 0.9,
        keywords: ['冒险', '刺激', '极限', '探险', '挑战'],
        indicators: {
          high: ['极限运动', '未知目的地', '野外探险'],
          low: ['安全', '熟悉', '舒适', '常规']
        }
      },
      '计划性': {
        category: 'travel-style',
        weight: 0.8,
        keywords: ['计划', '安排', '预订', '行程', '时间表'],
        indicators: {
          high: ['详细行程', '提前预订', '时间安排'],
          low: ['随性', '临时', '灵活', '自由']
        }
      },
      '文化体验偏好': {
        category: 'activity-preference',
        weight: 0.9,
        keywords: ['文化', '历史', '传统', '博物馆', '古迹'],
        indicators: {
          high: ['博物馆', '历史遗迹', '传统文化', '当地习俗'],
          low: ['现代', '娱乐', '购物', '休闲']
        }
      }
      // 更多规则...
    };
  }

  private analyzeSearchHistory(searchHistory: string[], rule: any): { value: number; confidence: number; evidence: string[] } {
    let score = 0;
    let matches = 0;
    const evidence: string[] = [];

    searchHistory.forEach(search => {
      const searchLower = search.toLowerCase();
      
      // 检查高分指标
      rule.indicators?.high?.forEach((indicator: string) => {
        if (searchLower.includes(indicator.toLowerCase())) {
          score += 0.8;
          matches++;
          evidence.push(`搜索包含高分指标: ${indicator}`);
        }
      });

      // 检查低分指标
      rule.indicators?.low?.forEach((indicator: string) => {
        if (searchLower.includes(indicator.toLowerCase())) {
          score += 0.2;
          matches++;
          evidence.push(`搜索包含低分指标: ${indicator}`);
        }
      });
    });

    const value = matches > 0 ? score / matches : 0.5;
    const confidence = Math.min(0.8, matches / 10); // 最多10个匹配达到最高置信度

    return { value, confidence, evidence };
  }

  private analyzeBookingHistory(bookingHistory: any[], rule: any): { value: number; confidence: number; evidence: string[] } {
    // 简化的预订历史分析
    return {
      value: 0.6,
      confidence: 0.5,
      evidence: ['基于预订历史的分析']
    };
  }

  private analyzeFeedback(feedback: any[], rule: any): { value: number; confidence: number; evidence: string[] } {
    // 简化的反馈分析
    const avgRating = feedback.reduce((sum, f) => sum + f.rating, 0) / feedback.length;
    return {
      value: avgRating / 5, // 假设评分是1-5
      confidence: Math.min(0.9, feedback.length / 10),
      evidence: [`基于${feedback.length}条反馈的分析`]
    };
  }

  private determinePersonalityType(dimensions: PersonalityDimension[]): string {
    // 基于维度分析确定个性类型
    const adventureScore = this.getDimensionValue(dimensions, '冒险倾向');
    const planningScore = this.getDimensionValue(dimensions, '计划性');
    const cultureScore = this.getDimensionValue(dimensions, '文化体验偏好');

    if (adventureScore > 0.7 && planningScore < 0.4) return '自由探险家';
    if (cultureScore > 0.8 && planningScore > 0.7) return '文化学者';
    if (adventureScore < 0.3 && planningScore > 0.8) return '安全规划者';
    
    return '平衡旅行者';
  }

  private analyzeTravelStyle(dimensions: PersonalityDimension[]): string {
    const comfortScore = this.getDimensionValue(dimensions, '舒适度要求');
    const budgetScore = this.getDimensionValue(dimensions, '预算敏感度');

    if (comfortScore > 0.8 && budgetScore < 0.3) return '奢华体验';
    if (budgetScore > 0.8) return '经济实惠';
    if (comfortScore > 0.6) return '舒适中档';
    
    return '灵活适应';
  }

  private generatePreferences(dimensions: PersonalityDimension[], behaviorData: UserBehaviorData): any {
    return {
      activities: this.extractPreferredActivities(dimensions),
      avoidances: this.extractAvoidances(dimensions),
      priorities: this.extractPriorities(dimensions),
      budgetStyle: this.determineBudgetStyle(dimensions),
      socialStyle: this.determineSocialStyle(dimensions),
      paceStyle: this.determinePaceStyle(dimensions)
    };
  }

  private async generateRecommendations(dimensions: PersonalityDimension[], preferences: any): Promise<any> {
    return {
      idealDestinations: ['基于个性推荐的目的地'],
      recommendedActivities: preferences.activities,
      suggestedDuration: this.suggestDuration(dimensions),
      bestTravelTimes: ['春季', '秋季'],
      compatibleTravelStyles: [preferences.budgetStyle]
    };
  }

  // 更多辅助方法...
  private getDimensionValue(dimensions: PersonalityDimension[], name: string): number {
    const dimension = dimensions.find(d => d.name === name);
    return dimension?.value || 0.5;
  }

  private extractPreferredActivities(dimensions: PersonalityDimension[]): string[] {
    const activities: string[] = [];
    
    if (this.getDimensionValue(dimensions, '文化体验偏好') > 0.7) {
      activities.push('文化体验', '历史探索', '博物馆参观');
    }
    
    if (this.getDimensionValue(dimensions, '户外活动偏好') > 0.7) {
      activities.push('户外探险', '自然观光', '徒步旅行');
    }
    
    return activities;
  }

  private extractAvoidances(dimensions: PersonalityDimension[]): string[] {
    const avoidances: string[] = [];
    
    if (this.getDimensionValue(dimensions, '冒险倾向') < 0.3) {
      avoidances.push('极限运动', '高风险活动');
    }
    
    return avoidances;
  }

  private extractPriorities(dimensions: PersonalityDimension[]): string[] {
    return ['安全', '舒适', '文化体验'];
  }

  private determineBudgetStyle(dimensions: PersonalityDimension[]): 'budget' | 'mid-range' | 'luxury' | 'mixed' {
    const budgetSensitivity = this.getDimensionValue(dimensions, '预算敏感度');
    const comfortRequirement = this.getDimensionValue(dimensions, '舒适度要求');
    
    if (budgetSensitivity > 0.8) return 'budget';
    if (comfortRequirement > 0.8 && budgetSensitivity < 0.3) return 'luxury';
    if (comfortRequirement > 0.6) return 'mid-range';
    
    return 'mixed';
  }

  private determineSocialStyle(dimensions: PersonalityDimension[]): 'solo' | 'couple' | 'group' | 'family' {
    const socialActivity = this.getDimensionValue(dimensions, '社交活跃度');
    
    if (socialActivity > 0.8) return 'group';
    if (socialActivity < 0.3) return 'solo';
    
    return 'couple';
  }

  private determinePaceStyle(dimensions: PersonalityDimension[]): 'relaxed' | 'moderate' | 'intensive' | 'flexible' {
    const pacePreference = this.getDimensionValue(dimensions, '节奏偏好');
    
    if (pacePreference > 0.8) return 'intensive';
    if (pacePreference < 0.3) return 'relaxed';
    
    return 'moderate';
  }

  private suggestDuration(dimensions: PersonalityDimension[]): number {
    const planningScore = this.getDimensionValue(dimensions, '计划性');
    const explorationDepth = this.getDimensionValue(dimensions, '探索深度');
    
    if (explorationDepth > 0.8) return 7; // 深度探索需要更长时间
    if (planningScore > 0.8) return 5; // 计划性强的人偏好中等时长
    
    return 3; // 默认短途
  }

  private calculateAverageConfidence(dimensions: PersonalityDimension[]): number {
    const totalConfidence = dimensions.reduce((sum, d) => sum + d.confidence, 0);
    return totalConfidence / dimensions.length;
  }

  private calculateOverallConfidence(dimensions: PersonalityDimension[]): number {
    // 加权平均置信度
    const weightedSum = dimensions.reduce((sum, d) => sum + (d.confidence * d.weight), 0);
    const totalWeight = dimensions.reduce((sum, d) => sum + d.weight, 0);
    return weightedSum / totalWeight;
  }

  private countDataPoints(behaviorData: UserBehaviorData): number {
    return behaviorData.searchHistory.length + 
           behaviorData.bookingHistory.length + 
           behaviorData.feedback.length;
  }

  private isCacheValid(personality: TravelPersonality): boolean {
    const daysSinceUpdate = (Date.now() - personality.metadata.lastUpdated.getTime()) / (1000 * 60 * 60 * 24);
    return daysSinceUpdate < 7; // 7天内有效
  }

  private getDefaultDimension(name: string, category: string): PersonalityDimension {
    return {
      name,
      category: category as any,
      weight: 0.5,
      value: 0.5,
      confidence: 0.3,
      evidence: ['默认值']
    };
  }

  private buildLLMPrompt(dimensionName: string, behaviorData: UserBehaviorData, baseAnalysis: PersonalityDimension): string {
    return `分析用户在"${dimensionName}"维度的个性特征。
用户行为数据：
- 搜索历史：${behaviorData.searchHistory.slice(0, 5).join(', ')}
- 预订次数：${behaviorData.bookingHistory.length}
- 反馈评分：${behaviorData.feedback.length > 0 ? behaviorData.feedback.map(f => f.rating).join(', ') : '无'}

请提供0-1的评分和分析理由。`;
  }

  private parseLLMResponse(content: string, baseAnalysis: PersonalityDimension): PersonalityDimension {
    // 简化的LLM响应解析
    const enhanced = { ...baseAnalysis };
    enhanced.confidence = Math.min(1.0, enhanced.confidence + 0.2);
    enhanced.evidence.push('LLM增强分析');
    return enhanced;
  }

  private generateFallbackPersonality(userId: string): TravelPersonality {
    return {
      userId,
      profileVersion: '2.0',
      dimensions: this.personalityDimensions.map(d => ({ ...d })),
      personalityType: '平衡旅行者',
      travelStyle: '灵活适应',
      preferences: {
        activities: ['观光游览', '文化体验'],
        avoidances: [],
        priorities: ['安全', '舒适'],
        budgetStyle: 'mid-range',
        socialStyle: 'couple',
        paceStyle: 'moderate'
      },
      recommendations: {
        idealDestinations: ['热门目的地'],
        recommendedActivities: ['观光', '美食'],
        suggestedDuration: 5,
        bestTravelTimes: ['春季', '秋季'],
        compatibleTravelStyles: ['mid-range']
      },
      metadata: {
        analysisDate: new Date(),
        dataPoints: 0,
        confidence: 0.5,
        lastUpdated: new Date()
      }
    };
  }
}
