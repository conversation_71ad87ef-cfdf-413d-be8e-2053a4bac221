/**
 * 🌍 全球季节性活动生成器
 * 根据季节和节庆为全世界任何目的地生成高质量的旅行活动
 */

import { globalSeasonalDataService } from './GlobalSeasonalDataService';
import { safeDateHandler } from '../datetime/SafeDateHandler';

export interface SeasonalActivity {
  id: string;
  name: string;
  title: string;
  category: string;
  season: 'spring' | 'summer' | 'autumn' | 'winter';
  description: string;
  duration: number; // 分钟
  cost: number;
  currency: string;
  startTime?: string;
  endTime?: string;
  location: {
    name: string;
    address: string;
    coordinates?: { lat: number; lng: number };
  };
  highlights: string[];
  tips: string[];
  weatherDependent: boolean;
  crowdLevel: 'low' | 'medium' | 'high' | 'very_high';
  bestTimeOfDay: 'early_morning' | 'morning' | 'afternoon' | 'evening' | 'night';
  seasonalFeatures: string[];
  relatedFestivals?: string[];
}

export interface SeasonalGenerationRequest {
  destination: string;
  season: 'spring' | 'summer' | 'autumn' | 'winter';
  duration: number; // 天数
  startDate: Date;
  preferences: any;
  includeSpecialEvents: boolean;
}

export class SeasonalActivityGenerator {
  /**
   * 🌍 生成全球季节性活动
   */
  generateSeasonalActivities(request: SeasonalGenerationRequest): SeasonalActivity[] {
    console.log('🌍 开始生成全球季节性活动:', request);

    const activities: SeasonalActivity[] = [];

    try {
      // 获取目的地的季节特征
      const seasonalCharacteristics = globalSeasonalDataService.getSeasonalCharacteristics(
        request.destination,
        request.season
      );

      // 获取节庆活动
      const festivals = globalSeasonalDataService.getFestivalsForDestination(
        request.destination,
        request.startDate,
        new Date(request.startDate.getTime() + request.duration * 24 * 60 * 60 * 1000)
      );

      // 生成基础季节性活动
      activities.push(...this.generateBaseSeasonalActivities(request, seasonalCharacteristics));

      // 生成节庆特色活动
      if (request.includeSpecialEvents && festivals.length > 0) {
        activities.push(...this.generateFestivalActivities(festivals, request));
      }

      // 生成目的地特色活动
      activities.push(...this.generateDestinationSpecificActivities(request, seasonalCharacteristics));

      console.log(`✅ 全球季节性活动生成完成: ${activities.length}个活动`);

    } catch (error) {
      console.error('❌ 季节性活动生成失败:', error);
      // 降级到基础活动生成
      activities.push(...this.generateFallbackActivities(request));
    }

    return activities;
  }

  /**
   * 🔄 已移除东京特定方法，使用全球通用方法
   */

  /**
   * 🌞 创建东京夏季基础活动
   */
  private createTokyoSummerBaseActivities(request: SeasonalGenerationRequest): SeasonalActivity[] {
    return [
      {
        id: 'tokyo_summer_temple_visit',
        name: '浅草寺夏日清晨参拜',
        title: '在清凉的晨光中体验传统文化',
        category: 'cultural',
        season: 'summer',
        description: '在夏日清晨参拜东京最古老的寺庙，避开炎热和人群，享受宁静的文化体验',
        duration: 90,
        cost: 0,
        currency: 'MYR',
        startTime: '07:00',
        endTime: '08:30',
        location: {
          name: '浅草寺',
          address: '东京都台东区浅草2-3-1',
          coordinates: { lat: 35.7148, lng: 139.7967 }
        },
        highlights: [
          '1400年历史古寺',
          '清晨宁静氛围',
          '免费参观',
          '传统建筑欣赏'
        ],
        tips: [
          '早晨7点前到达最佳',
          '穿着舒适的步行鞋',
          '可以参与晨间祈祷',
          '拍照时保持安静'
        ],
        weatherDependent: false,
        crowdLevel: 'low',
        bestTimeOfDay: 'early_morning',
        seasonalFeatures: [
          '夏日清晨凉爽',
          '避开炎热时段',
          '晨光中的古建筑',
          '宁静的文化体验'
        ]
      },
      {
        id: 'tokyo_summer_forest_walk',
        name: '明治神宫森林避暑漫步',
        title: '在都市绿洲中享受清凉',
        category: 'nature',
        season: 'summer',
        description: '在明治神宫的茂密森林中漫步，享受天然空调般的清凉环境',
        duration: 120,
        cost: 0,
        currency: 'MYR',
        startTime: '08:00',
        endTime: '10:00',
        location: {
          name: '明治神宫',
          address: '东京都涩谷区代代木神园町1-1',
          coordinates: { lat: 35.6762, lng: 139.6993 }
        },
        highlights: [
          '100年历史森林',
          '天然避暑胜地',
          '免费参观',
          '都市中的自然'
        ],
        tips: [
          '穿着舒适的步行鞋',
          '携带饮用水',
          '注意森林小径',
          '尊重神社环境'
        ],
        weatherDependent: false,
        crowdLevel: 'low',
        bestTimeOfDay: 'morning',
        seasonalFeatures: [
          '森林天然空调',
          '夏日避暑胜地',
          '绿荫遮蔽',
          '清新空气'
        ]
      },
      {
        id: 'tokyo_summer_shopping',
        name: '银座空调购物体验',
        title: '在舒适环境中享受购物乐趣',
        category: 'shopping',
        season: 'summer',
        description: '在银座的高端商场中享受空调环境，体验日本的购物文化和夏季折扣',
        duration: 180,
        cost: 200,
        currency: 'MYR',
        startTime: '13:00',
        endTime: '16:00',
        location: {
          name: '银座商业区',
          address: '东京都中央区银座',
          coordinates: { lat: 35.6762, lng: 139.7649 }
        },
        highlights: [
          '高端购物体验',
          '空调舒适环境',
          '夏季特别折扣',
          '日本品牌集中'
        ],
        tips: [
          '携带护照享受免税',
          '关注夏季促销',
          '试用化妆品和护肤品',
          '体验日式服务'
        ],
        weatherDependent: false,
        crowdLevel: 'medium',
        bestTimeOfDay: 'afternoon',
        seasonalFeatures: [
          '夏季折扣季',
          '空调避暑',
          '夏季新品上市',
          '舒适购物环境'
        ]
      }
    ];
  }

  /**
   * 🍧 创建夏季特色体验
   */
  private createSummerSpecialExperiences(request: SeasonalGenerationRequest): SeasonalActivity[] {
    return [
      {
        id: 'tokyo_summer_food_tour',
        name: '夏季美食探索之旅',
        title: '品尝东京夏季特色美食',
        category: 'food',
        season: 'summer',
        description: '品尝刨冰、冷拉面、夏季和果子等东京夏季特色美食',
        duration: 150,
        cost: 120,
        currency: 'MYR',
        startTime: '14:00',
        endTime: '16:30',
        location: {
          name: '筑地外市场',
          address: '东京都中央区筑地',
          coordinates: { lat: 35.6654, lng: 139.7707 }
        },
        highlights: [
          '夏季限定美食',
          '传统刨冰体验',
          '冷拉面品尝',
          '和果子制作'
        ],
        tips: [
          '空腹前往体验更佳',
          '尝试不同口味',
          '了解制作工艺',
          '注意食品卫生'
        ],
        weatherDependent: false,
        crowdLevel: 'medium',
        bestTimeOfDay: 'afternoon',
        seasonalFeatures: [
          '夏季限定菜单',
          '清凉解暑美食',
          '传统制作工艺',
          '季节性食材'
        ]
      },
      {
        id: 'tokyo_summer_tower_view',
        name: '东京塔夏日观景',
        title: '在空调环境中俯瞰都市美景',
        category: 'attraction',
        season: 'summer',
        description: '登上东京塔，在舒适的空调环境中俯瞰夏日东京的都市景色',
        duration: 120,
        cost: 50,
        currency: 'MYR',
        startTime: '16:00',
        endTime: '18:00',
        location: {
          name: '东京塔',
          address: '东京都港区芝公园4-2-8',
          coordinates: { lat: 35.6586, lng: 139.7454 }
        },
        highlights: [
          '360度都市景观',
          '空调舒适环境',
          '夏日夕阳美景',
          '经典地标体验'
        ],
        tips: [
          '选择傍晚时分最佳',
          '携带相机拍照',
          '购买套票更优惠',
          '注意观景台开放时间'
        ],
        weatherDependent: false,
        crowdLevel: 'medium',
        bestTimeOfDay: 'afternoon',
        seasonalFeatures: [
          '夏日都市景观',
          '空调避暑',
          '夕阳西下美景',
          '清晰的远山视野'
        ]
      }
    ];
  }

  /**
   * 🌍 生成基础季节性活动
   */
  private generateBaseSeasonalActivities(
    request: SeasonalGenerationRequest,
    characteristics: any
  ): SeasonalActivity[] {
    const activities: SeasonalActivity[] = [];
    const { destination, season, duration } = request;

    // 根据季节生成通用活动模板
    const seasonalTemplates = this.getSeasonalActivityTemplates(season);

    seasonalTemplates.forEach((template, index) => {
      const activity = this.createActivityFromTemplate(template, destination, season, index);
      if (activity) {
        activities.push(activity);
      }
    });

    return activities;
  }

  /**
   * 🎪 生成节庆活动
   */
  private generateFestivalActivities(festivals: any[], request: SeasonalGenerationRequest): SeasonalActivity[] {
    const activities: SeasonalActivity[] = [];

    festivals.forEach((festival, index) => {
      // 为每个节庆生成相关活动
      const festivalActivities = this.createFestivalActivities(festival, request.destination, request.season);
      activities.push(...festivalActivities);
    });

    return activities;
  }

  /**
   * 🏛️ 生成目的地特色活动
   */
  private generateDestinationSpecificActivities(
    request: SeasonalGenerationRequest,
    characteristics: any
  ): SeasonalActivity[] {
    const activities: SeasonalActivity[] = [];
    const { destination, season } = request;

    // 根据目的地类型生成特色活动
    const destinationType = this.getDestinationType(destination);
    const specificTemplates = this.getDestinationSpecificTemplates(destinationType, season);

    specificTemplates.forEach((template, index) => {
      const activity = this.createActivityFromTemplate(template, destination, season, index + 100);
      if (activity) {
        activities.push(activity);
      }
    });

    return activities;
  }

  /**
   * 🔄 降级活动生成
   */
  private generateFallbackActivities(request: SeasonalGenerationRequest): SeasonalActivity[] {
    console.log('🔄 使用降级活动生成策略');

    return [
      {
        id: `fallback_cultural_${Date.now()}`,
        name: `${request.destination}文化探索`,
        title: `探索${request.destination}的文化魅力`,
        category: 'cultural',
        season: request.season,
        description: `深度体验${request.destination}的当地文化和传统`,
        duration: 120,
        cost: 50,
        currency: 'MYR',
        location: {
          name: `${request.destination}文化区`,
          address: request.destination,
        },
        highlights: ['文化体验', '当地传统', '历史了解'],
        tips: ['尊重当地文化', '适当着装', '保持开放心态'],
        weatherDependent: false,
        crowdLevel: 'medium',
        bestTimeOfDay: 'morning',
        seasonalFeatures: [`${request.season}季特色体验`]
      }
    ];
  }

  /**
   * 📋 获取季节性活动模板
   */
  private getSeasonalActivityTemplates(season: string): any[] {
    const templates: Record<string, any[]> = {
      spring: [
        {
          type: 'nature',
          name: '春季赏花',
          category: 'nature',
          description: '欣赏春季盛开的花卉',
          duration: 90,
          cost: 0,
          highlights: ['花卉观赏', '自然美景', '摄影机会'],
          bestTime: 'morning'
        },
        {
          type: 'cultural',
          name: '春季文化节',
          category: 'cultural',
          description: '参与当地春季文化庆典',
          duration: 120,
          cost: 30,
          highlights: ['传统文化', '节庆活动', '当地体验'],
          bestTime: 'afternoon'
        }
      ],
      summer: [
        {
          type: 'outdoor',
          name: '夏季户外活动',
          category: 'nature',
          description: '享受夏日阳光和户外乐趣',
          duration: 150,
          cost: 40,
          highlights: ['户外运动', '阳光沐浴', '活力体验'],
          bestTime: 'morning'
        },
        {
          type: 'festival',
          name: '夏季音乐节',
          category: 'entertainment',
          description: '参加当地夏季音乐庆典',
          duration: 180,
          cost: 80,
          highlights: ['音乐表演', '艺术体验', '夜生活'],
          bestTime: 'evening'
        },
        {
          type: 'food',
          name: '夏季美食探索',
          category: 'food',
          description: '品尝当地夏季特色美食',
          duration: 120,
          cost: 60,
          highlights: ['季节美食', '当地特色', '清爽口感'],
          bestTime: 'afternoon'
        }
      ],
      autumn: [
        {
          type: 'nature',
          name: '秋季赏枫',
          category: 'nature',
          description: '欣赏秋季红叶美景',
          duration: 120,
          cost: 0,
          highlights: ['红叶观赏', '秋季美景', '摄影天堂'],
          bestTime: 'afternoon'
        },
        {
          type: 'harvest',
          name: '秋收体验',
          category: 'cultural',
          description: '参与当地秋收庆典活动',
          duration: 150,
          cost: 50,
          highlights: ['收获体验', '农业文化', '传统庆典'],
          bestTime: 'morning'
        }
      ],
      winter: [
        {
          type: 'indoor',
          name: '冬季室内文化',
          category: 'cultural',
          description: '在温暖室内体验文化活动',
          duration: 120,
          cost: 40,
          highlights: ['室内活动', '文化体验', '温暖舒适'],
          bestTime: 'afternoon'
        },
        {
          type: 'winter_sports',
          name: '冬季运动',
          category: 'sports',
          description: '体验冬季特色运动项目',
          duration: 180,
          cost: 100,
          highlights: ['冬季运动', '刺激体验', '季节特色'],
          bestTime: 'morning'
        }
      ]
    };

    return templates[season] || templates.summer;
  }

  /**
   * 📊 根据偏好过滤活动
   */
  filterActivitiesByPreferences(activities: SeasonalActivity[], preferences: any): SeasonalActivity[] {
    if (!preferences || !preferences.travelStyle) {
      return activities;
    }

    const styles = Array.isArray(preferences.travelStyle) ? preferences.travelStyle : [preferences.travelStyle];
    
    return activities.filter(activity => {
      // 文化偏好
      if (styles.includes('cultural') && ['cultural', 'festival'].includes(activity.category)) {
        return true;
      }
      
      // 美食偏好
      if (styles.includes('food') && activity.category === 'food') {
        return true;
      }
      
      // 购物偏好
      if (styles.includes('shopping') && activity.category === 'shopping') {
        return true;
      }
      
      // 自然偏好
      if (styles.includes('nature') && activity.category === 'nature') {
        return true;
      }
      
      // 默认包含基础活动
      return ['attraction', 'transport', 'accommodation'].includes(activity.category);
    });
  }

  /**
   * 🏗️ 从模板创建活动
   */
  private createActivityFromTemplate(template: any, destination: string, season: string, index: number): SeasonalActivity | null {
    try {
      return {
        id: `seasonal_${season}_${index}_${Date.now()}`,
        name: `${destination}${template.name}`,
        title: template.description,
        category: template.category,
        season: season as any,
        description: `在${destination}${template.description}，体验${season}季的独特魅力`,
        duration: template.duration,
        cost: template.cost,
        currency: 'MYR',
        startTime: this.getTimeForBestTime(template.bestTime),
        endTime: this.calculateEndTime(this.getTimeForBestTime(template.bestTime), template.duration),
        location: {
          name: `${destination}${template.type}区域`,
          address: destination,
        },
        highlights: template.highlights,
        tips: [
          '提前了解当地文化',
          '准备适合的服装',
          '关注天气变化',
          '尊重当地习俗'
        ],
        weatherDependent: template.category === 'nature',
        crowdLevel: 'medium',
        bestTimeOfDay: template.bestTime,
        seasonalFeatures: [`${season}季特色`, '当地体验', '文化沉浸']
      };
    } catch (error) {
      console.warn('⚠️ 活动模板创建失败:', error);
      return null;
    }
  }

  /**
   * 🎪 创建节庆活动
   */
  private createFestivalActivities(festival: any, destination: string, season: string): SeasonalActivity[] {
    const activities: SeasonalActivity[] = [];

    // 主要节庆活动
    activities.push({
      id: `festival_${festival.id}_main`,
      name: `${destination}${festival.name}庆典`,
      title: `参与${festival.name}传统庆典`,
      category: 'festival',
      season: season as any,
      description: festival.description,
      duration: 180,
      cost: festival.costImpact === 'high' ? 100 : festival.costImpact === 'medium' ? 50 : 0,
      currency: 'MYR',
      startTime: '18:00',
      endTime: '21:00',
      location: {
        name: `${destination}节庆区域`,
        address: destination,
      },
      highlights: festival.activities.slice(0, 4),
      tips: [
        '了解节庆背景',
        '尊重当地传统',
        '准备合适服装',
        '注意人流密集'
      ],
      weatherDependent: false,
      crowdLevel: festival.crowdLevel,
      bestTimeOfDay: 'evening',
      seasonalFeatures: [`${festival.name}特色`, '传统文化', '节庆氛围'],
      relatedFestivals: [festival.name]
    });

    // 节庆准备活动
    if (festival.touristFriendly) {
      activities.push({
        id: `festival_${festival.id}_prep`,
        name: `${festival.name}文化体验`,
        title: `了解${festival.name}文化背景`,
        category: 'cultural',
        season: season as any,
        description: `深入了解${festival.name}的文化意义和传统习俗`,
        duration: 90,
        cost: 30,
        currency: 'MYR',
        startTime: '14:00',
        endTime: '15:30',
        location: {
          name: `${destination}文化中心`,
          address: destination,
        },
        highlights: ['文化学习', '传统了解', '背景知识'],
        tips: [
          '保持开放心态',
          '积极参与互动',
          '记录学习心得',
          '准备相关问题'
        ],
        weatherDependent: false,
        crowdLevel: 'low',
        bestTimeOfDay: 'afternoon',
        seasonalFeatures: ['文化教育', '传统学习', '深度体验'],
        relatedFestivals: [festival.name]
      });
    }

    return activities;
  }

  /**
   * 🏛️ 获取目的地类型
   */
  private getDestinationType(destination: string): string {
    // 简化的目的地类型判断
    const cityTypes: Record<string, string> = {
      '东京': 'modern_city',
      '京都': 'historic_city',
      '巴黎': 'cultural_city',
      '伦敦': 'historic_city',
      '纽约': 'modern_city',
      '罗马': 'historic_city',
      '悉尼': 'coastal_city',
      '曼谷': 'tropical_city'
    };

    return cityTypes[destination] || 'general_city';
  }

  /**
   * 📋 获取目的地特定模板
   */
  private getDestinationSpecificTemplates(destinationType: string, season: string): any[] {
    const templates: Record<string, any[]> = {
      modern_city: [
        {
          type: 'urban',
          name: '现代都市探索',
          category: 'attraction',
          description: '体验现代都市的魅力',
          duration: 120,
          cost: 40,
          highlights: ['现代建筑', '都市景观', '科技体验'],
          bestTime: 'afternoon'
        }
      ],
      historic_city: [
        {
          type: 'heritage',
          name: '历史文化遗产',
          category: 'cultural',
          description: '探索历史文化遗产',
          duration: 150,
          cost: 35,
          highlights: ['历史建筑', '文化遗产', '传统工艺'],
          bestTime: 'morning'
        }
      ],
      coastal_city: [
        {
          type: 'beach',
          name: '海滨休闲',
          category: 'nature',
          description: '享受海滨风光',
          duration: 180,
          cost: 20,
          highlights: ['海滨风光', '水上活动', '海鲜美食'],
          bestTime: 'afternoon'
        }
      ]
    };

    return templates[destinationType] || templates.modern_city;
  }

  /**
   * 🕐 获取最佳时间对应的具体时间
   */
  private getTimeForBestTime(bestTime: string): string {
    const timeMap: Record<string, string> = {
      'early_morning': '07:00',
      'morning': '09:00',
      'afternoon': '14:00',
      'evening': '18:00',
      'night': '20:00'
    };

    return timeMap[bestTime] || '09:00';
  }

  /**
   * 🕐 计算结束时间
   */
  private calculateEndTime(startTime: string, duration: number): string {
    try {
      const [hours, minutes] = startTime.split(':').map(Number);
      const startMinutes = hours * 60 + minutes;
      const endMinutes = startMinutes + duration;

      const endHours = Math.floor(endMinutes / 60) % 24;
      const endMins = endMinutes % 60;

      return `${endHours.toString().padStart(2, '0')}:${endMins.toString().padStart(2, '0')}`;
    } catch (error) {
      console.warn('⚠️ 结束时间计算失败:', error);
      return '18:00';
    }
  }

  /**
   * 🎯 获取全球季节性活动建议
   */
  getSeasonalRecommendations(destination: string, season: string): string[] {
    const characteristics = globalSeasonalDataService.getSeasonalCharacteristics(destination, season);

    const baseRecommendations = [
      '根据季节选择合适的服装',
      '关注当地天气变化',
      '了解季节性活动和节庆',
      '体验当季特色美食',
      '尊重当地文化和习俗'
    ];

    // 添加季节特定建议
    const seasonalTips = characteristics.advantages.map(advantage => `享受${advantage}`);
    const seasonalWarnings = characteristics.disadvantages.map(disadvantage => `注意${disadvantage}`);

    return [...baseRecommendations, ...seasonalTips, ...seasonalWarnings];
  }
}

// 导出单例实例
export const seasonalActivityGenerator = new SeasonalActivityGenerator();
