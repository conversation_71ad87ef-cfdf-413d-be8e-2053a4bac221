/**
 * 🧠 Ultra Think Model Strategy - LLM模型分配策略
 * Phase 1: 核心架构重构 - 智能模型选择和成本控制
 * 优化LLM模型选择和降级机制，实现成本控制
 */

import { ultraThinkConfig } from '../../config/UltraThinkConfig';
import { UltraThinkLLMManager } from '../ai/UltraThinkLLMManager';

// ===== 模型策略接口定义 =====

export interface ModelTier {
  tier: 'high' | 'medium' | 'low';
  name: string;
  description: string;
  model: string;
  costPerCall: number;
  maxTokens: number;
  timeout: number;
  suitableFor: string[];
  fallbackTier?: 'medium' | 'low';
}

export interface TaskComplexity {
  type: string;
  complexity: 'simple' | 'moderate' | 'complex';
  priority: 'low' | 'medium' | 'high';
  estimatedTokens: number;
  qualityRequirement: number;
}

export interface ModelSelection {
  selectedModel: string;
  tier: 'high' | 'medium' | 'low';
  estimatedCost: number;
  reasoning: string;
  fallbackOptions: string[];
  qualityExpectation: number;
}

export interface CostBudget {
  dailyLimit: number;
  currentUsage: number;
  remainingBudget: number;
  alertThreshold: number;
}

// ===== Ultra Think Model Strategy 核心类 =====

export class UltraThinkModelStrategy {
  private static instance: UltraThinkModelStrategy;
  private config = ultraThinkConfig.getConfig();
  private llmManager = UltraThinkLLMManager.getInstance();
  private modelTiers = new Map<string, ModelTier>();
  private costTracker: CostBudget;
  private usageHistory: Array<{ timestamp: Date; model: string; cost: number; task: string }> = [];

  private constructor() {
    this.initializeModelTiers();
    this.initializeCostTracker();
    console.log('🧠 Ultra Think Model Strategy 初始化完成');
  }

  static getInstance(): UltraThinkModelStrategy {
    if (!UltraThinkModelStrategy.instance) {
      UltraThinkModelStrategy.instance = new UltraThinkModelStrategy();
    }
    return UltraThinkModelStrategy.instance;
  }

  /**
   * 🎯 智能模型选择 - 主要入口方法
   */
  async selectOptimalModel(taskType: string, context?: any): Promise<ModelSelection> {
    console.log(`🧠 开始模型选择: ${taskType}`);

    try {
      // 1. 分析任务复杂度
      const complexity = this.analyzeTaskComplexity(taskType, context);
      console.log(`📊 任务复杂度分析: ${complexity.complexity} (优先级: ${complexity.priority})`);

      // 2. 检查成本预算
      const budgetCheck = this.checkCostBudget(complexity);
      if (!budgetCheck.allowed) {
        console.warn('⚠️ 成本预算不足，选择低成本模型');
        return this.selectBudgetModel(taskType, complexity);
      }

      // 3. 选择最优模型层级
      const selectedTier = this.selectModelTier(complexity);
      console.log(`🎯 选择模型层级: ${selectedTier.tier} - ${selectedTier.name}`);

      // 4. 验证模型可用性
      const availabilityCheck = await this.checkModelAvailability(selectedTier.model);
      if (!availabilityCheck.available) {
        console.warn(`⚠️ 模型${selectedTier.model}不可用，选择降级模型`);
        return this.selectFallbackModel(selectedTier, complexity);
      }

      // 5. 构建模型选择结果
      const selection: ModelSelection = {
        selectedModel: selectedTier.model,
        tier: selectedTier.tier,
        estimatedCost: selectedTier.costPerCall,
        reasoning: this.generateSelectionReasoning(selectedTier, complexity),
        fallbackOptions: this.getFallbackOptions(selectedTier),
        qualityExpectation: this.calculateQualityExpectation(selectedTier, complexity)
      };

      // 6. 记录选择决策
      this.recordModelSelection(selection, taskType);

      return selection;

    } catch (error) {
      console.error('❌ 模型选择失败:', error);
      return this.getEmergencyModel(taskType);
    }
  }

  /**
   * 📊 分析任务复杂度
   */
  private analyzeTaskComplexity(taskType: string, context?: any): TaskComplexity {
    const complexityRules = {
      // 高复杂度任务
      'creative-content': { complexity: 'complex' as const, priority: 'high' as const, tokens: 2000 },
      'personality-analysis': { complexity: 'complex' as const, priority: 'high' as const, tokens: 1500 },
      'cultural-analysis': { complexity: 'complex' as const, priority: 'high' as const, tokens: 1800 },
      'planner': { complexity: 'complex' as const, priority: 'high' as const, tokens: 2500 },
      
      // 中等复杂度任务
      'activity-generation': { complexity: 'moderate' as const, priority: 'medium' as const, tokens: 1200 },
      'attraction': { complexity: 'moderate' as const, priority: 'medium' as const, tokens: 1000 },
      'hotel': { complexity: 'moderate' as const, priority: 'medium' as const, tokens: 800 },
      'flight': { complexity: 'moderate' as const, priority: 'medium' as const, tokens: 900 },
      'budget-calculation': { complexity: 'moderate' as const, priority: 'medium' as const, tokens: 600 },
      
      // 低复杂度任务
      'data-formatting': { complexity: 'simple' as const, priority: 'low' as const, tokens: 400 },
      'transport': { complexity: 'simple' as const, priority: 'low' as const, tokens: 500 },
      'weather': { complexity: 'simple' as const, priority: 'low' as const, tokens: 300 },
      'simple-query': { complexity: 'simple' as const, priority: 'low' as const, tokens: 200 }
    };

    const rule = complexityRules[taskType as keyof typeof complexityRules] || complexityRules['activity-generation'];
    
    // 根据上下文调整复杂度
    let adjustedTokens = rule.tokens;
    let adjustedComplexity = rule.complexity;
    
    if (context?.duration && context.duration > 7) {
      adjustedTokens *= 1.5;
      adjustedComplexity = 'complex';
    }
    
    if (context?.preferences && Object.keys(context.preferences).length > 5) {
      adjustedTokens *= 1.2;
    }

    return {
      type: taskType,
      complexity: adjustedComplexity,
      priority: rule.priority,
      estimatedTokens: Math.round(adjustedTokens),
      qualityRequirement: this.calculateQualityRequirement(rule.complexity, rule.priority)
    };
  }

  /**
   * 🎯 选择模型层级
   */
  private selectModelTier(complexity: TaskComplexity): ModelTier {
    const tiers = Array.from(this.modelTiers.values());
    
    // 根据复杂度选择层级
    if (complexity.complexity === 'complex' || complexity.priority === 'high') {
      return tiers.find(t => t.tier === 'high') || tiers[0];
    }
    
    if (complexity.complexity === 'moderate' || complexity.priority === 'medium') {
      return tiers.find(t => t.tier === 'medium') || tiers[1];
    }
    
    return tiers.find(t => t.tier === 'low') || tiers[2];
  }

  /**
   * 💰 检查成本预算
   */
  private checkCostBudget(complexity: TaskComplexity): { allowed: boolean; reason?: string } {
    const estimatedCost = this.estimateTaskCost(complexity);
    
    if (this.costTracker.currentUsage + estimatedCost > this.costTracker.dailyLimit) {
      return { 
        allowed: false, 
        reason: `超出日预算限制: ${this.costTracker.currentUsage + estimatedCost} > ${this.costTracker.dailyLimit}` 
      };
    }
    
    if (this.costTracker.remainingBudget < estimatedCost) {
      return { 
        allowed: false, 
        reason: `剩余预算不足: ${this.costTracker.remainingBudget} < ${estimatedCost}` 
      };
    }
    
    return { allowed: true };
  }

  /**
   * 🔧 初始化模型层级
   */
  private initializeModelTiers(): void {
    const tiers: ModelTier[] = [
      {
        tier: 'high',
        name: 'Premium Intelligence',
        description: '高级智能模型，适用于复杂创意和分析任务',
        model: 'openai/gpt-4o-mini',
        costPerCall: 0.003,
        maxTokens: 4000,
        timeout: 30000,
        suitableFor: ['creative-content', 'personality-analysis', 'cultural-analysis', 'planner'],
        fallbackTier: 'medium'
      },
      {
        tier: 'medium',
        name: 'Balanced Performance',
        description: '平衡性能模型，适用于常规生成和推荐任务',
        model: 'google/gemini-2.0-flash-exp:free',
        costPerCall: 0.000,
        maxTokens: 3000,
        timeout: 20000,
        suitableFor: ['activity-generation', 'attraction', 'hotel', 'flight', 'budget-calculation'],
        fallbackTier: 'low'
      },
      {
        tier: 'low',
        name: 'Efficient Processing',
        description: '高效处理模型，适用于简单格式化和查询任务',
        model: 'google/gemini-flash-1.5-8b:free',
        costPerCall: 0.000,
        maxTokens: 2000,
        timeout: 15000,
        suitableFor: ['data-formatting', 'transport', 'weather', 'simple-query']
      }
    ];

    tiers.forEach(tier => {
      this.modelTiers.set(tier.tier, tier);
    });

    console.log(`🔧 初始化${tiers.length}个模型层级`);
  }

  /**
   * 💰 初始化成本追踪器
   */
  private initializeCostTracker(): void {
    this.costTracker = {
      dailyLimit: 1.00, // $1.00 per day
      currentUsage: 0,
      remainingBudget: 1.00,
      alertThreshold: 0.80 // 80%
    };

    // 每天重置预算
    setInterval(() => {
      this.resetDailyBudget();
    }, 24 * 60 * 60 * 1000);
  }

  /**
   * 🔧 辅助方法
   */
  private calculateQualityRequirement(complexity: string, priority: string): number {
    let base = 0.7;
    
    if (complexity === 'complex') base += 0.2;
    if (priority === 'high') base += 0.1;
    
    return Math.min(1.0, base);
  }

  private estimateTaskCost(complexity: TaskComplexity): number {
    const baseCost = complexity.complexity === 'complex' ? 0.003 : 
                    complexity.complexity === 'moderate' ? 0.001 : 0.000;
    
    const tokenMultiplier = complexity.estimatedTokens / 1000;
    return baseCost * tokenMultiplier;
  }

  private async checkModelAvailability(model: string): Promise<{ available: boolean; reason?: string }> {
    // 简化的可用性检查
    try {
      // 这里可以添加实际的模型健康检查
      return { available: true };
    } catch (error) {
      return { available: false, reason: error?.toString() };
    }
  }

  private selectBudgetModel(taskType: string, complexity: TaskComplexity): ModelSelection {
    const budgetTier = this.modelTiers.get('low')!;
    
    return {
      selectedModel: budgetTier.model,
      tier: 'low',
      estimatedCost: 0,
      reasoning: '预算限制，选择免费模型',
      fallbackOptions: [],
      qualityExpectation: 0.6
    };
  }

  private selectFallbackModel(originalTier: ModelTier, complexity: TaskComplexity): ModelSelection {
    const fallbackTierName = originalTier.fallbackTier || 'low';
    const fallbackTier = this.modelTiers.get(fallbackTierName)!;
    
    return {
      selectedModel: fallbackTier.model,
      tier: fallbackTierName,
      estimatedCost: fallbackTier.costPerCall,
      reasoning: `原模型不可用，降级到${fallbackTier.name}`,
      fallbackOptions: this.getFallbackOptions(fallbackTier),
      qualityExpectation: this.calculateQualityExpectation(fallbackTier, complexity)
    };
  }

  private generateSelectionReasoning(tier: ModelTier, complexity: TaskComplexity): string {
    return `基于任务复杂度${complexity.complexity}和优先级${complexity.priority}，选择${tier.name}模型以确保质量和成本平衡`;
  }

  private getFallbackOptions(tier: ModelTier): string[] {
    const allTiers = Array.from(this.modelTiers.values());
    return allTiers
      .filter(t => t.tier !== tier.tier)
      .map(t => t.model);
  }

  private calculateQualityExpectation(tier: ModelTier, complexity: TaskComplexity): number {
    const tierQuality = { high: 0.9, medium: 0.8, low: 0.7 };
    const complexityBonus = { complex: 0.1, moderate: 0.05, simple: 0 };
    
    return Math.min(1.0, 
      tierQuality[tier.tier] + complexityBonus[complexity.complexity]
    );
  }

  private recordModelSelection(selection: ModelSelection, taskType: string): void {
    this.usageHistory.push({
      timestamp: new Date(),
      model: selection.selectedModel,
      cost: selection.estimatedCost,
      task: taskType
    });

    // 更新成本追踪
    this.costTracker.currentUsage += selection.estimatedCost;
    this.costTracker.remainingBudget -= selection.estimatedCost;

    // 检查预算警告
    if (this.costTracker.currentUsage / this.costTracker.dailyLimit > this.costTracker.alertThreshold) {
      console.warn(`⚠️ 成本警告: 已使用${(this.costTracker.currentUsage / this.costTracker.dailyLimit * 100).toFixed(1)}%的日预算`);
    }
  }

  private getEmergencyModel(taskType: string): ModelSelection {
    const emergencyTier = this.modelTiers.get('low')!;
    
    return {
      selectedModel: emergencyTier.model,
      tier: 'low',
      estimatedCost: 0,
      reasoning: '紧急降级，使用免费模型',
      fallbackOptions: [],
      qualityExpectation: 0.5
    };
  }

  private resetDailyBudget(): void {
    this.costTracker.currentUsage = 0;
    this.costTracker.remainingBudget = this.costTracker.dailyLimit;
    console.log('🔄 日预算已重置');
  }

  /**
   * 📊 获取使用统计
   */
  getCostStats() {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const todayUsage = this.usageHistory.filter(h => h.timestamp >= today);
    const totalCost = todayUsage.reduce((sum, h) => sum + h.cost, 0);
    
    return {
      dailyUsage: totalCost,
      dailyLimit: this.costTracker.dailyLimit,
      remainingBudget: this.costTracker.remainingBudget,
      usagePercentage: (totalCost / this.costTracker.dailyLimit) * 100,
      callsToday: todayUsage.length,
      averageCostPerCall: todayUsage.length > 0 ? totalCost / todayUsage.length : 0
    };
  }
}
