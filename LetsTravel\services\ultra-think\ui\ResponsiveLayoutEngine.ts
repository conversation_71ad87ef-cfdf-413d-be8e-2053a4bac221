/**
 * 📱 Responsive Layout Engine - 响应式布局引擎
 * Phase 5: UI简化和体验优化 - 响应式设计和交互优化
 * 实现响应式设计和流畅的用户交互体验
 */

import { UltraThinkUIEngine } from './UltraThinkUIEngine';
import { UltraThinkCacheManager } from '../UltraThinkCacheManager';
import { environmentDetector } from '../utils/EnvironmentDetector';

// ===== 响应式布局接口定义 =====

export interface BreakpointConfig {
  name: string;
  minWidth: number;
  maxWidth?: number;
  columns: number;
  spacing: string;
  fontSize: string;
  containerPadding: string;
}

export interface LayoutConfig {
  breakpoints: BreakpointConfig[];
  gridSystem: {
    columns: number;
    gutterWidth: string;
    containerMaxWidth: string;
  };
  typography: {
    scaleRatio: number;
    baseSize: string;
    lineHeight: number;
  };
  spacing: {
    baseUnit: number;
    scale: number[];
  };
  animations: {
    duration: string;
    easing: string;
    enabled: boolean;
  };
}

export interface ResponsiveComponent {
  id: string;
  type: string;
  content: any;
  layout: {
    mobile: ComponentLayout;
    tablet: ComponentLayout;
    desktop: ComponentLayout;
  };
  interactions: InteractionConfig[];
  accessibility: AccessibilityConfig;
}

export interface ComponentLayout {
  display: 'block' | 'flex' | 'grid' | 'inline-block';
  width: string;
  height?: string;
  padding: string;
  margin: string;
  flexDirection?: 'row' | 'column';
  justifyContent?: string;
  alignItems?: string;
  gridTemplate?: string;
  order?: number;
}

export interface InteractionConfig {
  trigger: 'click' | 'hover' | 'focus' | 'scroll' | 'resize';
  action: 'expand' | 'collapse' | 'highlight' | 'navigate' | 'animate';
  target?: string;
  animation?: string;
  duration?: number;
}

export interface AccessibilityConfig {
  ariaLabel?: string;
  ariaRole?: string;
  tabIndex?: number;
  keyboardNavigation: boolean;
  screenReaderSupport: boolean;
  highContrast: boolean;
}

export interface ViewportInfo {
  width: number;
  height: number;
  devicePixelRatio: number;
  orientation: 'portrait' | 'landscape';
  breakpoint: string;
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
}

// ===== Responsive Layout Engine 核心类 =====

export class ResponsiveLayoutEngine {
  private static instance: ResponsiveLayoutEngine;
  private uiEngine = UltraThinkUIEngine.getInstance();
  private cacheManager = UltraThinkCacheManager.getInstance();
  private layoutConfig: LayoutConfig;
  private currentViewport?: ViewportInfo;
  private componentRegistry = new Map<string, ResponsiveComponent>();
  private interactionHandlers = new Map<string, Function>();
  private rnDimensionsSubscription?: any; // React Native Dimensions订阅
  private viewportInfo: ViewportInfo = {
    width: 375,
    height: 667,
    aspectRatio: 375 / 667,
    orientation: 'portrait',
    deviceType: 'mobile'
  };

  private constructor() {
    this.initializeLayoutConfig();
    this.initializeViewportDetection();
    this.initializeInteractionHandlers();
    console.log('📱 Responsive Layout Engine 初始化完成');
  }

  static getInstance(): ResponsiveLayoutEngine {
    if (!ResponsiveLayoutEngine.instance) {
      ResponsiveLayoutEngine.instance = new ResponsiveLayoutEngine();
    }
    return ResponsiveLayoutEngine.instance;
  }

  /**
   * 🎨 渲染响应式组件 - 主要入口方法
   */
  async renderResponsiveComponent(component: ResponsiveComponent): Promise<{ html: string; css: string; javascript: string }> {
    console.log(`📱 开始渲染响应式组件: ${component.type}`);

    try {
      // 1. 检测当前视口
      const viewport = this.getCurrentViewport();

      // 2. 选择适当的布局
      const layout = this.selectLayout(component, viewport);

      // 3. 生成响应式HTML
      const html = this.generateResponsiveHTML(component, layout, viewport);

      // 4. 生成响应式CSS
      const css = this.generateResponsiveCSS(component, viewport);

      // 5. 生成交互JavaScript
      const javascript = this.generateInteractiveJS(component, viewport);

      // 6. 注册组件
      this.componentRegistry.set(component.id, component);

      console.log(`✅ 响应式组件渲染完成: ${component.type}`);
      return { html, css, javascript };

    } catch (error) {
      console.error('❌ 响应式组件渲染失败:', error);
      return this.generateFallbackComponent(component);
    }
  }

  /**
   * 📐 生成响应式网格布局
   */
  generateResponsiveGrid(items: any[], config: { columns: { mobile: number; tablet: number; desktop: number } }): string {
    const gridClass = 'responsive-grid';
    
    return `
      <div class="${gridClass}">
        ${items.map((item, index) => `
          <div class="grid-item" data-index="${index}">
            ${typeof item === 'string' ? item : JSON.stringify(item)}
          </div>
        `).join('')}
      </div>
      
      <style>
        .${gridClass} {
          display: grid;
          gap: var(--spacing-md);
          width: 100%;
        }
        
        @media (max-width: 768px) {
          .${gridClass} {
            grid-template-columns: repeat(${config.columns.mobile}, 1fr);
          }
        }
        
        @media (min-width: 769px) and (max-width: 1024px) {
          .${gridClass} {
            grid-template-columns: repeat(${config.columns.tablet}, 1fr);
          }
        }
        
        @media (min-width: 1025px) {
          .${gridClass} {
            grid-template-columns: repeat(${config.columns.desktop}, 1fr);
          }
        }
        
        .grid-item {
          background: var(--surface);
          border-radius: var(--border-radius-md);
          padding: var(--spacing-md);
          transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        
        .grid-item:hover {
          transform: translateY(-2px);
          box-shadow: var(--shadow-lg);
        }
      </style>
    `;
  }

  /**
   * 📱 优化移动端体验
   */
  optimizeForMobile(component: ResponsiveComponent): ResponsiveComponent {
    const optimized = { ...component };
    
    // 移动端布局优化
    optimized.layout.mobile = {
      display: 'block',
      width: '100%',
      padding: 'var(--spacing-sm)',
      margin: '0 0 var(--spacing-sm) 0',
      order: 1
    };
    
    // 移动端交互优化
    optimized.interactions = optimized.interactions.map(interaction => ({
      ...interaction,
      // 移动端使用触摸友好的交互
      trigger: interaction.trigger === 'hover' ? 'click' : interaction.trigger
    }));
    
    // 移动端无障碍优化
    optimized.accessibility = {
      ...optimized.accessibility,
      keyboardNavigation: true,
      screenReaderSupport: true
    };
    
    return optimized;
  }

  /**
   * 🔧 初始化布局配置
   */
  private initializeLayoutConfig(): void {
    this.layoutConfig = {
      breakpoints: [
        {
          name: 'mobile',
          minWidth: 0,
          maxWidth: 768,
          columns: 1,
          spacing: 'var(--spacing-sm)',
          fontSize: 'var(--font-size-sm)',
          containerPadding: 'var(--spacing-md)'
        },
        {
          name: 'tablet',
          minWidth: 769,
          maxWidth: 1024,
          columns: 2,
          spacing: 'var(--spacing-md)',
          fontSize: 'var(--font-size-base)',
          containerPadding: 'var(--spacing-lg)'
        },
        {
          name: 'desktop',
          minWidth: 1025,
          columns: 3,
          spacing: 'var(--spacing-lg)',
          fontSize: 'var(--font-size-base)',
          containerPadding: 'var(--spacing-xl)'
        }
      ],
      gridSystem: {
        columns: 12,
        gutterWidth: 'var(--spacing-md)',
        containerMaxWidth: '1200px'
      },
      typography: {
        scaleRatio: 1.25,
        baseSize: '16px',
        lineHeight: 1.6
      },
      spacing: {
        baseUnit: 4,
        scale: [0.25, 0.5, 1, 1.5, 2, 3, 4, 6, 8]
      },
      animations: {
        duration: '0.3s',
        easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
        enabled: true
      }
    };
  }

  private initializeViewportDetection(): void {
    console.log('🔧 初始化视口检测 - 环境:', environmentDetector.getEnvironmentInfo().platform);

    if (environmentDetector.isWeb()) {
      // Web环境
      this.updateViewportInfo();

      // 使用环境检测器安全地添加事件监听器
      environmentDetector.safeAddEventListener('window', 'resize', () => {
        this.updateViewportInfo();
        this.handleViewportChange();
      });

      environmentDetector.safeAddEventListener('window', 'orientationchange', () => {
        setTimeout(() => {
          this.updateViewportInfo();
          this.handleViewportChange();
        }, 100);
      });
    } else if (environmentDetector.isReactNative()) {
      // React Native环境
      console.log('🔧 [RN] 在React Native环境中初始化视口检测');
      this.initializeReactNativeViewport();
    } else {
      // 其他环境，使用默认值
      console.log('🔧 [OTHER] 在其他环境中使用默认视口设置');
      this.setDefaultViewport();
    }
  }

  private initializeReactNativeViewport(): void {
    // 使用环境检测器获取屏幕尺寸
    const dimensions = environmentDetector.getScreenDimensions();

    if (dimensions) {
      this.viewportInfo = {
        width: dimensions.width,
        height: dimensions.height,
        aspectRatio: dimensions.width / dimensions.height,
        orientation: dimensions.width > dimensions.height ? 'landscape' : 'portrait',
        deviceType: dimensions.width > 768 ? 'tablet' : 'mobile'
      };

      // 使用环境检测器监听尺寸变化
      this.rnDimensionsSubscription = environmentDetector.addUniversalDimensionsListener((newDimensions) => {
        this.viewportInfo = {
          width: newDimensions.width,
          height: newDimensions.height,
          aspectRatio: newDimensions.width / newDimensions.height,
          orientation: newDimensions.width > newDimensions.height ? 'landscape' : 'portrait',
          deviceType: newDimensions.width > 768 ? 'tablet' : 'mobile'
        };
        this.handleViewportChange();
      });

      console.log('✅ [RN] React Native视口检测已初始化:', this.viewportInfo);
    } else {
      console.warn('⚠️ [RN] 无法获取屏幕尺寸，使用默认值');
      this.setDefaultViewport();
    }
  }

  private setDefaultViewport(): void {
    this.viewportInfo = {
      width: 375,
      height: 667,
      aspectRatio: 375 / 667,
      orientation: 'portrait',
      deviceType: 'mobile'
    };
    console.log('🔧 使用默认视口设置:', this.viewportInfo);
  }

  private initializeInteractionHandlers(): void {
    const handlers = {
      expand: (element: HTMLElement) => {
        element.classList.add('expanded');
        element.style.maxHeight = element.scrollHeight + 'px';
      },
      collapse: (element: HTMLElement) => {
        element.classList.remove('expanded');
        element.style.maxHeight = '0';
      },
      highlight: (element: HTMLElement) => {
        element.classList.add('highlighted');
        setTimeout(() => element.classList.remove('highlighted'), 2000);
      },
      animate: (element: HTMLElement, animation: string) => {
        element.style.animation = animation;
        element.addEventListener('animationend', () => {
          element.style.animation = '';
        }, { once: true });
      }
    };

    Object.entries(handlers).forEach(([action, handler]) => {
      this.interactionHandlers.set(action, handler);
    });
  }

  /**
   * 🔧 辅助方法
   */
  private getCurrentViewport(): ViewportInfo {
    if (!this.currentViewport && typeof window !== 'undefined') {
      this.updateViewportInfo();
    }
    
    return this.currentViewport || {
      width: 1024,
      height: 768,
      devicePixelRatio: 1,
      orientation: 'landscape',
      breakpoint: 'desktop',
      isMobile: false,
      isTablet: false,
      isDesktop: true
    };
  }

  private updateViewportInfo(): void {
    if (typeof window === 'undefined') return;
    
    const width = window.innerWidth;
    const height = window.innerHeight;
    const devicePixelRatio = window.devicePixelRatio || 1;
    const orientation = width > height ? 'landscape' : 'portrait';
    
    let breakpoint = 'desktop';
    let isMobile = false;
    let isTablet = false;
    let isDesktop = false;
    
    if (width <= 768) {
      breakpoint = 'mobile';
      isMobile = true;
    } else if (width <= 1024) {
      breakpoint = 'tablet';
      isTablet = true;
    } else {
      breakpoint = 'desktop';
      isDesktop = true;
    }
    
    this.currentViewport = {
      width,
      height,
      devicePixelRatio,
      orientation,
      breakpoint,
      isMobile,
      isTablet,
      isDesktop
    };
  }

  private selectLayout(component: ResponsiveComponent, viewport: ViewportInfo): ComponentLayout {
    if (viewport.isMobile) {
      return component.layout.mobile;
    } else if (viewport.isTablet) {
      return component.layout.tablet;
    } else {
      return component.layout.desktop;
    }
  }

  private generateResponsiveHTML(component: ResponsiveComponent, layout: ComponentLayout, viewport: ViewportInfo): string {
    const componentClass = `responsive-component responsive-component--${component.type} responsive-component--${viewport.breakpoint}`;
    const accessibilityAttrs = this.generateAccessibilityAttributes(component.accessibility);
    
    return `
      <div class="${componentClass}" 
           data-component-id="${component.id}" 
           data-breakpoint="${viewport.breakpoint}"
           ${accessibilityAttrs}>
        ${this.renderComponentContent(component, layout, viewport)}
      </div>
    `;
  }

  private renderComponentContent(component: ResponsiveComponent, layout: ComponentLayout, viewport: ViewportInfo): string {
    switch (component.type) {
      case 'day-card':
        return this.renderDayCardContent(component.content, layout, viewport);
      case 'activity-list':
        return this.renderActivityListContent(component.content, layout, viewport);
      case 'timeline':
        return this.renderTimelineContent(component.content, layout, viewport);
      default:
        return `<div class="component-content">${JSON.stringify(component.content)}</div>`;
    }
  }

  private renderDayCardContent(content: any, layout: ComponentLayout, viewport: ViewportInfo): string {
    const isCompact = viewport.isMobile;
    
    return `
      <div class="day-card-content ${isCompact ? 'day-card-content--compact' : ''}">
        <div class="day-card-header">
          <h3 class="day-card-title">${content.title || '未知标题'}</h3>
          ${!isCompact ? `<span class="day-card-date">${content.date || ''}</span>` : ''}
        </div>
        <div class="day-card-body">
          ${content.activities ? this.renderActivitiesList(content.activities, isCompact) : ''}
        </div>
        ${content.expandable ? '<button class="expand-btn" aria-label="展开详情">▼</button>' : ''}
      </div>
    `;
  }

  private renderActivityListContent(content: any, layout: ComponentLayout, viewport: ViewportInfo): string {
    const activities = content.activities || [];
    
    return `
      <div class="activity-list">
        ${activities.map((activity: any, index: number) => `
          <div class="activity-item" data-index="${index}">
            <div class="activity-time">${activity.time || ''}</div>
            <div class="activity-info">
              <h4 class="activity-title">${activity.title || '未知活动'}</h4>
              ${!viewport.isMobile ? `<p class="activity-description">${activity.description || ''}</p>` : ''}
            </div>
          </div>
        `).join('')}
      </div>
    `;
  }

  private renderTimelineContent(content: any, layout: ComponentLayout, viewport: ViewportInfo): string {
    const items = content.items || [];
    
    return `
      <div class="timeline-content">
        <div class="timeline-line"></div>
        ${items.map((item: any, index: number) => `
          <div class="timeline-item" data-index="${index}">
            <div class="timeline-marker"></div>
            <div class="timeline-content-item">
              <div class="timeline-time">${item.time || ''}</div>
              <h4 class="timeline-title">${item.title || '未知项目'}</h4>
              ${!viewport.isMobile && item.description ? `<p class="timeline-description">${item.description}</p>` : ''}
            </div>
          </div>
        `).join('')}
      </div>
    `;
  }

  private renderActivitiesList(activities: any[], isCompact: boolean): string {
    return activities.map(activity => `
      <div class="activity-summary ${isCompact ? 'activity-summary--compact' : ''}">
        <span class="activity-time">${activity.time || ''}</span>
        <span class="activity-name">${activity.title || '未知活动'}</span>
      </div>
    `).join('');
  }

  private generateResponsiveCSS(component: ResponsiveComponent, viewport: ViewportInfo): string {
    const breakpoints = this.layoutConfig.breakpoints;
    
    return `
      .responsive-component {
        width: 100%;
        transition: all ${this.layoutConfig.animations.duration} ${this.layoutConfig.animations.easing};
      }
      
      /* 移动端样式 */
      @media (max-width: ${breakpoints[0].maxWidth}px) {
        .responsive-component--mobile {
          padding: ${breakpoints[0].containerPadding};
          font-size: ${breakpoints[0].fontSize};
        }
        
        .day-card-content--compact .day-card-header {
          flex-direction: column;
          align-items: flex-start;
          gap: var(--spacing-xs);
        }
        
        .activity-summary--compact {
          display: flex;
          justify-content: space-between;
          padding: var(--spacing-xs) 0;
          border-bottom: 1px solid var(--color-secondary);
        }
        
        .timeline-item {
          padding-left: var(--spacing-lg);
        }
        
        .timeline-line {
          left: var(--spacing-sm);
        }
      }
      
      /* 平板端样式 */
      @media (min-width: ${breakpoints[1].minWidth}px) and (max-width: ${breakpoints[1].maxWidth}px) {
        .responsive-component--tablet {
          padding: ${breakpoints[1].containerPadding};
          font-size: ${breakpoints[1].fontSize};
        }
        
        .activity-list {
          display: grid;
          grid-template-columns: repeat(${breakpoints[1].columns}, 1fr);
          gap: ${breakpoints[1].spacing};
        }
      }
      
      /* 桌面端样式 */
      @media (min-width: ${breakpoints[2].minWidth}px) {
        .responsive-component--desktop {
          padding: ${breakpoints[2].containerPadding};
          font-size: ${breakpoints[2].fontSize};
          max-width: ${this.layoutConfig.gridSystem.containerMaxWidth};
          margin: 0 auto;
        }
        
        .activity-list {
          display: grid;
          grid-template-columns: repeat(${breakpoints[2].columns}, 1fr);
          gap: ${breakpoints[2].spacing};
        }
        
        .day-card-content {
          display: flex;
          flex-direction: column;
          height: 100%;
        }
      }
      
      /* 交互样式 */
      .responsive-component:hover {
        transform: translateY(-1px);
        box-shadow: var(--shadow-md);
      }
      
      .expand-btn {
        position: absolute;
        top: var(--spacing-md);
        right: var(--spacing-md);
        background: none;
        border: none;
        cursor: pointer;
        padding: var(--spacing-xs);
        border-radius: var(--border-radius-sm);
        transition: all 0.2s ease;
      }
      
      .expand-btn:hover {
        background: var(--color-secondary);
        transform: scale(1.1);
      }
      
      /* 动画效果 */
      ${this.layoutConfig.animations.enabled ? this.generateAnimationCSS() : ''}
      
      /* 无障碍样式 */
      .responsive-component:focus-within {
        outline: 2px solid var(--color-primary);
        outline-offset: 2px;
      }
      
      @media (prefers-reduced-motion: reduce) {
        .responsive-component,
        .expand-btn {
          transition: none;
          animation: none;
        }
      }
      
      /* 高对比度模式 */
      @media (prefers-contrast: high) {
        .responsive-component {
          border: 2px solid var(--text-primary);
        }
        
        .activity-summary--compact {
          border-bottom-width: 2px;
        }
      }
    `;
  }

  private generateAnimationCSS(): string {
    return `
      @keyframes slideInUp {
        from {
          opacity: 0;
          transform: translateY(20px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }
      
      @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
      }
      
      .responsive-component {
        animation: slideInUp ${this.layoutConfig.animations.duration} ${this.layoutConfig.animations.easing};
      }
      
      .activity-item,
      .timeline-item {
        animation: fadeIn ${this.layoutConfig.animations.duration} ${this.layoutConfig.animations.easing};
        animation-fill-mode: both;
      }
      
      .activity-item:nth-child(1) { animation-delay: 0.1s; }
      .activity-item:nth-child(2) { animation-delay: 0.2s; }
      .activity-item:nth-child(3) { animation-delay: 0.3s; }
    `;
  }

  private generateInteractiveJS(component: ResponsiveComponent, viewport: ViewportInfo): string {
    return `
      // 响应式组件交互功能
      (function() {
        const component = document.querySelector('[data-component-id="${component.id}"]');
        if (!component) return;
        
        // 展开/收起功能
        const expandBtn = component.querySelector('.expand-btn');
        if (expandBtn) {
          expandBtn.addEventListener('click', function() {
            const content = component.querySelector('.day-card-body');
            const isExpanded = component.classList.contains('expanded');
            
            component.classList.toggle('expanded');
            
            if (content) {
              content.style.maxHeight = isExpanded ? '0' : content.scrollHeight + 'px';
            }
            
            this.textContent = isExpanded ? '▼' : '▲';
            this.setAttribute('aria-label', isExpanded ? '展开详情' : '收起详情');
            
            // 触发自定义事件
            component.dispatchEvent(new CustomEvent('component:toggle', {
              detail: { componentId: '${component.id}', expanded: !isExpanded }
            }));
          });
        }
        
        // 活动项点击事件
        component.querySelectorAll('.activity-item, .timeline-item').forEach((item, index) => {
          item.addEventListener('click', function() {
            // 移除其他选中状态
            component.querySelectorAll('.activity-item--selected, .timeline-item--selected')
              .forEach(el => el.classList.remove('activity-item--selected', 'timeline-item--selected'));
            
            // 添加选中状态
            this.classList.add(this.classList.contains('activity-item') ? 'activity-item--selected' : 'timeline-item--selected');
            
            // 触发自定义事件
            component.dispatchEvent(new CustomEvent('item:select', {
              detail: { componentId: '${component.id}', itemIndex: index }
            }));
          });
        });
        
        // 键盘导航支持
        if (${component.accessibility.keyboardNavigation}) {
          component.addEventListener('keydown', function(e) {
            const focusableElements = this.querySelectorAll('button, [tabindex="0"]');
            const currentIndex = Array.from(focusableElements).indexOf(document.activeElement);
            
            switch(e.key) {
              case 'ArrowDown':
                e.preventDefault();
                const nextIndex = (currentIndex + 1) % focusableElements.length;
                focusableElements[nextIndex].focus();
                break;
              case 'ArrowUp':
                e.preventDefault();
                const prevIndex = (currentIndex - 1 + focusableElements.length) % focusableElements.length;
                focusableElements[prevIndex].focus();
                break;
              case 'Enter':
              case ' ':
                if (document.activeElement.tagName === 'BUTTON') {
                  e.preventDefault();
                  document.activeElement.click();
                }
                break;
            }
          });
        }
        
        // 视口变化处理 - 仅在Web环境中
        if (typeof window !== 'undefined' && window.addEventListener) {
          window.addEventListener('resize', function() {
            const newBreakpoint = window.innerWidth <= 768 ? 'mobile' :
                                 window.innerWidth <= 1024 ? 'tablet' : 'desktop';

            if (component.dataset.breakpoint !== newBreakpoint) {
              component.dataset.breakpoint = newBreakpoint;
              component.className = component.className.replace(/responsive-component--\\w+/, 'responsive-component--' + newBreakpoint);

              // 触发断点变化事件
              component.dispatchEvent(new CustomEvent('breakpoint:change', {
                detail: { componentId: '${component.id}', breakpoint: newBreakpoint }
              }));
            }
          });
        }
      })();
    `;
  }

  private generateAccessibilityAttributes(config: AccessibilityConfig): string {
    const attrs: string[] = [];
    
    if (config.ariaLabel) attrs.push(`aria-label="${config.ariaLabel}"`);
    if (config.ariaRole) attrs.push(`role="${config.ariaRole}"`);
    if (config.tabIndex !== undefined) attrs.push(`tabindex="${config.tabIndex}"`);
    
    return attrs.join(' ');
  }

  private handleViewportChange(): void {
    // 通知所有注册的组件视口已变化
    this.componentRegistry.forEach((component, id) => {
      const element = document.querySelector(`[data-component-id="${id}"]`);
      if (element) {
        element.dispatchEvent(new CustomEvent('viewport:change', {
          detail: { viewport: this.currentViewport }
        }));
      }
    });
  }

  private generateFallbackComponent(component: ResponsiveComponent): any {
    return {
      html: `<div class="component-fallback">组件渲染失败: ${component.type}</div>`,
      css: '.component-fallback { padding: 1rem; background: #fee; color: #c00; border-radius: 0.5rem; }',
      javascript: ''
    };
  }

  /**
   * 📊 获取响应式统计
   */
  getResponsiveStats() {
    return {
      registeredComponents: this.componentRegistry.size,
      currentViewport: this.currentViewport,
      breakpoints: this.layoutConfig.breakpoints.map(bp => bp.name),
      interactionHandlers: this.interactionHandlers.size,
      animationsEnabled: this.layoutConfig.animations.enabled
    };
  }

  /**
   * 🎯 优化性能
   */
  optimizePerformance() {
    // 延迟加载非关键组件
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('component-visible');
          observer.unobserve(entry.target);
        }
      });
    }, { threshold: 0.1 });

    document.querySelectorAll('.responsive-component').forEach(component => {
      observer.observe(component);
    });

    // 防抖处理resize事件 - 仅在Web环境中
    if (typeof window !== 'undefined' && window.addEventListener) {
      let resizeTimeout: NodeJS.Timeout;
      window.addEventListener('resize', () => {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(() => {
          this.updateViewportInfo();
          this.handleViewportChange();
        }, 150);
      });
    }
  }
}
