/**
 * 🌍 环境检测工具
 * 检测当前运行环境并提供相应的API适配
 */

export interface EnvironmentInfo {
  isReactNative: boolean;
  isWeb: boolean;
  isNode: boolean;
  platform: 'ios' | 'android' | 'web' | 'node' | 'unknown';
  hasWindow: boolean;
  hasDocument: boolean;
  hasDimensions: boolean;
}

export class EnvironmentDetector {
  private static instance: EnvironmentDetector;
  private environmentInfo: EnvironmentInfo;

  private constructor() {
    this.environmentInfo = this.detectEnvironment();
    console.log('🌍 环境检测完成:', this.environmentInfo);
  }

  static getInstance(): EnvironmentDetector {
    if (!EnvironmentDetector.instance) {
      EnvironmentDetector.instance = new EnvironmentDetector();
    }
    return EnvironmentDetector.instance;
  }

  private detectEnvironment(): EnvironmentInfo {
    const hasWindow = typeof window !== 'undefined';
    const hasDocument = typeof document !== 'undefined';
    const hasGlobal = typeof global !== 'undefined';
    const hasProcess = typeof process !== 'undefined';
    
    // 检测React Native
    const isReactNative = hasGlobal && !hasWindow && !hasDocument;
    
    // 检测Web环境
    const isWeb = hasWindow && hasDocument;
    
    // 检测Node.js环境
    const isNode = hasProcess && hasGlobal && !hasWindow;
    
    // 检测平台
    let platform: 'ios' | 'android' | 'web' | 'node' | 'unknown' = 'unknown';
    
    if (isReactNative) {
      try {
        const { Platform } = require('react-native');
        platform = Platform.OS as 'ios' | 'android';
      } catch (error) {
        console.warn('⚠️ 无法获取React Native平台信息:', error);
      }
    } else if (isWeb) {
      platform = 'web';
    } else if (isNode) {
      platform = 'node';
    }

    // 检测Dimensions API
    let hasDimensions = false;
    if (isReactNative) {
      try {
        require('react-native').Dimensions;
        hasDimensions = true;
      } catch (error) {
        console.warn('⚠️ React Native Dimensions API不可用:', error);
      }
    }

    return {
      isReactNative,
      isWeb,
      isNode,
      platform,
      hasWindow,
      hasDocument,
      hasDimensions
    };
  }

  /**
   * 获取环境信息
   */
  getEnvironmentInfo(): EnvironmentInfo {
    return this.environmentInfo;
  }

  /**
   * 检查是否为React Native环境
   */
  isReactNative(): boolean {
    return this.environmentInfo.isReactNative;
  }

  /**
   * 检查是否为Web环境
   */
  isWeb(): boolean {
    return this.environmentInfo.isWeb;
  }

  /**
   * 检查是否有window对象
   */
  hasWindow(): boolean {
    return this.environmentInfo.hasWindow;
  }

  /**
   * 安全地访问window对象
   */
  safeWindow(): Window | null {
    return this.environmentInfo.hasWindow ? window : null;
  }

  /**
   * 安全地访问document对象
   */
  safeDocument(): Document | null {
    return this.environmentInfo.hasDocument ? document : null;
  }

  /**
   * 获取屏幕尺寸信息
   */
  getScreenDimensions(): { width: number; height: number } | null {
    if (this.environmentInfo.isWeb && this.environmentInfo.hasWindow) {
      return {
        width: window.innerWidth,
        height: window.innerHeight
      };
    }

    if (this.environmentInfo.isReactNative && this.environmentInfo.hasDimensions) {
      try {
        const { Dimensions } = require('react-native');
        const { width, height } = Dimensions.get('window');
        return { width, height };
      } catch (error) {
        console.warn('⚠️ 获取React Native屏幕尺寸失败:', error);
      }
    }

    return null;
  }

  /**
   * 安全地添加事件监听器
   */
  safeAddEventListener(
    target: 'window' | 'document',
    event: string,
    handler: EventListener,
    options?: boolean | AddEventListenerOptions
  ): (() => void) | null {
    if (target === 'window' && this.environmentInfo.hasWindow) {
      window.addEventListener(event, handler, options);
      return () => window.removeEventListener(event, handler, options);
    }

    if (target === 'document' && this.environmentInfo.hasDocument) {
      document.addEventListener(event, handler, options);
      return () => document.removeEventListener(event, handler, options);
    }

    console.warn(`⚠️ 无法在当前环境中添加${target}事件监听器:`, event);
    return null;
  }

  /**
   * 为React Native环境添加尺寸变化监听器
   */
  addDimensionsChangeListener(handler: (dimensions: { width: number; height: number }) => void): (() => void) | null {
    if (this.environmentInfo.isReactNative && this.environmentInfo.hasDimensions) {
      try {
        const { Dimensions } = require('react-native');
        const subscription = Dimensions.addEventListener('change', ({ window }) => {
          handler({ width: window.width, height: window.height });
        });

        return () => {
          if (subscription && typeof subscription.remove === 'function') {
            subscription.remove();
          }
        };
      } catch (error) {
        console.warn('⚠️ 添加React Native尺寸监听器失败:', error);
      }
    }

    return null;
  }

  /**
   * 通用的尺寸变化监听器
   */
  addUniversalDimensionsListener(handler: (dimensions: { width: number; height: number }) => void): (() => void) | null {
    if (this.environmentInfo.isWeb) {
      return this.safeAddEventListener('window', 'resize', () => {
        const dimensions = this.getScreenDimensions();
        if (dimensions) {
          handler(dimensions);
        }
      });
    }

    if (this.environmentInfo.isReactNative) {
      return this.addDimensionsChangeListener(handler);
    }

    return null;
  }

  /**
   * 获取用户代理信息（仅Web环境）
   */
  getUserAgent(): string | null {
    if (this.environmentInfo.isWeb && navigator) {
      return navigator.userAgent;
    }
    return null;
  }

  /**
   * 检测设备类型
   */
  getDeviceType(): 'mobile' | 'tablet' | 'desktop' | 'unknown' {
    const dimensions = this.getScreenDimensions();
    
    if (!dimensions) {
      return 'unknown';
    }

    const { width } = dimensions;
    
    if (width <= 768) {
      return 'mobile';
    } else if (width <= 1024) {
      return 'tablet';
    } else {
      return 'desktop';
    }
  }

  /**
   * 获取环境特定的存储API
   */
  getStorage(): Storage | null {
    if (this.environmentInfo.isWeb && typeof localStorage !== 'undefined') {
      return localStorage;
    }

    if (this.environmentInfo.isReactNative) {
      try {
        const AsyncStorage = require('@react-native-async-storage/async-storage');
        return AsyncStorage;
      } catch (error) {
        console.warn('⚠️ AsyncStorage不可用:', error);
      }
    }

    return null;
  }

  /**
   * 打印环境诊断信息
   */
  printDiagnostics(): void {
    console.log('🔍 环境诊断信息:');
    console.log('  平台:', this.environmentInfo.platform);
    console.log('  React Native:', this.environmentInfo.isReactNative);
    console.log('  Web:', this.environmentInfo.isWeb);
    console.log('  Node.js:', this.environmentInfo.isNode);
    console.log('  Window对象:', this.environmentInfo.hasWindow);
    console.log('  Document对象:', this.environmentInfo.hasDocument);
    console.log('  Dimensions API:', this.environmentInfo.hasDimensions);
    
    const dimensions = this.getScreenDimensions();
    if (dimensions) {
      console.log('  屏幕尺寸:', `${dimensions.width}x${dimensions.height}`);
    }
    
    console.log('  设备类型:', this.getDeviceType());
  }
}

// 导出单例实例
export const environmentDetector = EnvironmentDetector.getInstance();