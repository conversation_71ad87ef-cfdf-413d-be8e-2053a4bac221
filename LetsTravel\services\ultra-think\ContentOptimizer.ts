/**
 * 🚀 Content Optimizer - 内容优化器
 * Phase 3: 实时内容生成引擎 - 统一内容优化和集成
 * 集成所有内容生成模块，进行统一优化和测试
 */

import { RealTimeDataFusion, FusedData, FusionRequest } from './engines/RealTimeDataFusion';
import { CreativeAI, CreativeContent, CreativeRequest } from './engines/CreativeAI';
import { QualityValidator, ValidationRequest, ValidationResult } from './engines/QualityValidator';
import { UltraThinkCacheManager } from './UltraThinkCacheManager';

// ===== 内容优化器接口定义 =====

export interface OptimizationRequest {
  destination: string;
  contentType: 'journey' | 'activity' | 'recommendation' | 'experience';
  requirements: {
    quality: number; // 0-1, 质量要求
    creativity: number; // 0-1, 创意要求
    uniqueness: number; // 0-1, 独特性要求
    realtime: boolean; // 是否需要实时数据
  };
  context: {
    userProfile?: any;
    seasonalContext?: any;
    culturalContext?: any;
    preferences?: any;
  };
  constraints: {
    maxProcessingTime: number; // 最大处理时间(ms)
    budgetLimit: number; // 成本限制
    fallbackEnabled: boolean; // 是否启用降级
  };
}

export interface OptimizedContent {
  id: string;
  type: string;
  content: any;
  realTimeData?: FusedData;
  creativeElements: CreativeContent[];
  qualityMetrics: ValidationResult;
  optimizationScore: number; // 0-1, 优化评分
  processingMetrics: {
    totalTime: number;
    dataFusionTime: number;
    creativeGenerationTime: number;
    qualityValidationTime: number;
    optimizationTime: number;
  };
  costMetrics: {
    totalCost: number;
    apiCosts: number;
    llmCosts: number;
    processingCosts: number;
  };
  metadata: {
    optimizationDate: Date;
    version: string;
    components: string[];
    iterations: number;
    fallbackUsed: boolean;
  };
}

export interface OptimizationPipeline {
  stages: OptimizationStage[];
  currentStage: number;
  totalStages: number;
  status: 'pending' | 'running' | 'completed' | 'failed';
  results: Map<string, any>;
}

export interface OptimizationStage {
  name: string;
  component: string;
  enabled: boolean;
  timeout: number;
  retries: number;
  fallbackEnabled: boolean;
  executor: (input: any, context: any) => Promise<any>;
}

// ===== Content Optimizer 核心类 =====

export class ContentOptimizer {
  private static instance: ContentOptimizer;
  private realTimeDataFusion = RealTimeDataFusion.getInstance();
  private creativeAI = CreativeAI.getInstance();
  private qualityValidator = QualityValidator.getInstance();
  private cacheManager = UltraThinkCacheManager.getInstance();
  private optimizationHistory: OptimizedContent[] = [];
  private performanceMetrics = new Map<string, any>();

  private constructor() {
    this.initializePerformanceMetrics();
    console.log('🚀 Content Optimizer 初始化完成');
  }

  static getInstance(): ContentOptimizer {
    if (!ContentOptimizer.instance) {
      ContentOptimizer.instance = new ContentOptimizer();
    }
    return ContentOptimizer.instance;
  }

  /**
   * 🎯 内容优化 - 主要入口方法
   */
  async optimizeContent(request: OptimizationRequest): Promise<OptimizedContent> {
    const startTime = Date.now();
    const optimizationId = this.generateOptimizationId(request);
    console.log(`🚀 开始内容优化: ${optimizationId} (${request.contentType})`);

    try {
      // 1. 检查缓存
      const cached = await this.checkOptimizationCache(request);
      if (cached) {
        console.log('✅ 内容优化缓存命中');
        return cached;
      }

      // 2. 创建优化管道
      const pipeline = this.createOptimizationPipeline(request);
      console.log(`🔄 创建${pipeline.totalStages}阶段优化管道`);

      // 3. 执行优化管道
      const pipelineResults = await this.executePipeline(pipeline, request);

      // 4. 整合优化结果
      const integratedContent = this.integrateResults(pipelineResults, request);

      // 5. 最终质量验证
      const finalValidation = await this.performFinalValidation(integratedContent, request);

      // 6. 计算优化评分
      const optimizationScore = this.calculateOptimizationScore(integratedContent, finalValidation);

      // 7. 构建最终结果
      const optimizedContent: OptimizedContent = {
        id: optimizationId,
        type: request.contentType,
        content: integratedContent,
        realTimeData: pipelineResults.get('realtime-data'),
        creativeElements: pipelineResults.get('creative-content') || [],
        qualityMetrics: finalValidation,
        optimizationScore,
        processingMetrics: this.calculateProcessingMetrics(pipelineResults, startTime),
        costMetrics: this.calculateCostMetrics(pipelineResults),
        metadata: {
          optimizationDate: new Date(),
          version: '3.0',
          components: Array.from(pipelineResults.keys()),
          iterations: 1,
          fallbackUsed: this.checkFallbackUsage(pipelineResults)
        }
      };

      // 8. 缓存优化结果
      await this.cacheOptimizedContent(optimizedContent);

      // 9. 更新性能指标
      this.updatePerformanceMetrics(optimizedContent);

      // 10. 记录优化历史
      this.recordOptimizationHistory(optimizedContent);

      console.log(`✅ 内容优化完成: 评分${optimizationScore.toFixed(2)}, 耗时${optimizedContent.processingMetrics.totalTime}ms`);
      return optimizedContent;

    } catch (error) {
      console.error('❌ 内容优化失败:', error);
      return this.generateFallbackContent(request, optimizationId, startTime);
    }
  }

  /**
   * 🔄 创建优化管道
   */
  private createOptimizationPipeline(request: OptimizationRequest): OptimizationPipeline {
    const stages: OptimizationStage[] = [];

    // 阶段1: 实时数据融合 (如果需要)
    if (request.requirements.realtime) {
      stages.push({
        name: 'Real-Time Data Fusion',
        component: 'realtime-data-fusion',
        enabled: true,
        timeout: 10000,
        retries: 2,
        fallbackEnabled: true,
        executor: this.executeDataFusion.bind(this)
      });
    }

    // 阶段2: 创意内容生成
    stages.push({
      name: 'Creative Content Generation',
      component: 'creative-ai',
      enabled: true,
      timeout: 15000,
      retries: 3,
      fallbackEnabled: true,
      executor: this.executeCreativeGeneration.bind(this)
    });

    // 阶段3: 质量验证
    stages.push({
      name: 'Quality Validation',
      component: 'quality-validator',
      enabled: true,
      timeout: 5000,
      retries: 1,
      fallbackEnabled: false,
      executor: this.executeQualityValidation.bind(this)
    });

    // 阶段4: 内容增强 (如果质量不达标)
    stages.push({
      name: 'Content Enhancement',
      component: 'content-enhancer',
      enabled: false, // 动态启用
      timeout: 10000,
      retries: 2,
      fallbackEnabled: true,
      executor: this.executeContentEnhancement.bind(this)
    });

    return {
      stages,
      currentStage: 0,
      totalStages: stages.length,
      status: 'pending',
      results: new Map()
    };
  }

  /**
   * ⚡ 执行优化管道
   */
  private async executePipeline(pipeline: OptimizationPipeline, request: OptimizationRequest): Promise<Map<string, any>> {
    pipeline.status = 'running';
    const results = new Map<string, any>();

    for (let i = 0; i < pipeline.stages.length; i++) {
      const stage = pipeline.stages[i];
      pipeline.currentStage = i;

      if (!stage.enabled) {
        console.log(`⏭️ 跳过阶段: ${stage.name}`);
        continue;
      }

      console.log(`🔄 执行阶段 ${i + 1}/${pipeline.totalStages}: ${stage.name}`);

      try {
        const stageStartTime = Date.now();
        const stageResult = await this.executeStageWithTimeout(stage, request, results);
        const stageTime = Date.now() - stageStartTime;

        results.set(stage.component, stageResult);
        results.set(`${stage.component}_time`, stageTime);

        console.log(`✅ 阶段${stage.name}完成: ${stageTime}ms`);

        // 动态启用内容增强阶段
        if (stage.component === 'quality-validator' && stageResult.overallScore < 0.7) {
          pipeline.stages[3].enabled = true; // 启用内容增强
          console.log('🔧 启用内容增强阶段');
        }

      } catch (error) {
        console.error(`❌ 阶段${stage.name}失败:`, error);

        if (stage.fallbackEnabled) {
          console.log(`🛡️ 执行${stage.name}降级处理`);
          const fallbackResult = await this.executeStageFallback(stage, request, results);
          results.set(stage.component, fallbackResult);
          results.set(`${stage.component}_fallback`, true);
        } else {
          throw error;
        }
      }
    }

    pipeline.status = 'completed';
    return results;
  }

  /**
   * 🔧 执行阶段方法
   */
  private async executeDataFusion(request: OptimizationRequest, results: Map<string, any>): Promise<FusedData> {
    const fusionRequest: FusionRequest = {
      destination: request.destination,
      dataTypes: ['places', 'weather', 'events'],
      priority: 'high',
      maxAge: 60, // 1小时
      fallbackEnabled: true,
      qualityThreshold: 0.6,
      timeout: 8000
    };

    return await this.realTimeDataFusion.fuseRealTimeData(fusionRequest);
  }

  private async executeCreativeGeneration(request: OptimizationRequest, results: Map<string, any>): Promise<CreativeContent[]> {
    const creativeContents: CreativeContent[] = [];
    const contentTypes = this.getCreativeContentTypes(request.contentType);

    for (const type of contentTypes) {
      const creativeRequest: CreativeRequest = {
        type,
        context: {
          destination: request.destination,
          activity: request.contentType,
          style: 'inspiring',
          ...request.context
        },
        constraints: {
          length: 'medium',
          tone: 'enthusiastic',
          uniqueness: request.requirements.uniqueness,
          creativity: request.requirements.creativity
        },
        previousContent: this.getPreviousContent(request.destination)
      };

      try {
        const creativeContent = await this.creativeAI.generateCreativeContent(creativeRequest);
        creativeContents.push(creativeContent);
      } catch (error) {
        console.warn(`⚠️ 创意内容生成失败: ${type}`, error);
      }
    }

    return creativeContents;
  }

  private async executeQualityValidation(request: OptimizationRequest, results: Map<string, any>): Promise<ValidationResult> {
    const content = this.buildContentForValidation(results);
    
    const validationRequest: ValidationRequest = {
      content,
      type: 'journey-plan',
      context: {
        destination: request.destination,
        userProfile: request.context.userProfile
      },
      qualityThreshold: request.requirements.quality,
      strictMode: request.requirements.quality > 0.8
    };

    return await this.qualityValidator.validateQuality(validationRequest);
  }

  private async executeContentEnhancement(request: OptimizationRequest, results: Map<string, any>): Promise<any> {
    // 内容增强逻辑
    const currentContent = results.get('creative-ai') || [];
    const qualityResult = results.get('quality-validator');

    // 基于质量验证结果进行内容增强
    const enhancedContent = await this.enhanceContentBasedOnIssues(currentContent, qualityResult);
    
    return enhancedContent;
  }

  /**
   * 🔧 辅助方法
   */
  private initializePerformanceMetrics(): void {
    this.performanceMetrics.set('totalOptimizations', 0);
    this.performanceMetrics.set('averageProcessingTime', 0);
    this.performanceMetrics.set('averageOptimizationScore', 0);
    this.performanceMetrics.set('successRate', 0);
    this.performanceMetrics.set('componentPerformance', new Map());
  }

  private generateOptimizationId(request: OptimizationRequest): string {
    return `opt_${request.contentType}_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`;
  }

  private async checkOptimizationCache(request: OptimizationRequest): Promise<OptimizedContent | null> {
    const cacheKey = this.generateCacheKey(request);
    return await this.cacheManager.get<OptimizedContent>(cacheKey, 'content-optimization');
  }

  private generateCacheKey(request: OptimizationRequest): string {
    const keyData = {
      destination: request.destination,
      type: request.contentType,
      quality: request.requirements.quality,
      creativity: request.requirements.creativity
    };

    // React Native兼容的base64编码
    const jsonString = JSON.stringify(keyData);
    let base64String: string;

    try {
      // 尝试使用btoa (Web环境)
      if (typeof btoa !== 'undefined') {
        base64String = btoa(unescape(encodeURIComponent(jsonString)));
      } else {
        // React Native环境下的替代方案
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';
        let result = '';
        let i = 0;

        while (i < jsonString.length) {
          const a = jsonString.charCodeAt(i++);
          const b = i < jsonString.length ? jsonString.charCodeAt(i++) : 0;
          const c = i < jsonString.length ? jsonString.charCodeAt(i++) : 0;

          const bitmap = (a << 16) | (b << 8) | c;

          result += chars.charAt((bitmap >> 18) & 63);
          result += chars.charAt((bitmap >> 12) & 63);
          result += i - 2 < jsonString.length ? chars.charAt((bitmap >> 6) & 63) : '=';
          result += i - 1 < jsonString.length ? chars.charAt(bitmap & 63) : '=';
        }

        base64String = result;
      }
    } catch (error) {
      console.warn('⚠️ Base64编码失败，使用简化方案:', error);
      // 简化的哈希方案
      let hash = 0;
      for (let i = 0; i < jsonString.length; i++) {
        const char = jsonString.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // 转换为32位整数
      }
      base64String = Math.abs(hash).toString(36);
    }

    return base64String.replace(/[^a-zA-Z0-9]/g, '').substring(0, 32);
  }

  private async executeStageWithTimeout(stage: OptimizationStage, request: OptimizationRequest, results: Map<string, any>): Promise<any> {
    return Promise.race([
      stage.executor(request, results),
      this.createTimeoutPromise(stage.timeout, stage.name)
    ]);
  }

  private createTimeoutPromise(timeout: number, stageName: string): Promise<never> {
    return new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error(`阶段${stageName}超时: ${timeout}ms`));
      }, timeout);
    });
  }

  private async executeStageFallback(stage: OptimizationStage, request: OptimizationRequest, results: Map<string, any>): Promise<any> {
    // 简化的降级处理
    switch (stage.component) {
      case 'realtime-data-fusion':
        return { destination: request.destination, dataPoints: [], fusionScore: 0.3 };
      case 'creative-ai':
        return [{ content: `${request.destination}的精彩体验`, type: 'fallback' }];
      case 'quality-validator':
        return { passed: true, overallScore: 0.6, issues: [], suggestions: [] };
      default:
        return {};
    }
  }

  private integrateResults(results: Map<string, any>, request: OptimizationRequest): any {
    const realTimeData = results.get('realtime-data-fusion');
    const creativeContent = results.get('creative-ai') || [];
    
    return {
      destination: request.destination,
      type: request.contentType,
      realTimeData,
      creativeElements: creativeContent,
      title: creativeContent[0]?.content || `${request.destination}${request.contentType}`,
      description: this.buildIntegratedDescription(creativeContent, realTimeData),
      highlights: this.extractHighlights(creativeContent),
      metadata: {
        generated: new Date(),
        components: Array.from(results.keys())
      }
    };
  }

  private async performFinalValidation(content: any, request: OptimizationRequest): Promise<ValidationResult> {
    const validationRequest: ValidationRequest = {
      content,
      type: 'journey-plan',
      context: { destination: request.destination },
      qualityThreshold: request.requirements.quality,
      strictMode: false
    };

    return await this.qualityValidator.validateQuality(validationRequest);
  }

  private calculateOptimizationScore(content: any, validation: ValidationResult): number {
    let score = validation.overallScore * 0.7; // 质量权重70%
    
    // 内容丰富度
    if (content.creativeElements && content.creativeElements.length > 0) {
      score += 0.1;
    }
    
    // 实时数据集成
    if (content.realTimeData && content.realTimeData.fusionScore > 0.7) {
      score += 0.1;
    }
    
    // 独特性
    if (content.creativeElements && content.creativeElements.some((c: any) => c.uniquenessScore > 0.8)) {
      score += 0.1;
    }
    
    return Math.min(1.0, score);
  }

  private calculateProcessingMetrics(results: Map<string, any>, startTime: number): any {
    return {
      totalTime: Date.now() - startTime,
      dataFusionTime: results.get('realtime-data-fusion_time') || 0,
      creativeGenerationTime: results.get('creative-ai_time') || 0,
      qualityValidationTime: results.get('quality-validator_time') || 0,
      optimizationTime: results.get('content-enhancer_time') || 0
    };
  }

  private calculateCostMetrics(results: Map<string, any>): any {
    // 简化的成本计算
    let totalCost = 0;
    let apiCosts = 0;
    let llmCosts = 0;

    if (results.has('realtime-data-fusion')) apiCosts += 0.002;
    if (results.has('creative-ai')) llmCosts += 0.003;

    totalCost = apiCosts + llmCosts;

    return {
      totalCost,
      apiCosts,
      llmCosts,
      processingCosts: 0.001
    };
  }

  private checkFallbackUsage(results: Map<string, any>): boolean {
    return Array.from(results.keys()).some(key => key.endsWith('_fallback'));
  }

  private getCreativeContentTypes(contentType: string): string[] {
    const typeMapping = {
      journey: ['description', 'story', 'recommendation'],
      activity: ['activity-name', 'description', 'experience'],
      recommendation: ['recommendation', 'insight'],
      experience: ['experience', 'story', 'insight']
    };

    return typeMapping[contentType as keyof typeof typeMapping] || ['description'];
  }

  private getPreviousContent(destination: string): string[] {
    return this.optimizationHistory
      .filter(opt => opt.content.destination === destination)
      .slice(-3)
      .map(opt => opt.content.title || '');
  }

  private buildContentForValidation(results: Map<string, any>): any {
    const creativeContent = results.get('creative-ai') || [];
    return {
      title: creativeContent[0]?.content || '旅行体验',
      description: creativeContent.map((c: any) => c.content).join(' '),
      type: 'optimized-content'
    };
  }

  private async enhanceContentBasedOnIssues(content: any[], qualityResult: ValidationResult): Promise<any[]> {
    // 基于质量问题进行内容增强
    const enhanced = [...content];
    
    qualityResult.issues.forEach(issue => {
      if (issue.autoFixable) {
        // 执行自动修复
        console.log(`🔧 自动修复: ${issue.description}`);
      }
    });

    return enhanced;
  }

  private buildIntegratedDescription(creativeContent: any[], realTimeData: any): string {
    const descriptions = creativeContent
      .filter(c => c.type === 'description')
      .map(c => c.content);
    
    if (descriptions.length > 0) {
      return descriptions[0];
    }
    
    return '精彩的旅行体验等待您的发现';
  }

  private extractHighlights(creativeContent: any[]): string[] {
    const highlights: string[] = [];
    
    creativeContent.forEach(content => {
      if (content.highlights) {
        highlights.push(...content.highlights);
      }
    });
    
    return highlights.slice(0, 5); // 最多5个亮点
  }

  private async cacheOptimizedContent(content: OptimizedContent): Promise<void> {
    const cacheKey = `opt_${content.id}`;
    await this.cacheManager.set(cacheKey, content, 'content-optimization', 2 * 60 * 60 * 1000); // 2小时
  }

  private updatePerformanceMetrics(content: OptimizedContent): void {
    const total = this.performanceMetrics.get('totalOptimizations') + 1;
    this.performanceMetrics.set('totalOptimizations', total);

    // 更新平均处理时间
    const avgTime = this.performanceMetrics.get('averageProcessingTime');
    const newAvgTime = (avgTime * (total - 1) + content.processingMetrics.totalTime) / total;
    this.performanceMetrics.set('averageProcessingTime', newAvgTime);

    // 更新平均优化评分
    const avgScore = this.performanceMetrics.get('averageOptimizationScore');
    const newAvgScore = (avgScore * (total - 1) + content.optimizationScore) / total;
    this.performanceMetrics.set('averageOptimizationScore', newAvgScore);
  }

  private recordOptimizationHistory(content: OptimizedContent): void {
    this.optimizationHistory.push(content);
    
    // 保持历史记录在合理范围内
    if (this.optimizationHistory.length > 100) {
      this.optimizationHistory = this.optimizationHistory.slice(-50);
    }
  }

  private generateFallbackContent(request: OptimizationRequest, id: string, startTime: number): OptimizedContent {
    return {
      id,
      type: request.contentType,
      content: {
        destination: request.destination,
        title: `${request.destination}${request.contentType}`,
        description: '精彩的旅行体验等待您的发现',
        highlights: ['独特体验', '难忘回忆']
      },
      creativeElements: [],
      qualityMetrics: {
        passed: false,
        overallScore: 0.3,
        metrics: {
          accuracy: 0.3, relevance: 0.3, completeness: 0.3, freshness: 0.5,
          uniqueness: 0.3, readability: 0.5, culturalSensitivity: 0.8, practicalValue: 0.3
        },
        issues: [],
        suggestions: [],
        metadata: {
          validationDate: new Date(),
          validator: 'fallback',
          processingTime: 0,
          rulesApplied: []
        }
      },
      optimizationScore: 0.3,
      processingMetrics: {
        totalTime: Date.now() - startTime,
        dataFusionTime: 0,
        creativeGenerationTime: 0,
        qualityValidationTime: 0,
        optimizationTime: 0
      },
      costMetrics: {
        totalCost: 0,
        apiCosts: 0,
        llmCosts: 0,
        processingCosts: 0
      },
      metadata: {
        optimizationDate: new Date(),
        version: '3.0',
        components: ['fallback'],
        iterations: 0,
        fallbackUsed: true
      }
    };
  }

  /**
   * 📊 获取优化统计
   */
  getOptimizationStats() {
    const recentOptimizations = this.optimizationHistory.slice(-20);
    
    return {
      totalOptimizations: this.performanceMetrics.get('totalOptimizations'),
      averageProcessingTime: this.performanceMetrics.get('averageProcessingTime'),
      averageOptimizationScore: this.performanceMetrics.get('averageOptimizationScore'),
      recentOptimizations: recentOptimizations.length,
      successRate: recentOptimizations.length > 0 
        ? recentOptimizations.filter(opt => opt.optimizationScore > 0.7).length / recentOptimizations.length 
        : 0,
      costEfficiency: recentOptimizations.length > 0 
        ? recentOptimizations.reduce((sum, opt) => sum + opt.costMetrics.totalCost, 0) / recentOptimizations.length 
        : 0
    };
  }

  /**
   * 🧪 运行集成测试
   */
  async runIntegrationTests(): Promise<{ success: boolean; results: any }> {
    console.log('🧪 开始内容优化器集成测试');
    
    const testRequest: OptimizationRequest = {
      destination: '东京',
      contentType: 'journey',
      requirements: {
        quality: 0.8,
        creativity: 0.7,
        uniqueness: 0.6,
        realtime: true
      },
      context: {
        preferences: { culturalInterests: ['传统文化', '现代艺术'] }
      },
      constraints: {
        maxProcessingTime: 30000,
        budgetLimit: 0.01,
        fallbackEnabled: true
      }
    };

    try {
      const result = await this.optimizeContent(testRequest);
      
      const testResults = {
        optimizationScore: result.optimizationScore,
        processingTime: result.processingMetrics.totalTime,
        qualityPassed: result.qualityMetrics.passed,
        componentsUsed: result.metadata.components.length,
        costEfficiency: result.costMetrics.totalCost < testRequest.constraints.budgetLimit
      };

      const success = testResults.optimizationScore > 0.6 && 
                     testResults.processingTime < testRequest.constraints.maxProcessingTime &&
                     testResults.costEfficiency;

      console.log(`✅ 集成测试${success ? '通过' : '失败'}`);
      return { success, results: testResults };

    } catch (error) {
      console.error('❌ 集成测试失败:', error);
      return { success: false, results: { error: error.message } };
    }
  }
}
