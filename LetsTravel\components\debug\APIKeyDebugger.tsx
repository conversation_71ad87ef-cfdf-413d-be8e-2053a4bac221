/**
 * 🔍 API密钥调试组件
 * 用于在应用运行时检查环境变量
 */

import React, { useEffect, useState } from 'react';
import { View, Text, ScrollView, StyleSheet, TouchableOpacity } from 'react-native';
import { GOOGLE_PLACES_CONFIG } from '../../config/GooglePlacesConfig';
import { environmentManager } from '../../config/EnvironmentManager';

interface APIKeyInfo {
  name: string;
  value: string | undefined;
  length: number;
  startsWithAIza: boolean;
  isValid: boolean;
}

export const APIKeyDebugger: React.FC = () => {
  const [apiKeys, setApiKeys] = useState<APIKeyInfo[]>([]);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // 获取环境管理器状态报告
    const statusReport = environmentManager.getStatusReport();
    const validation = environmentManager.validateAPIKeys();

    const keyInfos: APIKeyInfo[] = [
      {
        name: 'Google API (环境管理器)',
        value: environmentManager.getGoogleAPIKey(),
        length: environmentManager.getGoogleAPIKey().length,
        startsWithAIza: environmentManager.getGoogleAPIKey().startsWith('AIza'),
        isValid: validation.google
      },
      {
        name: 'SerpAPI (环境管理器)',
        value: environmentManager.getSerpAPIKey(),
        length: environmentManager.getSerpAPIKey().length,
        startsWithAIza: false, // SerpAPI密钥不以AIza开头
        isValid: validation.serpapi
      },
      {
        name: 'Amadeus API (环境管理器)',
        value: environmentManager.getAmadeusAPIKey(),
        length: environmentManager.getAmadeusAPIKey().length,
        startsWithAIza: false,
        isValid: validation.amadeus
      },
      {
        name: 'GOOGLE_PLACES_CONFIG.API_KEY',
        value: GOOGLE_PLACES_CONFIG.API_KEY,
        length: GOOGLE_PLACES_CONFIG.API_KEY.length,
        startsWithAIza: GOOGLE_PLACES_CONFIG.API_KEY.startsWith('AIza'),
        isValid: GOOGLE_PLACES_CONFIG.API_KEY.length > 30 && GOOGLE_PLACES_CONFIG.API_KEY.startsWith('AIza')
      }
    ];

    setApiKeys(keyInfos);
  }, []);

  if (!isVisible) {
    return (
      <TouchableOpacity 
        style={styles.toggleButton}
        onPress={() => setIsVisible(true)}
      >
        <Text style={styles.toggleButtonText}>🔍 Debug API Keys</Text>
      </TouchableOpacity>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>🔍 API密钥调试信息</Text>
        <TouchableOpacity 
          style={styles.closeButton}
          onPress={() => setIsVisible(false)}
        >
          <Text style={styles.closeButtonText}>✕</Text>
        </TouchableOpacity>
      </View>
      
      <ScrollView style={styles.content}>
        {apiKeys.map((keyInfo, index) => (
          <View key={index} style={[
            styles.keyItem,
            keyInfo.isValid ? styles.validKey : styles.invalidKey
          ]}>
            <Text style={styles.keyName}>{keyInfo.name}</Text>
            <Text style={styles.keyValue}>
              {keyInfo.value === 'undefined' ? 
                '❌ 未设置' : 
                `${keyInfo.value.substring(0, 10)}... (长度: ${keyInfo.length})`
              }
            </Text>
            <View style={styles.keyDetails}>
              <Text style={[styles.detail, keyInfo.startsWithAIza ? styles.valid : styles.invalid]}>
                以AIza开头: {keyInfo.startsWithAIza ? '✅' : '❌'}
              </Text>
              <Text style={[styles.detail, keyInfo.length > 30 ? styles.valid : styles.invalid]}>
                长度&gt;30: {keyInfo.length > 30 ? '✅' : '❌'}
              </Text>
              <Text style={[styles.detail, keyInfo.isValid ? styles.valid : styles.invalid]}>
                有效: {keyInfo.isValid ? '✅' : '❌'}
              </Text>
            </View>
          </View>
        ))}
        
        <View style={styles.instructions}>
          <Text style={styles.instructionsTitle}>💡 修复建议:</Text>
          <Text style={styles.instructionsText}>
            1. 确保.env文件中EXPO_PUBLIC_GOOGLE_PLACES_API_KEY已正确配置{'\n'}
            2. 重启开发服务器 (npm start){'\n'}
            3. 清除Expo缓存 (expo start -c){'\n'}
            4. 检查是否有多个.env文件冲突
          </Text>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  toggleButton: {
    position: 'absolute',
    top: 100,
    right: 20,
    backgroundColor: '#007AFF',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    zIndex: 1000,
  },
  toggleButtonText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  container: {
    position: 'absolute',
    top: 50,
    left: 10,
    right: 10,
    bottom: 50,
    backgroundColor: 'rgba(0, 0, 0, 0.95)',
    borderRadius: 12,
    zIndex: 1000,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#333',
  },
  title: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  closeButton: {
    backgroundColor: '#FF3B30',
    width: 30,
    height: 30,
    borderRadius: 15,
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  keyItem: {
    marginBottom: 16,
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
  },
  validKey: {
    backgroundColor: '#1B5E20',
    borderColor: '#4CAF50',
  },
  invalidKey: {
    backgroundColor: '#B71C1C',
    borderColor: '#F44336',
  },
  keyName: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  keyValue: {
    color: '#E0E0E0',
    fontSize: 12,
    marginBottom: 8,
  },
  keyDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  detail: {
    fontSize: 10,
  },
  valid: {
    color: '#4CAF50',
  },
  invalid: {
    color: '#F44336',
  },
  instructions: {
    marginTop: 20,
    padding: 12,
    backgroundColor: '#1A1A1A',
    borderRadius: 8,
  },
  instructionsTitle: {
    color: '#FFC107',
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  instructionsText: {
    color: '#E0E0E0',
    fontSize: 12,
    lineHeight: 18,
  },
});
