/**
 * 💎 Hidden Gems Discovery - 隐藏宝石发现引擎
 * Phase 4: 惊喜体验注入器 - 隐藏宝石发现
 * 挖掘小众景点和特色体验，为用户提供意外惊喜
 */

import { UltraThinkLLMManager } from '../../ai/UltraThinkLLMManager';
import { UltraThinkAPIRouter } from '../UltraThinkAPIRouter';
import { UltraThinkCacheManager } from '../UltraThinkCacheManager';

// ===== 隐藏宝石发现接口定义 =====

export interface HiddenGem {
  id: string;
  name: string;
  type: 'attraction' | 'restaurant' | 'shop' | 'experience' | 'viewpoint' | 'cultural' | 'nature';
  location: {
    address: string;
    coordinates: { lat: number; lng: number };
    district: string;
    accessibility: 'easy' | 'moderate' | 'difficult';
  };
  description: string;
  uniqueness: number; // 0-1, 独特性评分
  hiddenLevel: number; // 0-1, 隐藏程度 (1=极其隐秘)
  localPopularity: number; // 0-1, 当地人喜爱程度
  touristRatio: number; // 0-1, 游客比例 (0=纯当地人, 1=全是游客)
  bestTime: {
    season: string[];
    timeOfDay: string[];
    duration: number; // 建议停留时间(分钟)
  };
  discoveryStory: string; // 发现故事
  localTips: string[]; // 当地人建议
  surpriseFactors: string[]; // 惊喜因素
  metadata: {
    discoveryDate: Date;
    source: string[];
    confidence: number;
    lastVerified: Date;
  };
}

export interface DiscoveryRequest {
  destination: string;
  userProfile: {
    interests: string[];
    adventureLevel: 'conservative' | 'moderate' | 'adventurous';
    culturalOpenness: number; // 0-1
    budgetRange: 'budget' | 'mid-range' | 'luxury';
    groupType: 'solo' | 'couple' | 'family' | 'group';
  };
  constraints: {
    maxDistance: number; // 最大距离(km)
    timeAvailable: number; // 可用时间(小时)
    accessibilityNeeds: string[];
    avoidCrowds: boolean;
  };
  preferences: {
    hiddenLevel: number; // 期望隐藏程度 0-1
    localExperience: number; // 期望当地体验程度 0-1
    surpriseLevel: number; // 期望惊喜程度 0-1
  };
}

export interface DiscoveryResult {
  destination: string;
  hiddenGems: HiddenGem[];
  discoveryScore: number; // 0-1, 发现质量评分
  surpriseIndex: number; // 0-1, 惊喜指数
  localAuthenticity: number; // 0-1, 当地真实性
  recommendations: {
    featured: HiddenGem[]; // 精选推荐
    alternatives: HiddenGem[]; // 备选方案
    combinations: HiddenGemCombination[]; // 组合推荐
  };
  discoveryInsights: {
    patterns: string[]; // 发现模式
    trends: string[]; // 趋势分析
    secrets: string[]; // 当地秘密
  };
  metadata: {
    discoveryDate: Date;
    totalSearched: number;
    uniqueFinds: number;
    processingTime: number;
  };
}

export interface HiddenGemCombination {
  id: string;
  name: string;
  gems: HiddenGem[];
  theme: string;
  totalDuration: number;
  route: string;
  synergy: number; // 0-1, 协同效应
  story: string; // 组合故事
}

// ===== Hidden Gems Discovery 核心类 =====

export class HiddenGemsDiscovery {
  private static instance: HiddenGemsDiscovery;
  private llmManager = UltraThinkLLMManager.getInstance();
  private apiRouter = UltraThinkAPIRouter.getInstance();
  private cacheManager = UltraThinkCacheManager.getInstance();
  private gemsDatabase = new Map<string, HiddenGem[]>();
  private discoveryPatterns = new Map<string, any>();
  private localKnowledge = new Map<string, any>();

  private constructor() {
    this.initializeDiscoveryPatterns();
    this.initializeLocalKnowledge();
    console.log('💎 Hidden Gems Discovery 初始化完成');
  }

  static getInstance(): HiddenGemsDiscovery {
    if (!HiddenGemsDiscovery.instance) {
      HiddenGemsDiscovery.instance = new HiddenGemsDiscovery();
    }
    return HiddenGemsDiscovery.instance;
  }

  /**
   * 🔍 发现隐藏宝石 - 主要入口方法
   */
  async discoverHiddenGems(request: DiscoveryRequest): Promise<DiscoveryResult> {
    const startTime = Date.now();
    console.log(`💎 开始隐藏宝石发现: ${request.destination}`);

    try {
      // 1. 检查缓存
      const cacheKey = this.generateCacheKey(request);
      const cached = await this.cacheManager.get<DiscoveryResult>(cacheKey, 'hidden-gems');
      
      if (cached && this.isCacheValid(cached)) {
        console.log('✅ 隐藏宝石发现缓存命中');
        return cached;
      }

      // 2. 多源数据挖掘
      const rawGems = await this.mineHiddenGems(request);
      console.log(`⛏️ 挖掘到${rawGems.length}个潜在宝石`);

      // 3. 智能筛选和评估
      const evaluatedGems = await this.evaluateGems(rawGems, request);
      console.log(`📊 评估后保留${evaluatedGems.length}个高质量宝石`);

      // 4. 个性化匹配
      const matchedGems = this.matchUserPreferences(evaluatedGems, request);

      // 5. 生成组合推荐
      const combinations = this.generateGemCombinations(matchedGems, request);

      // 6. 发现洞察分析
      const insights = await this.generateDiscoveryInsights(matchedGems, request);

      // 7. 构建发现结果
      const result: DiscoveryResult = {
        destination: request.destination,
        hiddenGems: matchedGems,
        discoveryScore: this.calculateDiscoveryScore(matchedGems),
        surpriseIndex: this.calculateSurpriseIndex(matchedGems, request),
        localAuthenticity: this.calculateLocalAuthenticity(matchedGems),
        recommendations: {
          featured: matchedGems.slice(0, 5),
          alternatives: matchedGems.slice(5, 10),
          combinations
        },
        discoveryInsights: insights,
        metadata: {
          discoveryDate: new Date(),
          totalSearched: rawGems.length,
          uniqueFinds: matchedGems.length,
          processingTime: Date.now() - startTime
        }
      };

      // 8. 缓存结果
      await this.cacheDiscoveryResult(result);

      console.log(`✅ 隐藏宝石发现完成: 发现${result.hiddenGems.length}个宝石, 惊喜指数${result.surpriseIndex.toFixed(2)}`);
      return result;

    } catch (error) {
      console.error('❌ 隐藏宝石发现失败:', error);
      return this.generateFallbackResult(request, startTime);
    }
  }

  /**
   * ⛏️ 多源数据挖掘
   */
  private async mineHiddenGems(request: DiscoveryRequest): Promise<HiddenGem[]> {
    const gems: HiddenGem[] = [];

    // 1. 本地知识库挖掘
    const localGems = this.mineFromLocalKnowledge(request);
    gems.push(...localGems);

    // 2. API数据挖掘
    const apiGems = await this.mineFromAPIs(request);
    gems.push(...apiGems);

    // 3. LLM智能挖掘
    const llmGems = await this.mineWithLLM(request);
    gems.push(...llmGems);

    // 4. 社交媒体挖掘
    const socialGems = await this.mineFromSocialMedia(request);
    gems.push(...socialGems);

    return this.deduplicateGems(gems);
  }

  /**
   * 🧠 LLM智能挖掘
   */
  private async mineWithLLM(request: DiscoveryRequest): Promise<HiddenGem[]> {
    const prompt = this.buildLLMDiscoveryPrompt(request);
    
    try {
      const llmResponse = await this.llmManager.callLLM({
        prompt,
        taskType: 'creative-content',
        context: {
          destination: request.destination,
          complexity: 'high'
        }
      });

      if (llmResponse.success) {
        return this.parseLLMGems(llmResponse.content, request);
      }
    } catch (error) {
      console.warn('⚠️ LLM挖掘失败:', error);
    }

    return [];
  }

  /**
   * 📊 智能评估宝石
   */
  private async evaluateGems(gems: HiddenGem[], request: DiscoveryRequest): Promise<HiddenGem[]> {
    const evaluatedGems: HiddenGem[] = [];

    for (const gem of gems) {
      // 计算各项评分
      const uniquenessScore = this.calculateUniqueness(gem, request);
      const hiddenScore = this.calculateHiddenLevel(gem, request);
      const localScore = this.calculateLocalPopularity(gem, request);
      const accessibilityScore = this.calculateAccessibility(gem, request);

      // 综合评分
      const overallScore = (uniquenessScore + hiddenScore + localScore + accessibilityScore) / 4;

      if (overallScore >= 0.6) { // 质量阈值
        gem.uniqueness = uniquenessScore;
        gem.hiddenLevel = hiddenScore;
        gem.localPopularity = localScore;
        evaluatedGems.push(gem);
      }
    }

    return evaluatedGems.sort((a, b) => b.uniqueness - a.uniqueness);
  }

  /**
   * 🎯 个性化匹配
   */
  private matchUserPreferences(gems: HiddenGem[], request: DiscoveryRequest): HiddenGem[] {
    return gems.filter(gem => {
      // 兴趣匹配
      const interestMatch = this.checkInterestMatch(gem, request.userProfile.interests);
      if (!interestMatch) return false;

      // 冒险程度匹配
      const adventureMatch = this.checkAdventureMatch(gem, request.userProfile.adventureLevel);
      if (!adventureMatch) return false;

      // 隐藏程度匹配
      const hiddenMatch = Math.abs(gem.hiddenLevel - request.preferences.hiddenLevel) <= 0.3;
      if (!hiddenMatch) return false;

      // 距离约束
      const distanceOk = this.checkDistanceConstraint(gem, request.constraints.maxDistance);
      if (!distanceOk) return false;

      return true;
    }).slice(0, 20); // 最多返回20个匹配的宝石
  }

  /**
   * 🔧 初始化发现模式
   */
  private initializeDiscoveryPatterns(): void {
    const patterns = {
      'local-favorites': {
        indicators: ['当地人推荐', '老字号', '家族经营', '传统工艺'],
        weight: 0.9,
        hiddenLevel: 0.7
      },
      'off-beaten-path': {
        indicators: ['小巷', '隐秘', '鲜为人知', '需要寻找'],
        weight: 0.8,
        hiddenLevel: 0.9
      },
      'cultural-authentic': {
        indicators: ['传统', '文化', '历史', '手工'],
        weight: 0.85,
        hiddenLevel: 0.6
      },
      'nature-secret': {
        indicators: ['自然', '景观', '观景点', '徒步'],
        weight: 0.8,
        hiddenLevel: 0.8
      },
      'food-hidden': {
        indicators: ['美食', '小吃', '街边', '家常'],
        weight: 0.9,
        hiddenLevel: 0.7
      }
    };

    Object.entries(patterns).forEach(([type, pattern]) => {
      this.discoveryPatterns.set(type, pattern);
    });
  }

  private initializeLocalKnowledge(): void {
    const knowledge = {
      '东京': {
        hiddenGems: [
          {
            name: '神乐坂石畳小径',
            type: 'cultural',
            description: '隐藏在神乐坂的石板小径，保留着江户时代的风情',
            uniqueness: 0.9,
            hiddenLevel: 0.8,
            localTips: ['傍晚时分最美', '附近有传统料亭']
          },
          {
            name: '谷中银座商店街',
            type: 'cultural',
            description: '东京最后的下町风情，猫咪聚集的怀旧商店街',
            uniqueness: 0.85,
            hiddenLevel: 0.7,
            localTips: ['周末人较多', '有很多手工艺品店']
          }
        ],
        patterns: ['传统文化', '下町风情', '手工艺', '老字号'],
        secrets: ['当地人喜欢的小店往往没有英文招牌', '最好的体验在工作日']
      },
      '巴黎': {
        hiddenGems: [
          {
            name: 'Passage des Panoramas',
            type: 'cultural',
            description: '巴黎最古老的有顶商业街，隐藏着精美的19世纪建筑',
            uniqueness: 0.9,
            hiddenLevel: 0.8,
            localTips: ['有很多古董店', '建筑细节值得细看']
          }
        ],
        patterns: ['历史建筑', '艺术文化', '咖啡文化', '古董'],
        secrets: ['巴黎人喜欢的地方通常很安静', '最好的咖啡店在居民区']
      }
    };

    Object.entries(knowledge).forEach(([destination, data]) => {
      this.localKnowledge.set(destination, data);
    });
  }

  /**
   * 🔧 辅助方法
   */
  private mineFromLocalKnowledge(request: DiscoveryRequest): HiddenGem[] {
    const knowledge = this.localKnowledge.get(request.destination);
    if (!knowledge) return [];

    return knowledge.hiddenGems.map((gem: any, index: number) => ({
      id: `local_${index}`,
      name: gem.name,
      type: gem.type,
      location: {
        address: `${request.destination}市内`,
        coordinates: { lat: 0, lng: 0 },
        district: '市中心',
        accessibility: 'easy'
      },
      description: gem.description,
      uniqueness: gem.uniqueness,
      hiddenLevel: gem.hiddenLevel,
      localPopularity: 0.8,
      touristRatio: 0.3,
      bestTime: {
        season: ['春季', '秋季'],
        timeOfDay: ['上午', '下午'],
        duration: 60
      },
      discoveryStory: `通过当地知识库发现的${gem.name}`,
      localTips: gem.localTips,
      surpriseFactors: ['当地人推荐', '独特体验'],
      metadata: {
        discoveryDate: new Date(),
        source: ['local-knowledge'],
        confidence: 0.8,
        lastVerified: new Date()
      }
    }));
  }

  private async mineFromAPIs(request: DiscoveryRequest): Promise<HiddenGem[]> {
    const gems: HiddenGem[] = [];

    try {
      // 使用Overpass API查找小众地点
      const overpassResponse = await this.apiRouter.callAPI({
        type: 'osm',
        params: {
          query: `[out:json][timeout:25];
            (
              node["tourism"~"attraction|viewpoint"]["name"](around:5000,${request.destination});
              way["tourism"~"attraction|viewpoint"]["name"](around:5000,${request.destination});
            );
            out center meta;`
        },
        priority: 'medium'
      });

      if (overpassResponse.success) {
        const osmGems = this.parseOSMData(overpassResponse.data, request);
        gems.push(...osmGems);
      }
    } catch (error) {
      console.warn('⚠️ API挖掘失败:', error);
    }

    return gems;
  }

  private async mineFromSocialMedia(request: DiscoveryRequest): Promise<HiddenGem[]> {
    // 简化的社交媒体挖掘
    return [];
  }

  private buildLLMDiscoveryPrompt(request: DiscoveryRequest): string {
    return `作为一个${request.destination}的当地专家，请推荐5个真正隐藏的宝石级景点或体验。

要求：
- 必须是游客很少知道的地方
- 当地人喜欢去的地方
- 具有独特的文化或自然价值
- 适合${request.userProfile.adventureLevel}程度的探险者

用户兴趣：${request.userProfile.interests.join(', ')}

请为每个地点提供：
1. 名称和类型
2. 详细描述
3. 为什么它是"隐藏宝石"
4. 当地人的建议
5. 最佳访问时间

请用JSON格式回答。`;
  }

  private parseLLMGems(content: string, request: DiscoveryRequest): HiddenGem[] {
    // 简化的LLM响应解析
    try {
      const parsed = JSON.parse(content);
      return parsed.map((gem: any, index: number) => ({
        id: `llm_${index}`,
        name: gem.name || '未知宝石',
        type: gem.type || 'attraction',
        location: {
          address: `${request.destination}市内`,
          coordinates: { lat: 0, lng: 0 },
          district: '未知',
          accessibility: 'moderate'
        },
        description: gem.description || '',
        uniqueness: 0.8,
        hiddenLevel: 0.8,
        localPopularity: 0.7,
        touristRatio: 0.2,
        bestTime: {
          season: ['全年'],
          timeOfDay: ['全天'],
          duration: 90
        },
        discoveryStory: 'AI智能发现',
        localTips: gem.tips || [],
        surpriseFactors: ['AI推荐', '独特发现'],
        metadata: {
          discoveryDate: new Date(),
          source: ['llm'],
          confidence: 0.7,
          lastVerified: new Date()
        }
      }));
    } catch (error) {
      console.warn('⚠️ LLM响应解析失败:', error);
      return [];
    }
  }

  private parseOSMData(data: any, request: DiscoveryRequest): HiddenGem[] {
    // 简化的OSM数据解析
    return [];
  }

  private calculateUniqueness(gem: HiddenGem, request: DiscoveryRequest): number {
    let score = 0.6;
    
    // 基于描述关键词
    const uniqueKeywords = ['独特', '罕见', '特别', '唯一', '稀有'];
    const hasUniqueKeywords = uniqueKeywords.some(keyword => 
      gem.description.includes(keyword)
    );
    if (hasUniqueKeywords) score += 0.2;
    
    // 基于类型稀有度
    const rareTypes = ['viewpoint', 'cultural', 'experience'];
    if (rareTypes.includes(gem.type)) score += 0.1;
    
    return Math.min(1.0, score);
  }

  private calculateHiddenLevel(gem: HiddenGem, request: DiscoveryRequest): number {
    let score = 0.5;
    
    // 基于描述中的隐藏指标
    const hiddenKeywords = ['隐藏', '秘密', '鲜为人知', '小众', '当地人'];
    const hiddenCount = hiddenKeywords.filter(keyword => 
      gem.description.includes(keyword)
    ).length;
    
    score += hiddenCount * 0.1;
    
    return Math.min(1.0, score);
  }

  private calculateLocalPopularity(gem: HiddenGem, request: DiscoveryRequest): number {
    return 0.7; // 简化实现
  }

  private calculateAccessibility(gem: HiddenGem, request: DiscoveryRequest): number {
    const accessibilityScores = { easy: 1.0, moderate: 0.8, difficult: 0.6 };
    return accessibilityScores[gem.location.accessibility];
  }

  private checkInterestMatch(gem: HiddenGem, interests: string[]): boolean {
    const gemKeywords = gem.description.toLowerCase();
    return interests.some(interest => 
      gemKeywords.includes(interest.toLowerCase())
    );
  }

  private checkAdventureMatch(gem: HiddenGem, adventureLevel: string): boolean {
    const levelMapping = {
      conservative: ['easy'],
      moderate: ['easy', 'moderate'],
      adventurous: ['easy', 'moderate', 'difficult']
    };
    
    return levelMapping[adventureLevel].includes(gem.location.accessibility);
  }

  private checkDistanceConstraint(gem: HiddenGem, maxDistance: number): boolean {
    // 简化的距离检查
    return true;
  }

  private generateGemCombinations(gems: HiddenGem[], request: DiscoveryRequest): HiddenGemCombination[] {
    const combinations: HiddenGemCombination[] = [];
    
    // 生成主题组合
    const themes = ['文化探索', '自然发现', '美食之旅', '艺术体验'];
    
    themes.forEach((theme, index) => {
      const themeGems = gems.filter(gem => this.matchesTheme(gem, theme)).slice(0, 3);
      
      if (themeGems.length >= 2) {
        combinations.push({
          id: `combo_${index}`,
          name: `${theme}组合`,
          gems: themeGems,
          theme,
          totalDuration: themeGems.reduce((sum, gem) => sum + gem.bestTime.duration, 0),
          route: this.generateRoute(themeGems),
          synergy: this.calculateSynergy(themeGems),
          story: this.generateCombinationStory(themeGems, theme)
        });
      }
    });
    
    return combinations;
  }

  private matchesTheme(gem: HiddenGem, theme: string): boolean {
    const themeKeywords = {
      '文化探索': ['文化', '历史', '传统', '古'],
      '自然发现': ['自然', '景观', '公园', '山'],
      '美食之旅': ['美食', '餐厅', '小吃', '料理'],
      '艺术体验': ['艺术', '博物馆', '画廊', '创意']
    };
    
    const keywords = themeKeywords[theme as keyof typeof themeKeywords] || [];
    return keywords.some(keyword => gem.description.includes(keyword));
  }

  private generateRoute(gems: HiddenGem[]): string {
    return gems.map(gem => gem.name).join(' → ');
  }

  private calculateSynergy(gems: HiddenGem[]): number {
    // 简化的协同效应计算
    return 0.8;
  }

  private generateCombinationStory(gems: HiddenGem[], theme: string): string {
    return `探索${theme}的完美组合，从${gems[0].name}开始，体验${gems.length}个独特的隐藏宝石。`;
  }

  private async generateDiscoveryInsights(gems: HiddenGem[], request: DiscoveryRequest): Promise<any> {
    return {
      patterns: ['当地人偏爱的小众地点', '传统文化保存完好的区域'],
      trends: ['越来越多游客寻找真实体验', '社交媒体推动隐藏地点曝光'],
      secrets: ['最好的体验往往在工作日', '当地人推荐的地方通常没有英文标识']
    };
  }

  private calculateDiscoveryScore(gems: HiddenGem[]): number {
    if (gems.length === 0) return 0;
    return gems.reduce((sum, gem) => sum + gem.uniqueness, 0) / gems.length;
  }

  private calculateSurpriseIndex(gems: HiddenGem[], request: DiscoveryRequest): number {
    if (gems.length === 0) return 0;
    return gems.reduce((sum, gem) => sum + gem.hiddenLevel, 0) / gems.length;
  }

  private calculateLocalAuthenticity(gems: HiddenGem[]): number {
    if (gems.length === 0) return 0;
    return gems.reduce((sum, gem) => sum + gem.localPopularity, 0) / gems.length;
  }

  private deduplicateGems(gems: HiddenGem[]): HiddenGem[] {
    const seen = new Set<string>();
    return gems.filter(gem => {
      const key = gem.name.toLowerCase();
      if (seen.has(key)) return false;
      seen.add(key);
      return true;
    });
  }

  private generateCacheKey(request: DiscoveryRequest): string {
    return `hidden_gems_${request.destination}_${request.preferences.hiddenLevel}_${request.userProfile.adventureLevel}`;
  }

  private isCacheValid(result: DiscoveryResult): boolean {
    const hoursSinceDiscovery = (Date.now() - result.metadata.discoveryDate.getTime()) / (1000 * 60 * 60);
    return hoursSinceDiscovery < 24; // 24小时内有效
  }

  private async cacheDiscoveryResult(result: DiscoveryResult): Promise<void> {
    const cacheKey = `discovery_${result.destination}_${Date.now()}`;
    await this.cacheManager.set(cacheKey, result, 'hidden-gems', 24 * 60 * 60 * 1000); // 24小时
  }

  private generateFallbackResult(request: DiscoveryRequest, startTime: number): DiscoveryResult {
    return {
      destination: request.destination,
      hiddenGems: [],
      discoveryScore: 0.3,
      surpriseIndex: 0.2,
      localAuthenticity: 0.4,
      recommendations: {
        featured: [],
        alternatives: [],
        combinations: []
      },
      discoveryInsights: {
        patterns: ['数据获取失败'],
        trends: ['请稍后重试'],
        secrets: ['系统正在恢复中']
      },
      metadata: {
        discoveryDate: new Date(),
        totalSearched: 0,
        uniqueFinds: 0,
        processingTime: Date.now() - startTime
      }
    };
  }

  /**
   * 📊 获取发现统计
   */
  getDiscoveryStats() {
    const totalGems = Array.from(this.gemsDatabase.values()).reduce((sum, gems) => sum + gems.length, 0);
    
    return {
      totalDestinations: this.gemsDatabase.size,
      totalHiddenGems: totalGems,
      averageHiddenLevel: 0.75,
      discoveryPatterns: Array.from(this.discoveryPatterns.keys()),
      localKnowledgeCoverage: this.localKnowledge.size
    };
  }
}
