/**
 * 🎯 Ultra Think专用Journey适配器
 * 直接使用Ultra Think V3.0生成的journeyJSON数据，避免数据转换丢失
 * 绕过复杂的分组逻辑，保持数据完整性
 */

import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import UltraThinkDayCard from '../journey/UltraThinkDayCard';

interface UltraThinkJourneyAdapterProps {
  journeyJSON: any;
  onActivityPress?: (activity: any) => void;
}

export default function UltraThinkJourneyAdapter({
  journeyJSON,
  onActivityPress
}: UltraThinkJourneyAdapterProps) {
  console.log('🎯 UltraThinkJourneyAdapter 渲染开始');
  console.log('📊 JourneyJSON数据:', {
    hasPayload: !!journeyJSON?.payload,
    hasDayPlans: !!journeyJSON?.payload?.dayPlans,
    dayPlansCount: journeyJSON?.payload?.dayPlans?.length || 0,
    title: journeyJSON?.payload?.journey?.title || '未知标题'
  });

  // 🔍 数据验证
  if (!journeyJSON || !journeyJSON.payload || !journeyJSON.payload.dayPlans) {
    console.error('❌ UltraThinkJourneyAdapter: 无效的journeyJSON数据');
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>数据加载失败，请重试</Text>
      </View>
    );
  }

  const { payload } = journeyJSON;
  const { journey, dayPlans } = payload;

  // 🌤️ 天气信息生成
  const getWeatherInfo = (dayNumber: number): string => {
    const weatherOptions = [
      '🌤️ 晴朗 8°C',
      '☁️ 多云 6°C', 
      '🌧️ 小雨 5°C',
      '❄️ 微雪 2°C'
    ];
    return weatherOptions[(dayNumber - 1) % weatherOptions.length];
  };

  // 📅 日期显示生成
  const getDateDisplay = (dayNumber: number): string => {
    const baseDate = new Date(2025, 11, 14); // 2025年12月14日
    const targetDate = new Date(baseDate.getTime() + (dayNumber - 1) * 24 * 60 * 60 * 1000);
    return `12月${targetDate.getDate()}日`;
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* 📊 行程头部 */}
      <View style={styles.headerContainer}>
        <Text style={styles.headerTitle}>
          {journey?.title || '精彩之旅'}
        </Text>
        <Text style={styles.headerSubtitle}>
          {journey?.destination || '目的地'} • {dayPlans.length}天 • 2人
        </Text>
        <Text style={styles.headerDateRange}>
          {getDateDisplay(1)} - {getDateDisplay(dayPlans.length)}
        </Text>
        
        {/* 📈 行程统计 */}
        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>{dayPlans.length}</Text>
            <Text style={styles.statLabel}>天数</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>
              {dayPlans.reduce((total, day) => 
                total + (day.activities?.length || 0) + (day.meals?.length || 0), 0
              )}
            </Text>
            <Text style={styles.statLabel}>活动</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>
              RM{dayPlans.reduce((total, day) => 
                total + (day.summary?.totalCost?.amount || 0), 0
              )}
            </Text>
            <Text style={styles.statLabel}>预算</Text>
          </View>
        </View>
      </View>

      {/* 📅 Ultra Think专用日程卡片 */}
      {dayPlans.map((dayPlan, index) => {
        const dayNumber = index + 1;
        
        console.log(`🎯 渲染UltraThinkDayCard Day ${dayNumber}:`, {
          activities: dayPlan.activities?.length || 0,
          meals: dayPlan.meals?.length || 0,
          transportation: dayPlan.transportation?.length || 0,
          totalItems: (dayPlan.activities?.length || 0) + 
                     (dayPlan.meals?.length || 0) + 
                     (dayPlan.transportation?.length || 0)
        });

        return (
          <UltraThinkDayCard
            key={dayNumber}
            dayPlan={dayPlan}
            dayNumber={dayNumber}
            weather={getWeatherInfo(dayNumber)}
            date={getDateDisplay(dayNumber)}
            onActivityPress={onActivityPress}
          />
        );
      })}

      {/* 📊 行程总结 */}
      <View style={styles.summaryContainer}>
        <Text style={styles.summaryTitle}>🎯 行程亮点</Text>
        <Text style={styles.summaryText}>
          • 基于真实地点数据生成的高质量行程
        </Text>
        <Text style={styles.summaryText}>
          • LLM增强的生动活动描述
        </Text>
        <Text style={styles.summaryText}>
          • 智能时间分配和预算优化
        </Text>
        <Text style={styles.summaryText}>
          • 完整的交通和餐饮安排
        </Text>
        
        {journeyJSON.metadata && (
          <View style={styles.metadataContainer}>
            <Text style={styles.metadataText}>
              质量评分: {(journeyJSON.metadata.qualityScore * 100).toFixed(0)}%
            </Text>
            <Text style={styles.metadataText}>
              生成时间: {journeyJSON.metadata.processingTime}ms
            </Text>
            {journeyJSON.metadata.llmEnhanced && (
              <Text style={styles.metadataText}>✨ LLM内容增强</Text>
            )}
          </View>
        )}
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  headerContainer: {
    backgroundColor: '#fff',
    padding: 20,
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 4,
  },
  headerDateRange: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
    marginBottom: 16,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#007AFF',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
  },
  summaryContainer: {
    backgroundColor: '#fff',
    margin: 16,
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
  },
  summaryText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
    marginBottom: 4,
  },
  metadataContainer: {
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  metadataText: {
    fontSize: 12,
    color: '#999',
    marginBottom: 2,
  },
});
