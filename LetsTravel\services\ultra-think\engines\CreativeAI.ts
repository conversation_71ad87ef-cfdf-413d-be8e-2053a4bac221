/**
 * 🎨 Creative AI - 创意内容生成引擎
 * Phase 3: 实时内容生成引擎 - 创意内容生成
 * 确保每次生成独特高质量内容，避免重复和模板化
 */

import { UltraThinkLLMManager } from '../../ai/UltraThinkLLMManager';
import { UltraThinkCacheManager } from '../UltraThinkCacheManager';
import { UltraThinkModelStrategy } from '../UltraThinkModelStrategy';

// ===== 创意内容生成接口定义 =====

export interface CreativeRequest {
  type: 'activity-name' | 'description' | 'story' | 'recommendation' | 'insight' | 'experience';
  context: {
    destination: string;
    activity: string;
    season?: string;
    personality?: string;
    cultural?: string;
    mood?: string;
    style?: 'poetic' | 'casual' | 'professional' | 'humorous' | 'inspiring';
  };
  constraints: {
    length: 'short' | 'medium' | 'long';
    tone: 'formal' | 'casual' | 'enthusiastic' | 'mysterious' | 'warm';
    uniqueness: number; // 0-1, 独特性要求
    creativity: number; // 0-1, 创意性要求
  };
  avoidPatterns?: string[]; // 避免的模式或短语
  previousContent?: string[]; // 之前生成的内容，用于避免重复
}

export interface CreativeContent {
  id: string;
  type: string;
  content: string;
  title?: string;
  subtitle?: string;
  highlights: string[];
  uniquenessScore: number; // 0-1, 独特性评分
  creativityScore: number; // 0-1, 创意性评分
  qualityScore: number; // 0-1, 整体质量评分
  metadata: {
    generationDate: Date;
    model: string;
    prompt: string;
    iterations: number;
    processingTime: number;
    fingerprint: string; // 内容指纹，用于去重
  };
}

export interface ContentVariation {
  original: CreativeContent;
  variations: CreativeContent[];
  bestVariation: CreativeContent;
  diversityScore: number; // 变体多样性评分
  selectionReason: string;
}

export interface CreativeSession {
  sessionId: string;
  destination: string;
  generatedContent: CreativeContent[];
  contentFingerprints: Set<string>;
  creativityTrends: number[];
  qualityTrends: number[];
  sessionStats: {
    totalGenerated: number;
    uniqueContent: number;
    averageQuality: number;
    averageCreativity: number;
  };
}

// ===== Creative AI 核心类 =====

export class CreativeAI {
  private static instance: CreativeAI;
  private llmManager = UltraThinkLLMManager.getInstance();
  private cacheManager = UltraThinkCacheManager.getInstance();
  private modelStrategy = UltraThinkModelStrategy.getInstance();
  private creativeSessions = new Map<string, CreativeSession>();
  private contentDatabase = new Map<string, CreativeContent>();
  private creativityPatterns = new Map<string, any>();

  private constructor() {
    this.initializeCreativityPatterns();
    console.log('🎨 Creative AI 初始化完成');
  }

  static getInstance(): CreativeAI {
    if (!CreativeAI.instance) {
      CreativeAI.instance = new CreativeAI();
    }
    return CreativeAI.instance;
  }

  /**
   * 🚀 生成创意内容 - 主要入口方法
   */
  async generateCreativeContent(request: CreativeRequest, sessionId?: string): Promise<CreativeContent> {
    console.log(`🎨 开始创意内容生成: ${request.type} for ${request.context.destination}`);

    try {
      // 1. 获取或创建创意会话
      const session = this.getOrCreateSession(sessionId || this.generateSessionId(), request.context.destination);

      // 2. 检查内容唯一性要求
      const uniquenessCheck = this.checkUniquenessRequirement(request, session);

      // 3. 构建创意提示词
      const creativePrompt = this.buildCreativePrompt(request, session);
      console.log(`📝 创意提示词构建完成: ${creativePrompt.length}字符`);

      // 4. 选择最优模型
      const modelSelection = await this.modelStrategy.selectOptimalModel('creative-content', request.context);

      // 5. 生成多个变体
      const variations = await this.generateContentVariations(creativePrompt, modelSelection, request);
      console.log(`🎭 生成${variations.length}个内容变体`);

      // 6. 评估和选择最佳内容
      const bestContent = this.selectBestContent(variations, request, session);

      // 7. 后处理和优化
      const optimizedContent = await this.optimizeContent(bestContent, request);

      // 8. 更新会话状态
      this.updateCreativeSession(session, optimizedContent);

      // 9. 缓存内容
      await this.cacheCreativeContent(optimizedContent);

      console.log(`✅ 创意内容生成完成: 质量${optimizedContent.qualityScore.toFixed(2)}, 创意${optimizedContent.creativityScore.toFixed(2)}`);
      return optimizedContent;

    } catch (error) {
      console.error('❌ 创意内容生成失败:', error);
      return this.generateFallbackContent(request);
    }
  }

  /**
   * 🎭 生成内容变体
   */
  private async generateContentVariations(
    prompt: string, 
    modelSelection: any, 
    request: CreativeRequest
  ): Promise<CreativeContent[]> {
    const variations: CreativeContent[] = [];
    const variationCount = this.getVariationCount(request.constraints.creativity);

    for (let i = 0; i < variationCount; i++) {
      try {
        const variationPrompt = this.addVariationSeed(prompt, i);
        
        const llmResponse = await this.llmManager.callLLM({
          prompt: variationPrompt,
          taskType: 'creative-content',
          context: {
            destination: request.context.destination,
            complexity: 'high',
            creativity: request.constraints.creativity
          },
          forceModel: modelSelection.selectedModel
        });

        if (llmResponse.success) {
          const content = this.parseCreativeResponse(llmResponse.content, request, i);
          variations.push(content);
        }
      } catch (error) {
        console.warn(`⚠️ 变体${i}生成失败:`, error);
      }
    }

    return variations;
  }

  /**
   * 🏆 选择最佳内容
   */
  private selectBestContent(
    variations: CreativeContent[], 
    request: CreativeRequest, 
    session: CreativeSession
  ): CreativeContent {
    if (variations.length === 0) {
      throw new Error('没有可用的内容变体');
    }

    if (variations.length === 1) {
      return variations[0];
    }

    // 计算每个变体的综合评分
    const scoredVariations = variations.map(variation => ({
      content: variation,
      score: this.calculateContentScore(variation, request, session)
    }));

    // 按评分排序
    scoredVariations.sort((a, b) => b.score - a.score);

    const bestContent = scoredVariations[0].content;
    console.log(`🏆 选择最佳内容: 评分${scoredVariations[0].score.toFixed(2)}`);

    return bestContent;
  }

  /**
   * 📝 构建创意提示词
   */
  private buildCreativePrompt(request: CreativeRequest, session: CreativeSession): string {
    const basePrompt = this.getBasePromptForType(request.type);
    const contextPrompt = this.buildContextPrompt(request.context);
    const constraintsPrompt = this.buildConstraintsPrompt(request.constraints);
    const uniquenessPrompt = this.buildUniquenessPrompt(request, session);
    const creativityPrompt = this.buildCreativityPrompt(request.constraints.creativity);

    return [
      basePrompt,
      contextPrompt,
      constraintsPrompt,
      uniquenessPrompt,
      creativityPrompt,
      '\n请生成独特、有创意且高质量的内容。'
    ].join('\n\n');
  }

  /**
   * 🔧 初始化创意模式
   */
  private initializeCreativityPatterns(): void {
    const patterns = {
      'activity-name': {
        templates: [
          '{adjective} + {noun} + {experience}',
          '{cultural} + {activity} + {emotion}',
          '{time} + {place} + {discovery}'
        ],
        adjectives: ['神秘', '浪漫', '古老', '现代', '传统', '独特', '隐秘', '壮观'],
        emotions: ['体验', '探索', '发现', '感受', '领悟', '沉浸', '邂逅', '追寻'],
        styles: ['诗意', '文艺', '深度', '轻松', '奢华', '简约', '野性', '优雅']
      },
      'description': {
        structures: [
          'sensory + emotional + cultural',
          'historical + personal + future',
          'local + universal + unique'
        ],
        sensoryWords: ['视觉', '听觉', '触觉', '嗅觉', '味觉'],
        emotionalWords: ['温暖', '激动', '平静', '好奇', '敬畏', '喜悦'],
        culturalWords: ['传统', '现代', '融合', '对比', '传承', '创新']
      },
      'story': {
        narratives: ['journey', 'discovery', 'transformation', 'connection', 'revelation'],
        perspectives: ['first-person', 'observer', 'local-guide', 'time-traveler'],
        themes: ['culture-clash', 'hidden-beauty', 'personal-growth', 'serendipity']
      }
    };

    Object.entries(patterns).forEach(([type, pattern]) => {
      this.creativityPatterns.set(type, pattern);
    });
  }

  /**
   * 🔧 辅助方法
   */
  private getOrCreateSession(sessionId: string, destination: string): CreativeSession {
    if (!this.creativeSessions.has(sessionId)) {
      this.creativeSessions.set(sessionId, {
        sessionId,
        destination,
        generatedContent: [],
        contentFingerprints: new Set(),
        creativityTrends: [],
        qualityTrends: [],
        sessionStats: {
          totalGenerated: 0,
          uniqueContent: 0,
          averageQuality: 0,
          averageCreativity: 0
        }
      });
    }
    return this.creativeSessions.get(sessionId)!;
  }

  private generateSessionId(): string {
    return `creative_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private checkUniquenessRequirement(request: CreativeRequest, session: CreativeSession): boolean {
    if (request.constraints.uniqueness < 0.5) return true;

    // 检查是否与之前的内容过于相似
    const similarContent = session.generatedContent.filter(content => 
      this.calculateSimilarity(content.content, request.context.activity) > 0.7
    );

    return similarContent.length < 3; // 允许最多3个相似内容
  }

  private getVariationCount(creativity: number): number {
    if (creativity >= 0.8) return 5;
    if (creativity >= 0.6) return 3;
    return 2;
  }

  private addVariationSeed(prompt: string, index: number): string {
    const seeds = [
      '以独特的视角',
      '用诗意的语言',
      '从当地人的角度',
      '以故事的形式',
      '用感性的描述'
    ];
    
    const seed = seeds[index % seeds.length];
    return `${seed}，${prompt}`;
  }

  private parseCreativeResponse(content: string, request: CreativeRequest, index: number): CreativeContent {
    const contentId = `creative_${Date.now()}_${index}`;
    const fingerprint = this.generateContentFingerprint(content);

    return {
      id: contentId,
      type: request.type,
      content: content.trim(),
      highlights: this.extractHighlights(content),
      uniquenessScore: this.calculateUniquenessScore(content, request),
      creativityScore: this.calculateCreativityScore(content, request),
      qualityScore: this.calculateQualityScore(content, request),
      metadata: {
        generationDate: new Date(),
        model: 'creative-model',
        prompt: request.context.activity,
        iterations: 1,
        processingTime: 0,
        fingerprint
      }
    };
  }

  private calculateContentScore(content: CreativeContent, request: CreativeRequest, session: CreativeSession): number {
    const weights = {
      quality: 0.4,
      creativity: 0.3,
      uniqueness: 0.2,
      relevance: 0.1
    };

    const relevanceScore = this.calculateRelevanceScore(content, request);
    
    return (
      content.qualityScore * weights.quality +
      content.creativityScore * weights.creativity +
      content.uniquenessScore * weights.uniqueness +
      relevanceScore * weights.relevance
    );
  }

  private getBasePromptForType(type: string): string {
    const prompts = {
      'activity-name': '为以下旅行活动创造一个吸引人且独特的名称',
      'description': '为以下旅行体验写一段生动且富有感染力的描述',
      'story': '围绕以下旅行场景创作一个引人入胜的故事',
      'recommendation': '为以下旅行选择提供专业且个性化的推荐',
      'insight': '分享关于以下旅行体验的深刻见解和文化内涵',
      'experience': '描述以下旅行活动将带来的独特体验和感受'
    };

    return prompts[type] || prompts['description'];
  }

  private buildContextPrompt(context: any): string {
    const parts = [
      `目的地: ${context.destination}`,
      `活动: ${context.activity}`
    ];

    if (context.season) parts.push(`季节: ${context.season}`);
    if (context.personality) parts.push(`个性特征: ${context.personality}`);
    if (context.cultural) parts.push(`文化背景: ${context.cultural}`);
    if (context.mood) parts.push(`氛围: ${context.mood}`);
    if (context.style) parts.push(`风格: ${context.style}`);

    return `背景信息:\n${parts.join('\n')}`;
  }

  private buildConstraintsPrompt(constraints: any): string {
    const parts = [
      `长度要求: ${constraints.length}`,
      `语调风格: ${constraints.tone}`,
      `独特性要求: ${(constraints.uniqueness * 100).toFixed(0)}%`,
      `创意性要求: ${(constraints.creativity * 100).toFixed(0)}%`
    ];

    return `要求:\n${parts.join('\n')}`;
  }

  private buildUniquenessPrompt(request: CreativeRequest, session: CreativeSession): string {
    if (session.generatedContent.length === 0) return '';

    const recentContent = session.generatedContent.slice(-3).map(c => c.content.substring(0, 50));
    return `请避免与以下内容相似:\n${recentContent.join('\n')}`;
  }

  private buildCreativityPrompt(creativity: number): string {
    if (creativity >= 0.8) {
      return '请发挥最大创意，使用独特的比喻、新颖的视角和富有想象力的表达。';
    } else if (creativity >= 0.6) {
      return '请在保持准确性的同时，加入创意元素和个性化表达。';
    } else {
      return '请提供准确、实用且略带创意的内容。';
    }
  }

  private extractHighlights(content: string): string[] {
    // 简化的亮点提取
    const sentences = content.split(/[。！？]/).filter(s => s.trim().length > 10);
    return sentences.slice(0, 3).map(s => s.trim());
  }

  private calculateUniquenessScore(content: string, request: CreativeRequest): number {
    // 简化的独特性评分
    const commonPhrases = ['美丽的', '著名的', '值得一去', '不容错过'];
    const hasCommonPhrases = commonPhrases.some(phrase => content.includes(phrase));
    
    let score = 0.7;
    if (!hasCommonPhrases) score += 0.2;
    if (content.length > 100) score += 0.1;
    
    return Math.min(1.0, score);
  }

  private calculateCreativityScore(content: string, request: CreativeRequest): number {
    // 简化的创意性评分
    const creativeWords = ['邂逅', '沉浸', '穿越', '探寻', '领悟', '感悟'];
    const creativeCount = creativeWords.filter(word => content.includes(word)).length;
    
    return Math.min(1.0, 0.5 + (creativeCount * 0.1));
  }

  private calculateQualityScore(content: string, request: CreativeRequest): number {
    // 简化的质量评分
    let score = 0.6;
    
    if (content.length >= 50) score += 0.1;
    if (content.length >= 100) score += 0.1;
    if (!/[。！？]$/.test(content.trim())) score -= 0.1;
    
    return Math.max(0.3, Math.min(1.0, score));
  }

  private calculateRelevanceScore(content: CreativeContent, request: CreativeRequest): number {
    const destination = request.context.destination.toLowerCase();
    const activity = request.context.activity.toLowerCase();
    const contentLower = content.content.toLowerCase();
    
    let score = 0.5;
    if (contentLower.includes(destination)) score += 0.3;
    if (contentLower.includes(activity)) score += 0.2;
    
    return Math.min(1.0, score);
  }

  private calculateSimilarity(content1: string, content2: string): number {
    // 简化的相似度计算
    const words1 = content1.toLowerCase().split(/\s+/);
    const words2 = content2.toLowerCase().split(/\s+/);
    
    const commonWords = words1.filter(word => words2.includes(word));
    const totalWords = new Set([...words1, ...words2]).size;
    
    return commonWords.length / totalWords;
  }

  private generateContentFingerprint(content: string): string {
    // 简化的内容指纹生成
    return btoa(content.substring(0, 100)).substring(0, 16);
  }

  private async optimizeContent(content: CreativeContent, request: CreativeRequest): Promise<CreativeContent> {
    // 内容优化处理
    const optimized = { ...content };
    
    // 如果质量分数较低，尝试优化
    if (content.qualityScore < 0.7) {
      optimized.content = this.improveContentQuality(content.content);
      optimized.qualityScore = Math.min(1.0, content.qualityScore + 0.1);
    }
    
    return optimized;
  }

  private improveContentQuality(content: string): string {
    // 简化的内容质量改进
    let improved = content.trim();
    
    // 确保以标点符号结尾
    if (!/[。！？]$/.test(improved)) {
      improved += '。';
    }
    
    return improved;
  }

  private updateCreativeSession(session: CreativeSession, content: CreativeContent): void {
    session.generatedContent.push(content);
    session.contentFingerprints.add(content.metadata.fingerprint);
    session.creativityTrends.push(content.creativityScore);
    session.qualityTrends.push(content.qualityScore);
    
    // 更新统计信息
    session.sessionStats.totalGenerated++;
    session.sessionStats.uniqueContent = session.contentFingerprints.size;
    session.sessionStats.averageQuality = 
      session.qualityTrends.reduce((sum, score) => sum + score, 0) / session.qualityTrends.length;
    session.sessionStats.averageCreativity = 
      session.creativityTrends.reduce((sum, score) => sum + score, 0) / session.creativityTrends.length;
  }

  private async cacheCreativeContent(content: CreativeContent): Promise<void> {
    const cacheKey = `creative_${content.type}_${content.metadata.fingerprint}`;
    await this.cacheManager.set(cacheKey, content, 'creative-content', 24 * 60 * 60 * 1000); // 24小时
    
    this.contentDatabase.set(content.id, content);
  }

  private generateFallbackContent(request: CreativeRequest): CreativeContent {
    const fallbackContent = `${request.context.destination}的${request.context.activity}体验`;
    
    return {
      id: `fallback_${Date.now()}`,
      type: request.type,
      content: fallbackContent,
      highlights: [fallbackContent],
      uniquenessScore: 0.3,
      creativityScore: 0.2,
      qualityScore: 0.4,
      metadata: {
        generationDate: new Date(),
        model: 'fallback',
        prompt: 'fallback',
        iterations: 0,
        processingTime: 0,
        fingerprint: 'fallback'
      }
    };
  }

  /**
   * 📊 获取创意统计
   */
  getCreativeStats(sessionId?: string) {
    if (sessionId && this.creativeSessions.has(sessionId)) {
      return this.creativeSessions.get(sessionId)!.sessionStats;
    }
    
    const allSessions = Array.from(this.creativeSessions.values());
    const totalContent = allSessions.reduce((sum, session) => sum + session.sessionStats.totalGenerated, 0);
    const avgQuality = allSessions.length > 0 
      ? allSessions.reduce((sum, session) => sum + session.sessionStats.averageQuality, 0) / allSessions.length 
      : 0;
    
    return {
      totalSessions: allSessions.length,
      totalContent,
      averageQuality: avgQuality,
      uniqueContentRatio: totalContent > 0 ? this.contentDatabase.size / totalContent : 0
    };
  }
}
