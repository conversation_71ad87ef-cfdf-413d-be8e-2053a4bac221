/**
 * 🎭 Festival Core - 文化节日引擎
 * Phase 2: 智能偏好引擎构建 - 文化节日智能集成
 * 集成当地文化活动和传统节日
 */

import { UltraThinkLLMManager } from '../../ai/UltraThinkLLMManager';
import { UltraThinkCacheManager } from '../UltraThinkCacheManager';
import { UltraThinkAPIRouter } from '../UltraThinkAPIRouter';

// ===== 文化节日接口定义 =====

export interface Festival {
  id: string;
  name: string;
  type: 'traditional' | 'religious' | 'cultural' | 'seasonal' | 'modern' | 'food' | 'music' | 'art';
  location: {
    country: string;
    city: string;
    venue?: string;
    coordinates?: { lat: number; lng: number };
  };
  dates: {
    start: Date;
    end: Date;
    recurring: 'annual' | 'monthly' | 'seasonal' | 'irregular';
    nextOccurrence?: Date;
  };
  description: string;
  significance: string;
  activities: string[];
  culturalValue: number; // 0-1, 文化价值评分
  touristFriendly: number; // 0-1, 游客友好度
  crowdLevel: 'low' | 'medium' | 'high' | 'extreme';
  costLevel: 'free' | 'low' | 'medium' | 'high';
  recommendations: {
    bestTimeToVisit: string;
    duration: number; // 建议参与时长(小时)
    preparation: string[];
    whatToBring: string[];
    culturalEtiquette: string[];
  };
}

export interface CulturalContext {
  destination: string;
  travelDates: { start: Date; end: Date };
  culturalInterests: string[];
  experienceLevel: 'beginner' | 'intermediate' | 'expert';
  groupType: 'solo' | 'couple' | 'family' | 'group';
  preferences: {
    crowdTolerance: 'low' | 'medium' | 'high';
    budgetRange: 'budget' | 'mid-range' | 'luxury';
    authenticityLevel: 'tourist-friendly' | 'authentic' | 'local-only';
  };
}

export interface FestivalRecommendation {
  festival: Festival;
  relevanceScore: number; // 0-1, 相关性评分
  matchReasons: string[];
  participationSuggestions: {
    activities: string[];
    timeSlots: string[];
    preparationTips: string[];
    culturalInsights: string[];
  };
  alternatives: Festival[];
  warnings?: string[];
}

export interface CulturalCalendar {
  destination: string;
  period: { start: Date; end: Date };
  festivals: Festival[];
  culturalHighlights: string[];
  seasonalTraditions: string[];
  localCustoms: string[];
  recommendations: FestivalRecommendation[];
  metadata: {
    dataSource: string[];
    lastUpdated: Date;
    confidence: number;
  };
}

// ===== Festival Core 核心类 =====

export class FestivalCore {
  private static instance: FestivalCore;
  private llmManager = UltraThinkLLMManager.getInstance();
  private cacheManager = UltraThinkCacheManager.getInstance();
  private apiRouter = UltraThinkAPIRouter.getInstance();
  private festivalDatabase = new Map<string, Festival[]>();
  private culturalKnowledge = new Map<string, any>();

  private constructor() {
    this.initializeFestivalDatabase();
    this.initializeCulturalKnowledge();
    console.log('🎭 Festival Core 初始化完成');
  }

  static getInstance(): FestivalCore {
    if (!FestivalCore.instance) {
      FestivalCore.instance = new FestivalCore();
    }
    return FestivalCore.instance;
  }

  /**
   * 🎪 文化节日分析 - 主要入口方法
   */
  async analyzeCulturalContext(context: CulturalContext): Promise<CulturalCalendar> {
    console.log(`🎭 开始文化节日分析: ${context.destination}`);

    try {
      // 1. 检查缓存
      const cacheKey = this.generateCacheKey(context);
      const cached = await this.cacheManager.get<CulturalCalendar>(cacheKey, 'cultural-calendar');
      
      if (cached && this.isCacheValid(cached)) {
        console.log('✅ 文化节日分析缓存命中');
        return cached;
      }

      // 2. 获取目的地节日数据
      const festivals = await this.getFestivalsForDestination(context);
      console.log(`📅 发现${festivals.length}个相关节日`);

      // 3. 筛选时间范围内的节日
      const relevantFestivals = this.filterFestivalsByDate(festivals, context.travelDates);
      console.log(`🎯 时间范围内节日: ${relevantFestivals.length}个`);

      // 4. 生成节日推荐
      const recommendations = await this.generateFestivalRecommendations(relevantFestivals, context);

      // 5. 识别文化亮点
      const culturalHighlights = await this.identifyCulturalHighlights(context);

      // 6. 分析季节性传统
      const seasonalTraditions = this.analyzeSeasonalTraditions(context);

      // 7. 获取当地习俗
      const localCustoms = await this.getLocalCustoms(context);

      // 8. 构建文化日历
      const calendar: CulturalCalendar = {
        destination: context.destination,
        period: context.travelDates,
        festivals: relevantFestivals,
        culturalHighlights,
        seasonalTraditions,
        localCustoms,
        recommendations,
        metadata: {
          dataSource: ['festival-database', 'cultural-knowledge', 'api-data'],
          lastUpdated: new Date(),
          confidence: this.calculateConfidence(relevantFestivals, recommendations)
        }
      };

      // 9. 缓存结果
      await this.cacheManager.set(cacheKey, calendar, 'cultural-calendar', 12 * 60 * 60 * 1000); // 12小时

      console.log(`✅ 文化节日分析完成: ${recommendations.length}个推荐`);
      return calendar;

    } catch (error) {
      console.error('❌ 文化节日分析失败:', error);
      return this.generateFallbackCalendar(context);
    }
  }

  /**
   * 📅 获取目的地节日数据
   */
  private async getFestivalsForDestination(context: CulturalContext): Promise<Festival[]> {
    const festivals: Festival[] = [];

    // 1. 从本地数据库获取
    const localFestivals = this.festivalDatabase.get(context.destination) || [];
    festivals.push(...localFestivals);

    // 2. 从API获取实时数据
    try {
      const apiFestivals = await this.fetchFestivalsFromAPI(context);
      festivals.push(...apiFestivals);
    } catch (error) {
      console.warn('⚠️ API获取节日数据失败:', error);
    }

    // 3. 使用LLM增强节日信息
    const enhancedFestivals = await this.enhanceFestivalsWithLLM(festivals, context);

    return this.deduplicateFestivals(enhancedFestivals);
  }

  /**
   * 🌐 从API获取节日数据
   */
  private async fetchFestivalsFromAPI(context: CulturalContext): Promise<Festival[]> {
    try {
      // 使用Wikipedia API获取文化信息
      const wikiResponse = await this.apiRouter.callAPI({
        type: 'search',
        params: {
          q: `${context.destination} festivals traditions culture`,
          limit: 10
        },
        priority: 'medium'
      });

      if (wikiResponse.success) {
        return this.parseWikipediaFestivals(wikiResponse.data, context);
      }
    } catch (error) {
      console.warn('⚠️ Wikipedia API调用失败:', error);
    }

    return [];
  }

  /**
   * 🤖 使用LLM增强节日信息
   */
  private async enhanceFestivalsWithLLM(festivals: Festival[], context: CulturalContext): Promise<Festival[]> {
    const enhancedFestivals: Festival[] = [];

    for (const festival of festivals) {
      try {
        const prompt = `为${context.destination}的${festival.name}节日提供详细的文化背景、参与建议和游客须知。`;
        
        const llmResponse = await this.llmManager.callLLM({
          prompt,
          taskType: 'cultural-analysis',
          context: {
            destination: context.destination,
            complexity: 'high'
          }
        });

        if (llmResponse.success) {
          const enhanced = this.enhanceFestivalWithLLMData(festival, llmResponse.content);
          enhancedFestivals.push(enhanced);
        } else {
          enhancedFestivals.push(festival);
        }
      } catch (error) {
        console.warn(`⚠️ LLM增强失败: ${festival.name}`, error);
        enhancedFestivals.push(festival);
      }
    }

    return enhancedFestivals;
  }

  /**
   * 🎯 生成节日推荐
   */
  private async generateFestivalRecommendations(
    festivals: Festival[], 
    context: CulturalContext
  ): Promise<FestivalRecommendation[]> {
    const recommendations: FestivalRecommendation[] = [];

    for (const festival of festivals) {
      const relevanceScore = this.calculateRelevanceScore(festival, context);
      
      if (relevanceScore > 0.3) { // 只推荐相关性较高的节日
        const recommendation: FestivalRecommendation = {
          festival,
          relevanceScore,
          matchReasons: this.generateMatchReasons(festival, context),
          participationSuggestions: await this.generateParticipationSuggestions(festival, context),
          alternatives: this.findAlternativeFestivals(festival, festivals),
          warnings: this.generateWarnings(festival, context)
        };

        recommendations.push(recommendation);
      }
    }

    // 按相关性排序
    return recommendations.sort((a, b) => b.relevanceScore - a.relevanceScore);
  }

  /**
   * 🔧 初始化节日数据库
   */
  private initializeFestivalDatabase(): void {
    const sampleFestivals = {
      '东京': [
        {
          id: 'tokyo_cherry_blossom',
          name: '樱花祭',
          type: 'seasonal' as const,
          location: { country: '日本', city: '东京' },
          dates: {
            start: new Date('2024-03-20'),
            end: new Date('2024-05-10'),
            recurring: 'annual' as const
          },
          description: '东京最著名的春季庆典，庆祝樱花盛开',
          significance: '象征着春天的到来和生命的美好',
          activities: ['赏樱', '野餐', '传统表演', '摄影'],
          culturalValue: 0.95,
          touristFriendly: 0.9,
          crowdLevel: 'high' as const,
          costLevel: 'free' as const,
          recommendations: {
            bestTimeToVisit: '早晨或傍晚',
            duration: 3,
            preparation: ['提前查看花期预报', '准备野餐用品'],
            whatToBring: ['相机', '野餐垫', '保暖衣物'],
            culturalEtiquette: ['不要攀折樱花', '保持环境整洁', '尊重当地习俗']
          }
        }
      ],
      '巴黎': [
        {
          id: 'paris_bastille_day',
          name: '巴士底日',
          type: 'traditional' as const,
          location: { country: '法国', city: '巴黎' },
          dates: {
            start: new Date('2024-07-14'),
            end: new Date('2024-07-14'),
            recurring: 'annual' as const
          },
          description: '法国国庆日，纪念法国大革命',
          significance: '法国民主和自由的象征',
          activities: ['阅兵仪式', '烟花表演', '街头庆祝', '音乐会'],
          culturalValue: 0.9,
          touristFriendly: 0.85,
          crowdLevel: 'extreme' as const,
          costLevel: 'free' as const,
          recommendations: {
            bestTimeToVisit: '全天',
            duration: 8,
            preparation: ['提前占位观看阅兵', '了解历史背景'],
            whatToBring: ['法国国旗', '舒适鞋子', '充电宝'],
            culturalEtiquette: ['尊重国歌演奏', '参与集体庆祝', '注意安全']
          }
        }
      ]
    };

    Object.entries(sampleFestivals).forEach(([destination, festivals]) => {
      this.festivalDatabase.set(destination, festivals);
    });

    console.log(`🔧 初始化节日数据库: ${Object.keys(sampleFestivals).length}个目的地`);
  }

  /**
   * 🔧 初始化文化知识库
   */
  private initializeCulturalKnowledge(): void {
    const knowledge = {
      '东京': {
        culturalHighlights: ['传统茶道', '和服体验', '寺庙参拜', '相扑观赏'],
        seasonalTraditions: {
          spring: ['赏樱', '春季祭典'],
          summer: ['夏日祭', '烟花大会'],
          autumn: ['红叶狩', '秋季祭典'],
          winter: ['新年参拜', '梅花观赏']
        },
        localCustoms: ['脱鞋进屋', '鞠躬礼仪', '安静乘车', '垃圾分类']
      },
      '巴黎': {
        culturalHighlights: ['艺术博物馆', '咖啡文化', '时装周', '街头艺术'],
        seasonalTraditions: {
          spring: ['花园开放', '艺术节'],
          summer: ['户外音乐会', '塞纳河畔活动'],
          autumn: ['艺术展览', '美食节'],
          winter: ['圣诞市集', '新年庆典']
        },
        localCustoms: ['问候礼仪', '用餐礼节', '艺术鉴赏', '时尚品味']
      }
    };

    Object.entries(knowledge).forEach(([destination, data]) => {
      this.culturalKnowledge.set(destination, data);
    });
  }

  /**
   * 🔧 辅助方法
   */
  private filterFestivalsByDate(festivals: Festival[], dateRange: { start: Date; end: Date }): Festival[] {
    return festivals.filter(festival => {
      const festivalStart = festival.dates.start;
      const festivalEnd = festival.dates.end;
      
      // 检查日期重叠
      return (festivalStart <= dateRange.end && festivalEnd >= dateRange.start);
    });
  }

  private calculateRelevanceScore(festival: Festival, context: CulturalContext): number {
    let score = 0.5; // 基础分数

    // 文化兴趣匹配
    const interestMatch = context.culturalInterests.some(interest => 
      festival.activities.some(activity => 
        activity.toLowerCase().includes(interest.toLowerCase())
      )
    );
    if (interestMatch) score += 0.3;

    // 游客友好度
    score += festival.touristFriendly * 0.2;

    // 人群容忍度匹配
    const crowdMatch = this.matchCrowdPreference(festival.crowdLevel, context.preferences.crowdTolerance);
    score += crowdMatch * 0.2;

    // 预算匹配
    const budgetMatch = this.matchBudgetPreference(festival.costLevel, context.preferences.budgetRange);
    score += budgetMatch * 0.1;

    // 文化价值
    score += festival.culturalValue * 0.2;

    return Math.min(1.0, score);
  }

  private matchCrowdPreference(festivalCrowd: string, userTolerance: string): number {
    const crowdLevels = { low: 1, medium: 2, high: 3, extreme: 4 };
    const toleranceLevels = { low: 1, medium: 2, high: 3 };
    
    const festivalLevel = crowdLevels[festivalCrowd as keyof typeof crowdLevels];
    const userLevel = toleranceLevels[userTolerance as keyof typeof toleranceLevels];
    
    if (festivalLevel <= userLevel) return 1.0;
    if (festivalLevel === userLevel + 1) return 0.7;
    return 0.3;
  }

  private matchBudgetPreference(festivalCost: string, userBudget: string): number {
    const costLevels = { free: 0, low: 1, medium: 2, high: 3 };
    const budgetLevels = { budget: 1, 'mid-range': 2, luxury: 3 };
    
    const festivalLevel = costLevels[festivalCost as keyof typeof costLevels];
    const userLevel = budgetLevels[userBudget as keyof typeof budgetLevels];
    
    if (festivalLevel <= userLevel) return 1.0;
    return 0.5;
  }

  private generateMatchReasons(festival: Festival, context: CulturalContext): string[] {
    const reasons: string[] = [];
    
    if (festival.touristFriendly > 0.8) {
      reasons.push('对游客非常友好');
    }
    
    if (festival.culturalValue > 0.8) {
      reasons.push('具有重要文化价值');
    }
    
    const interestMatch = context.culturalInterests.some(interest => 
      festival.activities.some(activity => 
        activity.toLowerCase().includes(interest.toLowerCase())
      )
    );
    if (interestMatch) {
      reasons.push('符合您的文化兴趣');
    }
    
    return reasons;
  }

  private async generateParticipationSuggestions(festival: Festival, context: CulturalContext): Promise<any> {
    return {
      activities: festival.activities.slice(0, 3),
      timeSlots: [festival.recommendations.bestTimeToVisit],
      preparationTips: festival.recommendations.preparation,
      culturalInsights: [`了解${festival.significance}`]
    };
  }

  private findAlternativeFestivals(festival: Festival, allFestivals: Festival[]): Festival[] {
    return allFestivals
      .filter(f => f.id !== festival.id && f.type === festival.type)
      .slice(0, 2);
  }

  private generateWarnings(festival: Festival, context: CulturalContext): string[] | undefined {
    const warnings: string[] = [];
    
    if (festival.crowdLevel === 'extreme') {
      warnings.push('人群极其拥挤，建议提前规划路线');
    }
    
    if (festival.costLevel === 'high' && context.preferences.budgetRange === 'budget') {
      warnings.push('费用较高，请提前准备预算');
    }
    
    return warnings.length > 0 ? warnings : undefined;
  }

  private async identifyCulturalHighlights(context: CulturalContext): Promise<string[]> {
    const knowledge = this.culturalKnowledge.get(context.destination);
    return knowledge?.culturalHighlights || ['当地文化体验'];
  }

  private analyzeSeasonalTraditions(context: CulturalContext): string[] {
    const knowledge = this.culturalKnowledge.get(context.destination);
    const season = this.getCurrentSeason(context.travelDates.start);
    
    return knowledge?.seasonalTraditions?.[season] || [];
  }

  private async getLocalCustoms(context: CulturalContext): Promise<string[]> {
    const knowledge = this.culturalKnowledge.get(context.destination);
    return knowledge?.localCustoms || ['尊重当地习俗'];
  }

  private getCurrentSeason(date: Date): string {
    const month = date.getMonth() + 1;
    if (month >= 3 && month <= 5) return 'spring';
    if (month >= 6 && month <= 8) return 'summer';
    if (month >= 9 && month <= 11) return 'autumn';
    return 'winter';
  }

  private calculateConfidence(festivals: Festival[], recommendations: FestivalRecommendation[]): number {
    let confidence = 0.6; // 基础置信度
    
    if (festivals.length > 0) confidence += 0.2;
    if (recommendations.length > 0) confidence += 0.2;
    
    return Math.min(1.0, confidence);
  }

  private generateCacheKey(context: CulturalContext): string {
    return `festival_${context.destination}_${context.travelDates.start.toISOString().split('T')[0]}_${context.travelDates.end.toISOString().split('T')[0]}`;
  }

  private isCacheValid(calendar: CulturalCalendar): boolean {
    const hoursSinceUpdate = (Date.now() - calendar.metadata.lastUpdated.getTime()) / (1000 * 60 * 60);
    return hoursSinceUpdate < 12; // 12小时内有效
  }

  private parseWikipediaFestivals(data: any, context: CulturalContext): Festival[] {
    // 简化的Wikipedia数据解析
    return [];
  }

  private enhanceFestivalWithLLMData(festival: Festival, llmContent: string): Festival {
    // 简化的LLM数据增强
    const enhanced = { ...festival };
    enhanced.description = llmContent.substring(0, 200) + '...';
    return enhanced;
  }

  private deduplicateFestivals(festivals: Festival[]): Festival[] {
    const seen = new Set<string>();
    return festivals.filter(festival => {
      if (seen.has(festival.id)) return false;
      seen.add(festival.id);
      return true;
    });
  }

  private generateFallbackCalendar(context: CulturalContext): CulturalCalendar {
    return {
      destination: context.destination,
      period: context.travelDates,
      festivals: [],
      culturalHighlights: ['当地文化体验'],
      seasonalTraditions: ['季节性传统活动'],
      localCustoms: ['尊重当地习俗'],
      recommendations: [],
      metadata: {
        dataSource: ['fallback'],
        lastUpdated: new Date(),
        confidence: 0.5
      }
    };
  }
}
