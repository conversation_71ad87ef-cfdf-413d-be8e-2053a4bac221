/**
 * Trekmate 4.0 - 高级日志系统
 * 提供全面的日志记录、错误处理和分析功能
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';

// ============================================================================
// 日志系统配置
// ============================================================================

interface LoggingConfig {
  // 日志级别
  logLevel: LogLevel;
  enableConsoleLogging: boolean;
  enableFileLogging: boolean;
  enableRemoteLogging: boolean;
  enablePerformanceLogging: boolean;
  
  // 存储配置
  maxLogFiles: number;
  maxLogFileSize: number; // bytes
  logRetentionDays: number;
  
  // 远程日志配置
  remoteEndpoint?: string;
  batchSize: number;
  uploadInterval: number; // ms
  
  // 过滤配置
  sensitiveFields: string[];
  excludePatterns: RegExp[];
  
  // 性能配置
  enableStackTrace: boolean;
  enableSourceMap: boolean;
  maxStackDepth: number;
}

enum LogLevel {
  TRACE = 0,
  DEBUG = 1,
  INFO = 2,
  WARN = 3,
  ERROR = 4,
  FATAL = 5,
}

interface LogEntry {
  id: string;
  timestamp: number;
  level: LogLevel;
  category: string;
  message: string;
  data?: any;
  stack?: string;
  context: LogContext;
  tags: string[];
  sessionId: string;
  userId?: string;
}

interface LogContext {
  screen?: string;
  component?: string;
  function?: string;
  userAction?: string;
  deviceInfo: DeviceInfo;
  appInfo: AppInfo;
  networkInfo?: NetworkInfo;
}

interface DeviceInfo {
  platform: string;
  version: string;
  model?: string;
  screenSize: { width: number; height: number };
  locale: string;
  timezone: string;
}

interface AppInfo {
  version: string;
  buildNumber: string;
  environment: 'development' | 'staging' | 'production';
  bundleId: string;
}

interface NetworkInfo {
  type: string;
  isConnected: boolean;
  isInternetReachable: boolean;
}

// 默认配置
const defaultConfig: LoggingConfig = {
  logLevel: __DEV__ ? LogLevel.DEBUG : LogLevel.INFO,
  enableConsoleLogging: __DEV__,
  enableFileLogging: true,
  enableRemoteLogging: !__DEV__,
  enablePerformanceLogging: true,
  
  maxLogFiles: 10,
  maxLogFileSize: 5 * 1024 * 1024, // 5MB
  logRetentionDays: 7,
  
  batchSize: 50,
  uploadInterval: 30000, // 30秒
  
  sensitiveFields: ['password', 'token', 'apiKey', 'secret', 'email', 'phone'],
  excludePatterns: [/password/i, /token/i, /secret/i],
  
  enableStackTrace: true,
  enableSourceMap: __DEV__,
  maxStackDepth: 10,
};

// ============================================================================
// 高级日志系统类
// ============================================================================

export class AdvancedLoggingSystem {
  private config: LoggingConfig;
  private sessionId: string;
  private logBuffer: LogEntry[] = [];
  private uploadTimer?: NodeJS.Timeout;
  private isUploading: boolean = false;
  
  private deviceInfo: DeviceInfo;
  private appInfo: AppInfo;
  private networkInfo?: NetworkInfo;

  constructor(config: Partial<LoggingConfig> = {}) {
    this.config = { ...defaultConfig, ...config };
    this.sessionId = this.generateSessionId();
    
    this.initializeDeviceInfo();
    this.initializeAppInfo();
    this.setupUploadTimer();
  }

  // ============================================================================
  // 初始化方法
  // ============================================================================

  private initializeDeviceInfo(): void {
    this.deviceInfo = {
      platform: Platform.OS,
      version: Platform.Version.toString(),
      screenSize: { width: 0, height: 0 }, // 需要从Dimensions获取
      locale: 'zh-CN', // 需要从I18nManager获取
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    };
  }

  private initializeAppInfo(): void {
    this.appInfo = {
      version: '4.0.0', // 从package.json获取
      buildNumber: '1', // 从原生代码获取
      environment: __DEV__ ? 'development' : 'production',
      bundleId: 'com.trekmate.app',
    };
  }

  private setupUploadTimer(): void {
    if (this.config.enableRemoteLogging) {
      this.uploadTimer = setInterval(() => {
        this.uploadLogs();
      }, this.config.uploadInterval);
    }
  }

  // ============================================================================
  // 日志记录方法
  // ============================================================================

  /**
   * 记录跟踪日志
   */
  trace(message: string, data?: any, context?: Partial<LogContext>): void {
    this.log(LogLevel.TRACE, 'TRACE', message, data, context);
  }

  /**
   * 记录调试日志
   */
  debug(message: string, data?: any, context?: Partial<LogContext>): void {
    this.log(LogLevel.DEBUG, 'DEBUG', message, data, context);
  }

  /**
   * 记录信息日志
   */
  info(message: string, data?: any, context?: Partial<LogContext>): void {
    this.log(LogLevel.INFO, 'INFO', message, data, context);
  }

  /**
   * 记录警告日志
   */
  warn(message: string, data?: any, context?: Partial<LogContext>): void {
    this.log(LogLevel.WARN, 'WARN', message, data, context);
  }

  /**
   * 记录错误日志
   */
  error(message: string, error?: Error, data?: any, context?: Partial<LogContext>): void {
    const errorData = error ? {
      name: error.name,
      message: error.message,
      stack: error.stack,
      ...data,
    } : data;

    this.log(LogLevel.ERROR, 'ERROR', message, errorData, context);
  }

  /**
   * 记录致命错误日志
   */
  fatal(message: string, error?: Error, data?: any, context?: Partial<LogContext>): void {
    const errorData = error ? {
      name: error.name,
      message: error.message,
      stack: error.stack,
      ...data,
    } : data;

    this.log(LogLevel.FATAL, 'FATAL', message, errorData, context);
  }

  /**
   * 记录性能日志
   */
  performance(operation: string, duration: number, data?: any, context?: Partial<LogContext>): void {
    if (!this.config.enablePerformanceLogging) return;

    const performanceData = {
      operation,
      duration,
      timestamp: Date.now(),
      ...data,
    };

    this.log(LogLevel.INFO, 'PERFORMANCE', `${operation} completed in ${duration}ms`, performanceData, context);
  }

  /**
   * 记录用户行为日志
   */
  userAction(action: string, data?: any, context?: Partial<LogContext>): void {
    const actionData = {
      action,
      timestamp: Date.now(),
      ...data,
    };

    this.log(LogLevel.INFO, 'USER_ACTION', `User action: ${action}`, actionData, {
      ...context,
      userAction: action,
    });
  }

  /**
   * 记录网络请求日志
   */
  networkRequest(
    method: string,
    url: string,
    status: number,
    duration: number,
    data?: any,
    context?: Partial<LogContext>
  ): void {
    const networkData = {
      method,
      url: this.sanitizeUrl(url),
      status,
      duration,
      timestamp: Date.now(),
      ...data,
    };

    const level = status >= 400 ? LogLevel.ERROR : LogLevel.INFO;
    const category = status >= 400 ? 'NETWORK_ERROR' : 'NETWORK';
    
    this.log(level, category, `${method} ${url} - ${status} (${duration}ms)`, networkData, context);
  }

  // ============================================================================
  // 核心日志方法
  // ============================================================================

  private log(
    level: LogLevel,
    category: string,
    message: string,
    data?: any,
    context?: Partial<LogContext>
  ): void {
    // 检查日志级别
    if (level < this.config.logLevel) return;

    // 创建日志条目
    const logEntry: LogEntry = {
      id: this.generateLogId(),
      timestamp: Date.now(),
      level,
      category,
      message,
      data: this.sanitizeData(data),
      stack: this.config.enableStackTrace ? this.getStackTrace() : undefined,
      context: this.buildContext(context),
      tags: this.extractTags(message, data),
      sessionId: this.sessionId,
    };

    // 输出到控制台
    if (this.config.enableConsoleLogging) {
      this.logToConsole(logEntry);
    }

    // 添加到缓冲区
    this.logBuffer.push(logEntry);

    // 文件日志
    if (this.config.enableFileLogging) {
      this.logToFile(logEntry);
    }

    // 检查缓冲区大小
    if (this.logBuffer.length >= this.config.batchSize) {
      this.uploadLogs();
    }
  }

  /**
   * 输出到控制台
   */
  private logToConsole(logEntry: LogEntry): void {
    const { level, category, message, data } = logEntry;
    const timestamp = new Date(logEntry.timestamp).toISOString();
    const logMessage = `[${timestamp}] [${category}] ${message}`;

    switch (level) {
      case LogLevel.TRACE:
      case LogLevel.DEBUG:
        console.debug(logMessage, data);
        break;
      case LogLevel.INFO:
        console.info(logMessage, data);
        break;
      case LogLevel.WARN:
        console.warn(logMessage, data);
        break;
      case LogLevel.ERROR:
      case LogLevel.FATAL:
        console.error(logMessage, data);
        break;
    }
  }

  /**
   * 记录到文件
   */
  private async logToFile(logEntry: LogEntry): Promise<void> {
    try {
      const logKey = `log_${this.getDateString()}_${this.sessionId}`;
      const existingLogs = await AsyncStorage.getItem(logKey);
      const logs = existingLogs ? JSON.parse(existingLogs) : [];
      
      logs.push(logEntry);
      
      // 检查文件大小
      const logString = JSON.stringify(logs);
      if (logString.length > this.config.maxLogFileSize) {
        // 创建新文件
        await this.rotateLogFile();
      } else {
        await AsyncStorage.setItem(logKey, logString);
      }
    } catch (error) {
      console.error('Failed to write log to file:', error);
    }
  }

  /**
   * 上传日志
   */
  private async uploadLogs(): Promise<void> {
    if (this.isUploading || this.logBuffer.length === 0 || !this.config.enableRemoteLogging) {
      return;
    }

    this.isUploading = true;

    try {
      const logsToUpload = [...this.logBuffer];
      this.logBuffer = [];

      if (this.config.remoteEndpoint) {
        await this.sendLogsToRemote(logsToUpload);
      }
    } catch (error) {
      console.error('Failed to upload logs:', error);
      // 重新添加到缓冲区
      this.logBuffer.unshift(...this.logBuffer);
    } finally {
      this.isUploading = false;
    }
  }

  /**
   * 发送日志到远程服务器
   */
  private async sendLogsToRemote(logs: LogEntry[]): Promise<void> {
    try {
      const response = await fetch(this.config.remoteEndpoint!, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sessionId: this.sessionId,
          logs,
          metadata: {
            deviceInfo: this.deviceInfo,
            appInfo: this.appInfo,
            networkInfo: this.networkInfo,
          },
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (error) {
      console.error('Failed to send logs to remote:', error);
      throw error;
    }
  }

  // ============================================================================
  // 工具方法
  // ============================================================================

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateLogId(): string {
    return `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private getDateString(): string {
    return new Date().toISOString().split('T')[0];
  }

  private getStackTrace(): string {
    const stack = new Error().stack;
    if (!stack) return '';

    const lines = stack.split('\n');
    return lines.slice(3, 3 + this.config.maxStackDepth).join('\n');
  }

  private buildContext(context?: Partial<LogContext>): LogContext {
    return {
      deviceInfo: this.deviceInfo,
      appInfo: this.appInfo,
      networkInfo: this.networkInfo,
      ...context,
    };
  }

  private extractTags(message: string, data?: any): string[] {
    const tags: string[] = [];
    
    // 从消息中提取标签
    const tagMatches = message.match(/#\w+/g);
    if (tagMatches) {
      tags.push(...tagMatches.map(tag => tag.substring(1)));
    }

    // 从数据中提取标签
    if (data && typeof data === 'object') {
      if (data.tags && Array.isArray(data.tags)) {
        tags.push(...data.tags);
      }
    }

    return [...new Set(tags)]; // 去重
  }

  private sanitizeData(data: any): any {
    if (!data) return data;

    const sanitized = JSON.parse(JSON.stringify(data));
    
    // 移除敏感字段
    this.removeSensitiveFields(sanitized, this.config.sensitiveFields);
    
    return sanitized;
  }

  private removeSensitiveFields(obj: any, sensitiveFields: string[]): void {
    if (typeof obj !== 'object' || obj === null) return;

    for (const key in obj) {
      if (sensitiveFields.some(field => key.toLowerCase().includes(field.toLowerCase()))) {
        obj[key] = '[REDACTED]';
      } else if (typeof obj[key] === 'object') {
        this.removeSensitiveFields(obj[key], sensitiveFields);
      }
    }
  }

  private sanitizeUrl(url: string): string {
    try {
      const urlObj = new URL(url);
      
      // 移除敏感查询参数
      this.config.sensitiveFields.forEach(field => {
        if (urlObj.searchParams.has(field)) {
          urlObj.searchParams.set(field, '[REDACTED]');
        }
      });

      return urlObj.toString();
    } catch {
      return url;
    }
  }

  private async rotateLogFile(): Promise<void> {
    try {
      // 获取所有日志文件
      const allKeys = await AsyncStorage.getAllKeys();
      const logKeys = allKeys.filter(key => key.startsWith('log_'));
      
      // 按时间排序
      logKeys.sort();
      
      // 删除超过限制的文件
      if (logKeys.length >= this.config.maxLogFiles) {
        const keysToDelete = logKeys.slice(0, logKeys.length - this.config.maxLogFiles + 1);
        await AsyncStorage.multiRemove(keysToDelete);
      }
    } catch (error) {
      console.error('Failed to rotate log files:', error);
    }
  }

  // ============================================================================
  // 公共接口
  // ============================================================================

  /**
   * 获取日志统计
   */
  async getLogStatistics(): Promise<any> {
    try {
      const allKeys = await AsyncStorage.getAllKeys();
      const logKeys = allKeys.filter(key => key.startsWith('log_'));
      
      let totalLogs = 0;
      const logsByLevel: Record<string, number> = {};
      const logsByCategory: Record<string, number> = {};

      for (const key of logKeys) {
        const logData = await AsyncStorage.getItem(key);
        if (logData) {
          const logs: LogEntry[] = JSON.parse(logData);
          totalLogs += logs.length;

          logs.forEach(log => {
            const levelName = LogLevel[log.level];
            logsByLevel[levelName] = (logsByLevel[levelName] || 0) + 1;
            logsByCategory[log.category] = (logsByCategory[log.category] || 0) + 1;
          });
        }
      }

      return {
        totalLogs,
        logsByLevel,
        logsByCategory,
        sessionId: this.sessionId,
        bufferSize: this.logBuffer.length,
      };
    } catch (error) {
      console.error('Failed to get log statistics:', error);
      return null;
    }
  }

  /**
   * 清理过期日志
   */
  async cleanupOldLogs(): Promise<void> {
    try {
      const allKeys = await AsyncStorage.getAllKeys();
      const logKeys = allKeys.filter(key => key.startsWith('log_'));
      
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - this.config.logRetentionDays);
      
      const keysToDelete = logKeys.filter(key => {
        const dateStr = key.split('_')[1];
        const logDate = new Date(dateStr);
        return logDate < cutoffDate;
      });

      if (keysToDelete.length > 0) {
        await AsyncStorage.multiRemove(keysToDelete);
        console.log(`Cleaned up ${keysToDelete.length} old log files`);
      }
    } catch (error) {
      console.error('Failed to cleanup old logs:', error);
    }
  }

  /**
   * 强制上传日志
   */
  async forceUploadLogs(): Promise<void> {
    await this.uploadLogs();
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<LoggingConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    // 重新设置上传定时器
    if (this.uploadTimer) {
      clearInterval(this.uploadTimer);
    }
    this.setupUploadTimer();
  }

  /**
   * 销毁日志系统
   */
  destroy(): void {
    if (this.uploadTimer) {
      clearInterval(this.uploadTimer);
    }
    
    // 上传剩余日志
    this.uploadLogs();
  }
}

// 导出单例实例
export const advancedLoggingSystem = new AdvancedLoggingSystem();

// ============================================================================
// 增强错误处理系统
// ============================================================================

interface ErrorHandlingConfig {
  enableAutoRecovery: boolean;
  enableUserNotification: boolean;
  enableErrorReporting: boolean;
  maxRetryAttempts: number;
  retryDelay: number;

  // 错误分类配置
  errorClassification: {
    network: { retryable: boolean; userVisible: boolean };
    validation: { retryable: boolean; userVisible: boolean };
    permission: { retryable: boolean; userVisible: boolean };
    runtime: { retryable: boolean; userVisible: boolean };
  };

  // 恢复策略
  recoveryStrategies: {
    fallbackToCache: boolean;
    fallbackToOffline: boolean;
    gracefulDegradation: boolean;
  };
}

interface ErrorMetadata {
  id: string;
  type: ErrorType;
  severity: ErrorSeverity;
  message: string;
  stack?: string;
  context: ErrorContext;
  timestamp: number;
  sessionId: string;
  retryCount: number;
  resolved: boolean;
  userNotified: boolean;
}

enum ErrorType {
  NETWORK = 'network',
  VALIDATION = 'validation',
  PERMISSION = 'permission',
  RUNTIME = 'runtime',
  TIMEOUT = 'timeout',
  UNKNOWN = 'unknown',
}

enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

interface ErrorContext {
  screen?: string;
  component?: string;
  userAction?: string;
  additionalData?: any;
}

interface RecoveryStrategy {
  type: 'retry' | 'fallback' | 'ignore' | 'redirect';
  maxAttempts?: number;
  delay?: number;
  fallbackAction?: () => Promise<any>;
  redirectTo?: string;
}

export class EnhancedErrorHandler {
  private config: ErrorHandlingConfig;
  private logger: AdvancedLoggingSystem;
  private errorHistory: ErrorMetadata[] = [];
  private retryCounters: Map<string, number> = new Map();
  private recoveryStrategies: Map<ErrorType, RecoveryStrategy> = new Map();

  constructor(logger: AdvancedLoggingSystem, config: Partial<ErrorHandlingConfig> = {}) {
    this.logger = logger;
    this.config = {
      enableAutoRecovery: true,
      enableUserNotification: true,
      enableErrorReporting: true,
      maxRetryAttempts: 3,
      retryDelay: 1000,

      errorClassification: {
        network: { retryable: true, userVisible: true },
        validation: { retryable: false, userVisible: true },
        permission: { retryable: false, userVisible: true },
        runtime: { retryable: false, userVisible: false },
      },

      recoveryStrategies: {
        fallbackToCache: true,
        fallbackToOffline: true,
        gracefulDegradation: true,
      },

      ...config,
    };

    this.initializeRecoveryStrategies();
    this.setupGlobalErrorHandlers();
  }

  /**
   * 初始化恢复策略
   */
  private initializeRecoveryStrategies(): void {
    // 网络错误恢复策略
    this.recoveryStrategies.set(ErrorType.NETWORK, {
      type: 'retry',
      maxAttempts: 3,
      delay: 2000,
      fallbackAction: async () => {
        // 尝试从缓存获取数据
        return this.fallbackToCache();
      },
    });

    // 权限错误恢复策略
    this.recoveryStrategies.set(ErrorType.PERMISSION, {
      type: 'redirect',
      redirectTo: 'PermissionSettings',
    });

    // 验证错误恢复策略
    this.recoveryStrategies.set(ErrorType.VALIDATION, {
      type: 'ignore', // 通常由UI处理
    });

    // 运行时错误恢复策略
    this.recoveryStrategies.set(ErrorType.RUNTIME, {
      type: 'fallback',
      fallbackAction: async () => {
        // 重置到安全状态
        return this.resetToSafeState();
      },
    });
  }

  /**
   * 设置全局错误处理器
   */
  private setupGlobalErrorHandlers(): void {
    // 处理未捕获的Promise rejection
    if (typeof window !== 'undefined' && window.addEventListener) {
      window.addEventListener('unhandledrejection', (event) => {
        this.handleError(event.reason, {
          userAction: 'Promise Rejection',
        });
      });
    } else {
      // React Native环境下的错误处理
      console.log('🔧 [RN] 在React Native环境中设置错误处理');
    }

    // 处理React错误边界
    const originalConsoleError = console.error;
    console.error = (...args) => {
      const error = args[0];
      if (error instanceof Error && error.stack?.includes('React')) {
        this.handleError(error, {
          component: 'React Component',
        });
      }
      originalConsoleError.apply(console, args);
    };
  }

  /**
   * 处理错误的主要方法
   */
  async handleError(
    error: Error | string,
    context: Partial<ErrorContext> = {}
  ): Promise<any> {
    try {
      // 创建错误元数据
      const errorMetadata = this.createErrorMetadata(error, context);

      // 记录错误
      this.logger.error(
        `Error handled: ${errorMetadata.message}`,
        error instanceof Error ? error : undefined,
        {
          errorId: errorMetadata.id,
          type: errorMetadata.type,
          severity: errorMetadata.severity,
        },
        context
      );

      // 添加到错误历史
      this.errorHistory.push(errorMetadata);

      // 尝试自动恢复
      if (this.config.enableAutoRecovery) {
        const recoveryResult = await this.attemptRecovery(errorMetadata);
        if (recoveryResult.success) {
          errorMetadata.resolved = true;
          this.logger.info(`Error auto-recovered: ${errorMetadata.id}`);
          return recoveryResult.data;
        }
      }

      // 通知用户
      if (this.config.enableUserNotification && this.shouldNotifyUser(errorMetadata)) {
        await this.notifyUser(errorMetadata);
      }

      // 报告错误
      if (this.config.enableErrorReporting) {
        await this.reportError(errorMetadata);
      }

      return null;
    } catch (handlingError) {
      this.logger.fatal('Error handling failed', handlingError);
      throw handlingError;
    }
  }

  /**
   * 创建错误元数据
   */
  private createErrorMetadata(
    error: Error | string,
    context: Partial<ErrorContext>
  ): ErrorMetadata {
    const message = error instanceof Error ? error.message : error;
    const stack = error instanceof Error ? error.stack : undefined;

    return {
      id: this.generateErrorId(),
      type: this.classifyError(message),
      severity: this.determineSeverity(message, context),
      message,
      stack,
      context: context as ErrorContext,
      timestamp: Date.now(),
      sessionId: this.logger['sessionId'], // 访问logger的sessionId
      retryCount: 0,
      resolved: false,
      userNotified: false,
    };
  }

  /**
   * 分类错误类型
   */
  private classifyError(message: string): ErrorType {
    const lowerMessage = message.toLowerCase();

    if (lowerMessage.includes('network') || lowerMessage.includes('fetch') || lowerMessage.includes('timeout')) {
      return ErrorType.NETWORK;
    }

    if (lowerMessage.includes('permission') || lowerMessage.includes('unauthorized')) {
      return ErrorType.PERMISSION;
    }

    if (lowerMessage.includes('validation') || lowerMessage.includes('invalid')) {
      return ErrorType.VALIDATION;
    }

    if (lowerMessage.includes('timeout')) {
      return ErrorType.TIMEOUT;
    }

    return ErrorType.RUNTIME;
  }

  /**
   * 确定错误严重程度
   */
  private determineSeverity(message: string, context: Partial<ErrorContext>): ErrorSeverity {
    const lowerMessage = message.toLowerCase();

    if (lowerMessage.includes('critical') || lowerMessage.includes('fatal')) {
      return ErrorSeverity.CRITICAL;
    }

    if (lowerMessage.includes('permission') || lowerMessage.includes('unauthorized')) {
      return ErrorSeverity.HIGH;
    }

    if (lowerMessage.includes('network') || lowerMessage.includes('timeout')) {
      return ErrorSeverity.MEDIUM;
    }

    return ErrorSeverity.LOW;
  }

  /**
   * 尝试恢复
   */
  private async attemptRecovery(errorMetadata: ErrorMetadata): Promise<{ success: boolean; data?: any }> {
    const strategy = this.recoveryStrategies.get(errorMetadata.type);
    if (!strategy) {
      return { success: false };
    }

    try {
      switch (strategy.type) {
        case 'retry':
          return await this.retryOperation(errorMetadata, strategy);
        case 'fallback':
          return await this.fallbackOperation(errorMetadata, strategy);
        case 'redirect':
          return await this.redirectOperation(errorMetadata, strategy);
        case 'ignore':
          return { success: true };
        default:
          return { success: false };
      }
    } catch (recoveryError) {
      this.logger.error('Recovery attempt failed', recoveryError);
      return { success: false };
    }
  }

  /**
   * 重试操作
   */
  private async retryOperation(
    errorMetadata: ErrorMetadata,
    strategy: RecoveryStrategy
  ): Promise<{ success: boolean; data?: any }> {
    const maxAttempts = strategy.maxAttempts || this.config.maxRetryAttempts;
    const delay = strategy.delay || this.config.retryDelay;

    if (errorMetadata.retryCount >= maxAttempts) {
      return { success: false };
    }

    // 等待重试延迟
    await new Promise(resolve => setTimeout(resolve, delay));

    errorMetadata.retryCount++;

    // 这里需要重新执行原始操作
    // 实际实现中需要保存原始操作的引用
    return { success: true };
  }

  /**
   * 降级操作
   */
  private async fallbackOperation(
    errorMetadata: ErrorMetadata,
    strategy: RecoveryStrategy
  ): Promise<{ success: boolean; data?: any }> {
    if (strategy.fallbackAction) {
      const result = await strategy.fallbackAction();
      return { success: true, data: result };
    }

    return { success: false };
  }

  /**
   * 重定向操作
   */
  private async redirectOperation(
    errorMetadata: ErrorMetadata,
    strategy: RecoveryStrategy
  ): Promise<{ success: boolean; data?: any }> {
    if (strategy.redirectTo) {
      // 这里需要导航到指定页面
      // 实际实现中需要注入导航服务
      this.logger.info(`Redirecting to ${strategy.redirectTo} due to error`);
      return { success: true };
    }

    return { success: false };
  }

  /**
   * 判断是否应该通知用户
   */
  private shouldNotifyUser(errorMetadata: ErrorMetadata): boolean {
    const classification = this.config.errorClassification[errorMetadata.type];
    return classification?.userVisible && errorMetadata.severity !== ErrorSeverity.LOW;
  }

  /**
   * 通知用户
   */
  private async notifyUser(errorMetadata: ErrorMetadata): Promise<void> {
    // 实际实现中需要显示用户友好的错误消息
    // 可以使用Toast、Alert或自定义错误组件
    this.logger.info(`User notified of error: ${errorMetadata.id}`);
    errorMetadata.userNotified = true;
  }

  /**
   * 报告错误
   */
  private async reportError(errorMetadata: ErrorMetadata): Promise<void> {
    // 发送到错误监控服务（如Sentry、Bugsnag等）
    this.logger.info(`Error reported: ${errorMetadata.id}`);
  }

  /**
   * 回退到缓存
   */
  private async fallbackToCache(): Promise<any> {
    // 实现缓存回退逻辑
    this.logger.info('Falling back to cache data');
    return null;
  }

  /**
   * 重置到安全状态
   */
  private async resetToSafeState(): Promise<any> {
    // 实现安全状态重置逻辑
    this.logger.info('Resetting to safe state');
    return null;
  }

  /**
   * 生成错误ID
   */
  private generateErrorId(): string {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // ============================================================================
  // 公共接口
  // ============================================================================

  /**
   * 获取错误统计
   */
  getErrorStatistics(): any {
    const totalErrors = this.errorHistory.length;
    const errorsByType: Record<string, number> = {};
    const errorsBySeverity: Record<string, number> = {};
    const resolvedErrors = this.errorHistory.filter(e => e.resolved).length;

    this.errorHistory.forEach(error => {
      errorsByType[error.type] = (errorsByType[error.type] || 0) + 1;
      errorsBySeverity[error.severity] = (errorsBySeverity[error.severity] || 0) + 1;
    });

    return {
      totalErrors,
      resolvedErrors,
      resolutionRate: totalErrors > 0 ? (resolvedErrors / totalErrors) * 100 : 0,
      errorsByType,
      errorsBySeverity,
    };
  }

  /**
   * 获取最近的错误
   */
  getRecentErrors(limit: number = 10): ErrorMetadata[] {
    return this.errorHistory
      .sort((a, b) => b.timestamp - a.timestamp)
      .slice(0, limit);
  }

  /**
   * 清理错误历史
   */
  clearErrorHistory(): void {
    this.errorHistory = [];
    this.retryCounters.clear();
  }
}

// 导出增强错误处理器实例
export const enhancedErrorHandler = new EnhancedErrorHandler(advancedLoggingSystem);
